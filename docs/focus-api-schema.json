{"openapi": "3.0.0", "info": {"title": "Focus NFSe API", "description": "API for issuing, checking, canceling and sending electronic service invoices (NFSe) through the Focus API.", "version": "1.0.0"}, "servers": [{"url": "https://homologacao.focusnfe.com.br", "description": "Sandbox/Testing environment"}, {"url": "https://api.focusnfe.com.br", "description": "Production environment"}], "components": {"securitySchemes": {"tokenAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "name": "token"}}, "schemas": {"NfseEnvioRequest": {"type": "object", "required": ["data_emissao", "prestador", "<PERSON><PERSON><PERSON>", "servico"], "properties": {"data_emissao": {"type": "string", "format": "date-time", "description": "Date and time of NFSe issuance in ISO format"}, "prestador": {"$ref": "#/components/schemas/Prestador"}, "tomador": {"$ref": "#/components/schemas/Tomador"}, "servico": {"$ref": "#/components/schemas/Ser<PERSON>o"}, "natureza_operacao": {"type": "integer", "description": "Nature of the operation (1-6)", "enum": [1, 2, 3, 4, 5, 6]}, "optante_simples_nacional": {"type": "boolean", "description": "Indicates if the service provider is under Simples Nacional tax regime"}, "incentivador_cultural": {"type": "boolean", "description": "Indicates if the service provider is incentivized by cultural incentives"}, "numero_rps": {"type": "string", "description": "RPS (Recibo Provisório de Serviço) serial number"}, "serie_rps": {"type": "string", "description": "RPS series"}, "tipo_rps": {"type": "integer", "description": "RPS type: 1=RPS, 2=Nota Fiscal Conjugada, 3=Cupom", "enum": [1, 2, 3]}, "competencia": {"type": "string", "format": "date", "description": "Competency period (Month/Year) in which the service was provided"}, "regime_especial_tributacao": {"type": "integer", "description": "Special taxation regime", "enum": [1, 2, 3, 4, 5, 6]}}}, "Prestador": {"type": "object", "required": ["cnpj", "inscricao_municipal", "codigo_municipio"], "properties": {"cnpj": {"type": "string", "description": "CNPJ of the service provider"}, "inscricao_municipal": {"type": "string", "description": "Municipal registration number"}, "codigo_municipio": {"type": "string", "description": "Municipality code (IBGE 7-digit code)"}}}, "Tomador": {"type": "object", "properties": {"cnpj": {"type": "string", "description": "CNPJ of the service recipient (required if CPF not provided)"}, "cpf": {"type": "string", "description": "CPF of the service recipient (required if CNPJ not provided)"}, "razao_social": {"type": "string", "description": "Legal name of the service recipient"}, "email": {"type": "string", "format": "email", "description": "Email for sending NFSe copy"}, "endereco": {"$ref": "#/components/schemas/Endereco"}, "telefone": {"type": "string", "description": "Recipient's phone number"}}}, "Endereco": {"type": "object", "properties": {"logradouro": {"type": "string", "description": "Street name"}, "numero": {"type": "string", "description": "Street number"}, "complemento": {"type": "string", "description": "Additional address info"}, "bairro": {"type": "string", "description": "Neighborhood"}, "codigo_municipio": {"type": "string", "description": "Municipality code (IBGE 7-digit code)"}, "uf": {"type": "string", "description": "State abbreviation"}, "cep": {"type": "string", "description": "ZIP/Postal code"}}}, "Servico": {"type": "object", "required": ["valor_servicos", "iss_retido", "item_lista_servico", "discriminacao"], "properties": {"valor_servicos": {"type": "number", "format": "double", "description": "Value of the service"}, "iss_retido": {"type": "boolean", "description": "Whether ISS tax was withheld by the recipient"}, "item_lista_servico": {"type": "string", "description": "Service code according to LC 116/2003"}, "discriminacao": {"type": "string", "description": "Service description"}, "codigo_municipio": {"type": "string", "description": "Municipality code where service was provided"}, "codigo_tributario_municipio": {"type": "string", "description": "Municipal tax code (specific to each municipality)"}, "aliquota": {"type": "number", "format": "double", "description": "ISS tax rate (e.g., 3.0 for 3%)"}, "codigo_cnae": {"type": "string", "description": "CNAE code"}, "valor_deducoes": {"type": "number", "format": "double", "description": "Value of deductions"}, "valor_inss": {"type": "number", "format": "double", "description": "Value of social security contribution (INSS)"}, "valor_ir": {"type": "number", "format": "double", "description": "Value of income tax (IR)"}, "valor_cofins": {"type": "number", "format": "double", "description": "Value of Contribution on Gross Revenue (COFINS)"}, "valor_csll": {"type": "number", "format": "double", "description": "Value of contribution to CSLL"}, "valor_pis": {"type": "number", "format": "double", "description": "Value of PIS/PASEP contribution"}, "valor_iss": {"type": "number", "format": "double", "description": "Value of ISS tax"}, "valor_iss_retido": {"type": "number", "format": "double", "description": "Value of ISS tax withheld"}, "outras_retencoes": {"type": "number", "format": "double", "description": "Other withholdings"}, "desconto_incondicionado": {"type": "number", "format": "double", "description": "Discount value (unconditional)"}, "desconto_condicionado": {"type": "number", "format": "double", "description": "Discount value (conditional)"}, "valor_liquido": {"type": "number", "format": "double", "description": "Net amount due"}}}, "NfseEnvioResponse": {"type": "object", "properties": {"cnpj_prestador": {"type": "string", "description": "CNPJ of the service provider"}, "ref": {"type": "string", "description": "Reference identifier provided in the request"}, "status": {"type": "string", "description": "Current status of the NFSe", "enum": ["processando_autorizacao"]}}}, "NfseStatusResponse": {"type": "object", "properties": {"cnpj_prestador": {"type": "string", "description": "CNPJ of the service provider"}, "ref": {"type": "string", "description": "Reference identifier provided in the creation request"}, "numero_rps": {"type": "string", "description": "RPS (Recibo Provisório de Serviço) number"}, "serie_rps": {"type": "string", "description": "RPS series"}, "status": {"type": "string", "description": "Current status of the NFSe", "enum": ["processando_autorizacao", "autorizado", "erro_autorizacao", "cancelado", "erro_cancelamento", "substituido"]}, "numero": {"type": "string", "description": "NFSe number (only available if authorized)"}, "codigo_verificacao": {"type": "string", "description": "Verification code (only available if authorized)"}, "data_emissao": {"type": "string", "format": "date-time", "description": "Issue date and time (only available if authorized)"}, "url": {"type": "string", "format": "uri", "description": "URL to view the NFSe (only available if authorized)"}, "caminho_xml_nota_fiscal": {"type": "string", "description": "Path to the NFSe XML file (only available if authorized)"}, "caminho_xml_cancelamento": {"type": "string", "description": "Path to the cancellation XML file (only if canceled)"}, "erros": {"type": "array", "description": "List of errors (only if status is erro_autorizacao or erro_cancelamento)", "items": {"$ref": "#/components/schemas/Erro"}}}}, "NfseCancelamentoRequest": {"type": "object", "required": ["justificativa"], "properties": {"justificativa": {"type": "string", "description": "Justification for the cancellation"}}}, "NfseCancelamentoResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "Current status of the NFSe", "enum": ["cancelado", "erro_cancelamento"]}, "erros": {"type": "array", "description": "List of errors (only if status is erro_cancelamento)", "items": {"$ref": "#/components/schemas/Erro"}}}}, "NfseEmailRequest": {"type": "object", "required": ["emails"], "properties": {"emails": {"type": "array", "description": "List of email addresses to send the NFSe to", "items": {"type": "string", "format": "email"}}}}, "Erro": {"type": "object", "properties": {"codigo": {"type": "string", "description": "Error code specific to this error"}, "mensagem": {"type": "string", "description": "Detailed error message"}, "correcao": {"type": "string", "description": "Suggested correction to fix the error"}}}, "Error": {"type": "object", "properties": {"codigo": {"type": "string", "description": "Error code identifying the type of error"}, "mensagem": {"type": "string", "description": "Human-readable error message"}, "erros": {"type": "array", "description": "List of detailed error information", "items": {"$ref": "#/components/schemas/Erro"}}}}}}, "security": [{"tokenAuth": []}], "paths": {"/v2/nfse": {"post": {"summary": "Create a new NFSe", "description": "Creates and submits an invoice for processing. The process is asynchronous, and the invoice is queued for processing.", "parameters": [{"name": "ref", "in": "query", "description": "Unique reference identifier for the invoice", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NfseEnvioRequest"}}}}, "responses": {"200": {"description": "Successfully submitted for processing", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NfseEnvioResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/v2/nfse/{reference}": {"get": {"summary": "Get NFSe status", "description": "Queries the status of a previously submitted invoice", "parameters": [{"name": "reference", "in": "path", "description": "Unique reference identifier for the invoice", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved NFSe status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NfseStatusResponse"}}}}, "404": {"description": "NFSe not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"summary": "Cancel an NFSe", "description": "Cancels a previously authorized invoice. The process is synchronous, with an immediate response.", "parameters": [{"name": "reference", "in": "path", "description": "Unique reference identifier for the invoice", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NfseCancelamentoRequest"}}}}, "responses": {"200": {"description": "Successfully canceled or cancellation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NfseCancelamentoResponse"}}}}, "404": {"description": "NFSe not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/v2/nfse/{reference}/email": {"post": {"summary": "Send NFSe via email", "description": "Sends an email with a copy of the invoice to specified recipients", "parameters": [{"name": "reference", "in": "path", "description": "Unique reference identifier for the invoice", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NfseEmailRequest"}}}}, "responses": {"200": {"description": "<PERSON>ail sent successfully", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"description": "NFSe not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}}