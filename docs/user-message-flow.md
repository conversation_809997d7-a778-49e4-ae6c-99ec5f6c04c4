# User Message Flow Documentation

This document describes the complete flow from when a Meta webhook arrives until a response message is sent back to the user.

## Overview

The system follows a clean architecture pattern with clear separation between infrastructure (Meta-specific), domain (business logic), and application layers. The flow is designed to be provider-agnostic, allowing easy integration with other messaging platforms.

## Architecture Diagram

```mermaid
graph TB
    subgraph "External"
        WA[WhatsApp User]
        META[Meta WhatsApp Business API]
    end
    
    subgraph "Infrastructure Layer"
        WH[MetaWebhookController]
        UC[ProcessMetaWebhookUseCaseImpl]
        SQS1[SQS: webhook-meta-v1-new-message-queue]
        CONS1[MetaWebhookQueueConsumer]
        FACT[MetaWebhookProcessorFactory]
        PROC[UserMessageMetaWebhookProcessor]
        CONV[MetaChatMessageEventConverter]
        SQS2[SQS: chat-message-received-queue]
    end
    
    subgraph "Domain Layer"
        CONS2[ChatMessageConsumer]
        CME[ChatMessageEvent]
    end
    
    subgraph "Application Layer"
        CS[ChatService]
        CMS[ChatMessageService]
        CCMS[ContextChatMessageService]
        LLM[LlmService]
        ASS[LlmAssistant]
        MMS[MessageManagerService]
    end
    
    subgraph "Database"
        DB[(PostgreSQL)]
    end
    
    WA -->|sends message| META
    META -->|webhook| WH
    WH --> UC
    UC -->|publishes| SQS1
    SQS1 --> CONS1
    CONS1 --> FACT
    FACT --> PROC
    PROC --> CONV
    CONV -->|ChatMessageEvent| PROC
    PROC -->|publishes| SQS2
    SQS2 --> CONS2
    CONS2 --> CS
    CONS2 --> CMS
    CONS2 --> CCMS
    CONS2 --> LLM
    LLM --> ASS
    CONS2 --> MMS
    CS --> DB
    CMS --> DB
    CCMS --> DB
    MMS -->|response| META
    META -->|delivers| WA
```

## Detailed Flow

### 1. Webhook Reception
```mermaid
sequenceDiagram
    participant User as WhatsApp User
    participant Meta as Meta API
    participant Controller as MetaWebhookController
    participant UseCase as ProcessMetaWebhookUseCaseImpl
    participant SQS as SQS Queue
    
    User->>Meta: Sends message
    Meta->>Controller: POST /webhook (MetaWebhookPayload)
    Controller->>UseCase: execute(payload)
    UseCase->>SQS: publish to webhook-meta-v1-new-message-queue
    UseCase-->>Controller: success
    Controller-->>Meta: 200 OK
```

**Components:**
- **MetaWebhookController**: REST endpoint that receives Meta webhooks
- **ProcessMetaWebhookUseCaseImpl**: Use case that publishes ALL Meta webhooks to a single queue
- **SQS Queue**: `webhook-meta-v1-new-message-queue` - stores all Meta webhooks for processing

### 2. Webhook Processing & Routing
```mermaid
sequenceDiagram
    participant SQS1 as webhook-meta-queue
    participant Consumer as MetaWebhookQueueConsumer
    participant Factory as MetaWebhookProcessorFactory
    participant Processor as UserMessageMetaWebhookProcessor
    participant Converter as MetaChatMessageEventConverter
    participant SQS2 as chat-message-queue
    
    SQS1->>Consumer: consume webhook
    Consumer->>Factory: getProcessor(payload)
    Factory->>Processor: canProcess(payload)?
    Processor-->>Factory: true (for user messages)
    Factory-->>Consumer: UserMessageMetaWebhookProcessor
    Consumer->>Processor: process(payload)
    Processor->>Converter: convert(payload)
    Converter-->>Processor: ChatMessageEvent
    Processor->>SQS2: publish ChatMessageEvent
```

**Components:**
- **MetaWebhookQueueConsumer**: Consumes from Meta webhook queue
- **MetaWebhookProcessorFactory**: Routes webhooks to appropriate processors based on type
- **UserMessageMetaWebhookProcessor**: Handles user message webhooks (excludes interactive buttons)
- **MetaChatMessageEventConverter**: Converts Meta-specific payload to domain object
- **ChatMessageEvent**: Domain object representing a chat message (provider-agnostic)

### 3. Domain Processing & Business Logic
```mermaid
sequenceDiagram
    participant SQS as chat-message-queue
    participant Consumer as ChatMessageConsumer
    participant ChatSvc as ChatService
    participant MsgSvc as ChatMessageService
    participant CtxSvc as ContextChatMessageService
    participant LLM as LlmService
    participant Assistant as LlmAssistant
    participant MgrSvc as MessageManagerService
    participant Meta as Meta API
    
    SQS->>Consumer: consume ChatMessageEvent
    Consumer->>ChatSvc: findOrCreateChat()
    ChatSvc-->>Consumer: ChatEntity
    Consumer->>MsgSvc: saveMessage()
    Consumer->>CtxSvc: save user context
    Consumer->>LLM: processUserMessage()
    LLM->>Assistant: process with context
    Assistant-->>LLM: response
    LLM-->>Consumer: LlmResponse
    Consumer->>CtxSvc: save temp context
    Consumer->>MgrSvc: sendTextMessage()
    MgrSvc->>Meta: send response
    Meta-->>Consumer: message ID
```

**Components:**
- **ChatMessageConsumer**: Domain consumer with complete business logic
- **ChatService**: Manages chat entities and user relationships
- **ChatMessageService**: Persists chat messages
- **ContextChatMessageService**: Manages conversation context for LLM
- **LlmService**: Orchestrates LLM processing
- **LlmAssistant**: Specific AI assistant implementation
- **MessageManagerService**: Handles outbound message sending

## Data Flow

### Input: Meta Webhook Payload
```json
{
  "entry": [{
    "id": "phone_number_id",
    "changes": [{
      "field": "messages",
      "value": {
        "messages": [{
          "id": "message_id",
          "from": "user_phone_number",
          "type": "text",
          "text": {
            "body": "User message content"
          },
          "timestamp": "1234567890"
        }]
      }
    }]
  }]
}
```

### Intermediate: ChatMessageEvent (Domain Object)
```kotlin
ChatMessageEvent(
    chatUuid = UUID,
    chatId = Long,
    source = MessageSourceEnum.META,
    externalMessageId = "message_id",
    message = "User message content",
    messageAuthorPhoneNumber = "user_phone_number",
    isMessageFromUser = true,
    // ... other fields
)
```

### Output: Response Message
```json
{
  "messaging_product": "whatsapp",
  "to": "user_phone_number",
  "type": "text",
  "text": {
    "body": "AI assistant response"
  }
}
```

## Key Design Principles

### 1. **Provider Decoupling**
- Meta-specific code isolated to `UserMessageMetaWebhookProcessor`
- Business logic works with domain objects (`ChatMessageEvent`)
- Easy to add Discord, Telegram, etc.

### 2. **Clean Architecture**
- **Infrastructure**: Meta webhooks, SQS, database
- **Domain**: ChatMessageEvent, business rules
- **Application**: LLM processing, message management

### 3. **Asynchronous Processing**
- Webhooks immediately queued for reliability
- Non-blocking response to Meta
- Scalable processing with SQS

### 4. **Single Responsibility**
- Each component has one clear purpose
- Easy to test and maintain
- Clear separation of concerns

## Error Handling

### Webhook Level
- Invalid webhooks logged and discarded
- SQS retry mechanism for transient failures
- Dead letter queues for persistent failures

### Processing Level
- Conversion errors logged with payload details
- LLM failures send fallback message to user
- Database errors trigger retry mechanism

### Response Level
- Meta API failures logged and retried
- User receives error message if all retries fail

## Monitoring & Observability

### Key Metrics
- Webhook processing latency
- Queue depth and processing rate
- LLM response time and success rate
- Message delivery success rate

### Logging Points
1. Webhook reception
2. Queue publishing/consumption
3. Domain object conversion
4. LLM processing start/end
5. Response sending success/failure

## Future Enhancements

### Multi-Provider Support
```mermaid
graph LR
    subgraph "Providers"
        META[Meta Webhook]
        DISCORD[Discord Webhook]
        TELEGRAM[Telegram Webhook]
    end
    
    subgraph "Adapters"
        METAP[MetaWebhookProcessor]
        DISCP[DiscordWebhookProcessor]
        TELEP[TelegramWebhookProcessor]
    end
    
    subgraph "Domain"
        CME[ChatMessageEvent]
        QUEUE[chat-message-received-queue]
        CONSUMER[ChatMessageConsumer]
    end
    
    META --> METAP
    DISCORD --> DISCP
    TELEGRAM --> TELEP
    
    METAP --> CME
    DISCP --> CME
    TELEP --> CME
    
    CME --> QUEUE
    QUEUE --> CONSUMER
```

### Interactive Messages
- Separate processor for button clicks
- Domain events for interactive responses
- Unified handling in business logic

### Advanced Features
- Message threading support
- Rich media handling
- Multi-language responses
- User preference management
