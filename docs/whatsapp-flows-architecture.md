# WhatsApp Flows - Simple Architecture

## 1. Overview

WhatsApp Flows allows creating structured interactions within WhatsApp. We need a simple, clean way to manage different flows without overcomplicating things.

### Current State
- ✅ Basic crypto and DTOs working
- ✅ Simple action handlers (`ping`, `data_exchange`)
- ✅ Single endpoint `/api/v1/webhooks/meta/flows`

### What We Need
1. **Flow Identification**: Know which flow we're in
2. **Action Routing**: Route actions to the right handler
3. **Simple State**: Basic session data between screens
4. **Clean Organization**: Separate handlers by flow type

## 2. Simple Architecture

### 2.1 Core Idea

```mermaid
graph LR
    Request[FlowRequest] --> Router[FlowRouter]
    Router --> Handler1[OnboardingHandler]
    Router --> Handler2[CompanyHandler]
    Router --> Handler3[CommonHandler]
    Handler1 --> Response[FlowResponse]
    Handler2 --> Response
    Handler3 --> Response
```

### 2.2 Flow Process

```mermaid
sequenceDiagram
    participant WA as WhatsApp
    participant Controller as Controller
    participant Router as FlowRouter
    participant Handler as FlowHandler

    WA->>Controller: POST /flows (encrypted)
    Controller->>Router: route(request)
    Router->>Handler: handle(request)
    Handler-->>Router: FlowResponse
    Router-->>Controller: FlowResponse
    Controller-->>WA: encrypted response
```

## 3. Simple Implementation

### 3.1 Flow Router

```kotlin
@Service
class FlowRouter(
    private val handlers: List<FlowHandler>
) {
    private val handlerMap = handlers.associateBy { it.flowId }

    fun route(request: FlowRequest): FlowResponse {
        // Extract flow ID from request (from flow_token or screen)
        val flowId = extractFlowId(request)

        val handler = handlerMap[flowId]
            ?: throw IllegalArgumentException("Unknown flow: $flowId")

        return handler.handle(request)
    }

    private fun extractFlowId(request: FlowRequest): String {
        // Logic to determine flow ID from request
        return request.flowToken?.split(":")?.first()
            ?: request.screen?.split("_")?.first()?.lowercase()
            ?: "common"
    }
}
```

### 3.2 Flow Handler Interface

```kotlin
interface FlowHandler {
    val flowId: String
    fun handle(request: FlowRequest): FlowResponse
}
```

### 3.3 Simple Session Storage

```kotlin
@Component
class FlowSessionStore {
    private val sessions = ConcurrentHashMap<String, MutableMap<String, Any>>()

    fun get(sessionId: String, key: String): Any? {
        return sessions[sessionId]?.get(key)
    }

    fun put(sessionId: String, key: String, value: Any) {
        sessions.computeIfAbsent(sessionId) { mutableMapOf() }[key] = value
    }

    fun getAll(sessionId: String): Map<String, Any> {
        return sessions[sessionId] ?: emptyMap()
    }

    fun clear(sessionId: String) {
        sessions.remove(sessionId)
    }
}
```

## 4. Simple Directory Structure

```
src/main/kotlin/br/com/mecontaai/api/accounting/
├── application/service/flow/
│   ├── FlowRouter.kt
│   ├── FlowSessionStore.kt
│   └── handlers/
│       ├── OnboardingFlowHandler.kt
│       ├── CompanyFlowHandler.kt
│       └── CommonFlowHandler.kt
├── domain/service/flow/
│   └── FlowHandler.kt (interface)
└── presentation/dto/flows/
    ├── FlowRequest.kt (existing)
    └── FlowResponse.kt (existing)
```

## 5. Example Flow Handlers

### 5.1 Onboarding Flow Handler

```kotlin
@Component
class OnboardingFlowHandler(
    private val userService: UserService,
    private val sessionStore: FlowSessionStore
) : FlowHandler {

    override val flowId = "onboarding"

    override fun handle(request: FlowRequest): FlowResponse {
        return when (request.action.lowercase()) {
            "ping" -> handlePing()
            "data_exchange" -> handleDataExchange(request)
            else -> FlowResponse(
                screen = "ERROR",
                data = mapOf("error" to "Unknown action: ${request.action}")
            )
        }
    }

    private fun handlePing(): FlowResponse {
        return FlowResponse(data = mapOf("status" to "active"))
    }

    private fun handleDataExchange(request: FlowRequest): FlowResponse {
        val data = request.data ?: return errorResponse("No data provided")

        return try {
            when (request.screen) {
                "SIGN_UP" -> handleSignUp(data, request.flowToken)
                else -> errorResponse("Unknown screen: ${request.screen}")
            }
        } catch (e: Exception) {
            errorResponse("Error: ${e.message}")
        }
    }

    private fun handleSignUp(data: Map<String, Any>, flowToken: String?): FlowResponse {
        // Validate required fields
        val email = data["email"]?.toString() ?: return errorResponse("Email required")
        val name = data["full_name"]?.toString() ?: return errorResponse("Name required")

        // Create user
        val user = userService.createUser(name, email, data)

        // Store in session for later use
        flowToken?.let {
            sessionStore.put(it, "user_id", user.id)
            sessionStore.put(it, "user_name", user.name)
        }

        return FlowResponse(
            screen = "SUCCESS",
            data = mapOf(
                "message" to "Conta criada com sucesso!",
                "user_name" to user.name
            )
        )
    }

    private fun errorResponse(message: String) = FlowResponse(
        screen = "ERROR",
        data = mapOf("error" to message)
    )
}
```

### 5.2 Company Flow Handler

```kotlin
@Component
class CompanyFlowHandler(
    private val companyService: CompanyService,
    private val sessionStore: FlowSessionStore
) : FlowHandler {

    override val flowId = "company"

    override fun handle(request: FlowRequest): FlowResponse {
        return when (request.action.lowercase()) {
            "ping" -> FlowResponse(data = mapOf("status" to "active"))
            "data_exchange" -> handleDataExchange(request)
            else -> FlowResponse(
                screen = "ERROR",
                data = mapOf("error" to "Unknown action")
            )
        }
    }

    private fun handleDataExchange(request: FlowRequest): FlowResponse {
        // Handle company-specific actions
        return when (request.screen) {
            "COMPANY_INFO" -> handleCompanyInfo(request)
            else -> FlowResponse(screen = "ERROR", data = mapOf("error" to "Unknown screen"))
        }
    }

    private fun handleCompanyInfo(request: FlowRequest): FlowResponse {
        // Company registration logic
        return FlowResponse(
            screen = "SUCCESS",
            data = mapOf("message" to "Company registered!")
        )
    }
}
```

## 6. Migration Steps

### Step 1: Create the Router
1. Create `FlowRouter` class
2. Create `FlowHandler` interface
3. Update `FlowService` to use the router

### Step 2: Create Flow Handlers
1. Create `OnboardingFlowHandler`
2. Create `CompanyFlowHandler`
3. Move existing logic from current handlers

### Step 3: Add Session Storage
1. Create `FlowSessionStore`
2. Use it in handlers for state between screens

### Step 4: Update Current Service
1. Replace `FlowService` logic with `FlowRouter`
2. Keep existing crypto and DTOs unchanged

## 7. Benefits

1. **Simple**: Easy to understand and maintain
2. **Organized**: Each flow has its own handler
3. **Extensible**: Easy to add new flows
4. **Testable**: Each handler can be tested separately
5. **Clean**: Separates different flow concerns

## 8. How to Use

### 8.1 Update FlowService

```kotlin
@Service
class FlowService(
    private val flowRouter: FlowRouter
) {
    fun processAction(request: FlowRequest): FlowResponse {
        return flowRouter.route(request)
    }
}
```

### 8.2 Add New Flow

To add a new flow, just create a new handler:

```kotlin
@Component
class MyNewFlowHandler : FlowHandler {
    override val flowId = "mynewflow"

    override fun handle(request: FlowRequest): FlowResponse {
        // Your flow logic here
        return FlowResponse(data = mapOf("status" to "ok"))
    }
}
```

That's it! The router will automatically pick it up.

## 9. Summary

This simple architecture gives us:

- **Flow separation**: Each flow has its own handler
- **Easy routing**: Router automatically finds the right handler
- **Simple state**: Basic session storage for data between screens
- **Clean organization**: Clear structure without overengineering
- **Easy to extend**: Just add new handlers for new flows

The key is keeping it simple while solving the main problems: organizing different flows and maintaining state between screens.
