# Jornada do cliente

1. Cliente envia mensagem querendo onbordar com o JAF
2. JAF envia mensagem de bem-vindo “Bem-vindo ao JAF, assistente de contabilidade 24/7 que resolve sua contabilidade. Para continuarmos, informa por gentileza o seu CNPJ”
3. Cliente informa CNPJ
    1. JAF verifica se CNPJ é válido (basta bater no endpoint da Receita Federal - [https://receitaws.com.br/v1/cnpj/{cnpj}](https://receitaws.com.br/v1/cnpj/%7Bcnpj%7D). Se retornar vazio ou com erro já assume que deu ruim no CNPJ).
    2. Temos que checar se o cliente é aderente do simples nacional, se o CNPJ só possui um sócio PF (bloco QSA) e se o CNPJ está com situação “Ativa”. Se o CNPJ estiver marcado como Simples = False ou se no bloco QSA tiver mais de um sócio (atentar que o sócio precisa ser PF, ou seja, não pode aparecer o campo “nome_rep_legal”, podemos enviar mensagem falando que infelizmente no momento só atendemos CNPJs aderentes ao Simples Nacional e unipessoal.  Se o campo “Situação” estiver algo diferente de “ATIVA” podemos enviar mensagem falando que “Infelizmente não conseguimos te atender pois o CNPJ não está com situação ativa perante a Receita Federal”. Abaixo um exemplo do response do endpoint da Receita:


        ![image.png](attachment:c336dcae-6885-4eae-a6cd-1ba9bbc80c11:image.png)


![image.png](attachment:11e7b6a0-575e-4bb3-b5f7-2f2d64cabe6e:image.png)

![image.png](attachment:0c0b242d-e42e-4f4c-b43f-9ae4358dafcf:image.png)

1. Se o cliente informar um CNPJ que satisfaz todas as condições ((i) - Simples = True; (ii) - QSA possui apenas 1 sócio (iii) - Sócio é PF e (iv) - campo “situacao” = Ativa) vamos iniciar um flows pedindo pra ele inserir um e-mail e na sequência uma senha (fluxo parecido com a Magie). Acho que aqui já podemos fazer ele concordar com os termos de uso e política de privacidade (de novo, seguindo o padrão da jornada da Magie).

2. Após inserir e-mail e senha corretamente, vamos bater no endpoint de [criar empresa](https://www.notion.so/SOP-Onboarding-de-cliente-CNPJ-existente-1f0b7435df3780919459e7c13a00151c?pvs=21) na focusNFE com as informações que temos  vamos pedir para o usuário enviar o certificado digital e a senha do certificado. Importante explicar o motivo de pedirmos isso na mensagem: “Falta pouco! Agora precisamos apenas que nos envie o certificado digital da sua empresa. É com esse certificado que conseguimos fazer a emissão das notas e transmissão dos relatórios contábeis.”
    1. É importante termos na nossa tabela um status pra saber se o CNPJ em questão já nos enviou o certificado, se já enviou a senha e se a senha está válida/cerificado está dentro da validade. Todas essas informações serão retornadas do FocusNFE através do endpoint
    2. Uma vez o usuário enviando o cerificado e senha, vamos bater no endpoint POST /v2/empresas da FocusNFE que faz a criação da empresa na Focus. É importante checar o status da empresa após a criação (GET /v2/empresas/ID). Seria importante também armazenarmos em em alguma tabela nossa a data de validade do certificado. Também é importante armazenarmos os campos de token de produção e homologação que vem da FocusNFE (são com esses tokens que emitimos NFS pelo cliente). Checar na seção abaixo desse doc (”Chamadas FocusNFE”) de onde vem cada campo do body.

   ![image.png](attachment:0f123dc8-f846-448e-a5b0-fa0a2e4157b8:image.png)


OBS: Parece que recentemente a FocusNFE colocou um token de produção “Master” (podemos usar apenas pra conseguir emitir NFS em produção de qualquer cliente nosso)

![image.png](attachment:079caa6b-5cd6-4592-8ebe-119cb18e3ed0:image.png)

1. Depois que o usuário enviar o certificado digital com a senha e a gente criar a empresa corretamente (e constatar que o certificado digital está ativo), precisamos colocar em algum lugar no nosso BD um status de “Aguardando Inscrição Municipal”. Esse passo do onboarding é manual (ou via crawler). Aqui vamos basicamente entrar no site da prefeitura onde o CNPJ está registrado para pegar a inscrição municipal e Códigos de Serviços cadastrados do CNPJ. Após pegar esses dados, Arthur irá inputar manualmente a inscrição municipal na tabela companies e os códigos de serviços na tabela companies_cnaes.

1. Uma vez inserindo a inscrição municipal e códigos de serviços, Arthur irá alterar manualmente o status para “Aguardando e-CAC”. Nesse passo aqui basicamente precisamos entrar no portal e-cac com o certificado digital e senha do usuário para registrar o JAF como procurador do CNPJ do usuário.

2. Uma vez registrado o e-CAC, Arthur irá alterar manualmente o status para “Ativo”. Esse é o último passo do onboarding. Uma vez o status estando Ativo, o cliente já está apto para emitir NFS com a gente.

# Chamadas FocusNFE

## Criação de empresas - 1o passo(POST /v2/empresas)

Exemplo de body:

```
{
   "nome":"A A Ribeiro Consultoria em Tecnologia da Informacao LTDA",
   "nome_fantasia":"AAR Consultoria",
   "cnpj":"49286707000103",
   "cep":"01323902",
   "uf":"SP",
   "municipio":"São Paulo",
   "bairro":"Bela Vista",
   "logradouro":"Rua João Julião",
   "numero":296,
   "complemento":"Apto 23",
   "discrimina_impostos":true,
   "email":"<EMAIL>",
   "enviar_email_destinatario":true,
   "inscricao_municipal":null,
   "regime_tributario":1,
   "telefone":"21980563843",
   "habilita_nfse":true
}
```

OBS: Essa primeira chamada será feita logo após o cliente informar informar o CNPJ e validarmos que o CNPJ satisfaz todas as condições (Simples = True, 1 único sócio PF e situação na Receita Federal Ativa)

Segue abaixo o response quando enviamos o body acima.

```json
{
    "id": 131889,
    "nome": "A A Ribeiro Consultoria em Tecnologia da Informacao LTDA",
    "nome_fantasia": "AAR Consultoria",
    "inscricao_estadual": null,
    "inscricao_municipal": null,
    "bairro": "Bela Vista",
    "cargo_responsavel": null,
    "cep": "01323902",
    "cnpj": "49286707000103",
    "cpf": null,
    "codigo_municipio": "3550308",
    "codigo_pais": "1058",
    "codigo_uf": "35",
    "complemento": "Apto 23",
    "cpf_cnpj_contabilidade": null,
    "cpf_responsavel": null,
    "discrimina_impostos": true,
    "email": "<EMAIL>",
    "enviar_email_destinatario": true,
    "enviar_email_homologacao": false,
    "habilita_nfce": false,
    "habilita_nfe": false,
    "habilita_nfse": true,
    "habilita_nfsen_producao": null,
    "habilita_nfsen_homologacao": null,
    "habilita_cte": false,
    "habilita_mdfe": false,
    "habilita_manifestacao": false,
    "habilita_manifestacao_homologacao": false,
    "habilita_manifestacao_cte": false,
    "habilita_manifestacao_cte_homologacao": false,
    "logradouro": "Rua João Julião",
    "municipio": "São Paulo",
    "nome_responsavel": null,
    "numero": "296",
    "pais": "Brasil",
    "regime_tributario": "1",
    "telefone": "21980563843",
    "uf": "SP",
    "habilita_contingencia_offline_nfce": true,
    "habilita_contingencia_epec_nfce": false,
    "reaproveita_numero_nfce_contingencia": false,
    "mostrar_danfse_badge": true,
    "csc_nfce_producao": null,
    "id_token_nfce_producao": null,
    "csc_nfce_homologacao": null,
    "id_token_nfce_homologacao": null,
    "proximo_numero_nfe_producao": null,
    "proximo_numero_nfe_homologacao": null,
    "serie_nfe_producao": null,
    "serie_nfe_homologacao": null,
    "proximo_numero_nfse_producao": 1,
    "proximo_numero_nfse_homologacao": 1,
    "proximo_numero_nfsen_producao": null,
    "proximo_numero_nfsen_homologacao": null,
    "serie_nfse_producao": "1",
    "serie_nfse_homologacao": "1",
    "serie_nfsen_producao": null,
    "serie_nfsen_homologacao": null,
    "proximo_numero_nfce_producao": null,
    "proximo_numero_nfce_homologacao": null,
    "serie_nfce_producao": null,
    "serie_nfce_homologacao": null,
    "proximo_numero_cte_producao": null,
    "proximo_numero_cte_homologacao": null,
    "serie_cte_producao": null,
    "serie_cte_homologacao": null,
    "proximo_numero_cte_os_producao": null,
    "proximo_numero_cte_os_homologacao": null,
    "serie_cte_os_producao": null,
    "serie_cte_os_homologacao": null,
    "proximo_numero_mdfe_producao": null,
    "proximo_numero_mdfe_homologacao": null,
    "serie_mdfe_producao": null,
    "serie_mdfe_homologacao": null,
    "certificado_valido_ate": null,
    "certificado_valido_de": null,
    "certificado_cnpj": null,
    "certificado_especifico": false,
    "data_ultima_emissao": null,
    "caminho_logo": null,
    "login_responsavel": null,
    "senha_responsavel_preenchida": false,
    "orientacao_danfe": null,
    "recibo_danfe": true,
    "exibe_sempre_ipi_danfe": false,
    "exibe_issqn_danfe": false,
    "exibe_impostos_adicionais_danfe": false,
    "exibe_fatura_danfe": false,
    "exibe_unidade_tributaria_danfe": false,
    "exibe_desconto_itens": false,
    "exibe_sempre_volumes_danfe": false,
    "exibe_composicao_carga_mdfe": true,
    "data_inicio_recebimento_nfe": null,
    "data_inicio_recebimento_cte": null,
    "habilita_csrt_nfe": true,
    "nfe_sincrono": false,
    "nfe_sincrono_homologacao": false,
    "mdfe_sincrono": null,
    "mdfe_sincrono_homologacao": null,
    "smtp_endereco": null,
    "smtp_dominio": null,
    "smtp_autenticacao": null,
    "smtp_porta": null,
    "smtp_login": null,
    "smtp_remetente": null,
    "smtp_responder_para": null,
    "smtp_modo_verificacao_openssl": null,
    "smtp_habilita_starttlls": null,
    "smtp_ssl": null,
    "smtp_tls": null,
    "token_producao": "ey2ukv3hMKTRpOLpsQxLTHdhxZV9Cfrr",
    "token_homologacao": "3m7INKUl1fC1D2LNgL68ug0EGz9zwdrC",
    "client_app_id": 277732
}
```

Importante salvarmos no nosso BD o ID da empresa (ID que o FocusNFE atribui pra essa empresa), token_producao e token_homologacao.

## Criação de empresas - 2o passo(PUT /v2/empresas/{id})

Depois que o usuário enviar o certificado digital e senha, vamos fazer um amend no cadastro da empresa já criada. Para isso, podemos usar o curl abaixo:

```powershell
curl --location --request PUT 'https://api.focusnfe.com.br/v2/empresas/131889' \
--header 'Content-Type: application/json' \
--header 'Authorization: xxxxx' \
--data '{
   "arquivo_certificado_base64":"certificado_em_base_64",
   "senha_certificado":"xxxxx"
}'
```

OBS: Repara que na URL da chamada a gente coloca o ID da empresa que a focusNFE criou no 1o passo (131889)

O certificado tem que ser enviado em formato base64. Segue abaixo um exemplo de script em py que transforma de .PFX para base64

```jsx
import base64

# Path to your .pfx file
pfx_path = r"C:\Users\<USER>\Downloads\up-leg-certificate (3).pfx"
output_path = r"C:\Users\<USER>\Downloads\up-leg-certificate-base64.txt"

# Read and encode the file
with open(pfx_path, "rb") as f:
    pfx_bytes = f.read()
    b64_encoded = base64.b64encode(pfx_bytes).decode('utf-8')

# Write base64 to a text file
with open(output_path, "w") as f:
    f.write(b64_encoded)

print("Base64-encoded file saved to:", output_path)

```

Após fazer a requisição, é importante armazenar em nosso BD os campos “certificado_valido_ate” e “certificado_valido_de”

OBS: É muito importante fazermos a verificação de que o campo “certificado_cnpj” é igual ao CNPJ do nosso cliente (para evitarmos o caso de um cara se cadastrar em um CNPJ e enviar o certificado digital de um outro CNPJ).