# Meta Webhook Request Flow

Este documento descreve o fluxo completo de uma requisição de webhook do Meta (WhatsApp) desde o recebimento até o processamento final.

## 1. Endpoint de Entrada

A requisição é recebida no endpoint:
```
POST /api/v1/webhooks/meta
```

## 2. Controller (MetaWebhookController)

O primeiro ponto de contato é a classe `MetaWebhookController`, que:

1. Recebe o payload como uma string raw
2. Registra o log da requisição recebida
3. Tenta converter o payload para o objeto `MetaWebhookPayload`
4. <PERSON><PERSON> o use case `ProcessMetaWebhookUseCase` para processar o webhook
5. Retorna:
   - HTTP 200 (OK) em caso de sucesso
   - HTTP 500 (Internal Server Error) em caso de erro

```kotlin
@PostMapping("/meta")
fun receiveMetaWebhook(@RequestBody payload: String): ResponseEntity<Unit>
```

## 3. Processamento Inicial (ProcessMetaWebhookUseCase)

O `ProcessMetaWebhookUseCase` é implementado pela classe `ProcessMetaWebhookUseCaseImpl` que:

1. Verifica se o webhook contém mensagens (campo "messages")
2. Se contiver mensagens:
   - Registra o log da detecção
   - Envia o payload para uma fila SQS (`webhook-meta-v1-new-message-queue`)
3. Se não contiver mensagens:
   - Registra o log e ignora o webhook

## 4. Fila SQS (WebhookMetaQueueConsumer)

O `WebhookMetaQueueConsumer` é um consumidor da fila SQS que:

1. Recebe a mensagem da fila
2. Converte o payload de volta para `MetaWebhookPayload`
3. Utiliza o `MetaChatMessageEventConverter` para converter o payload em um `ChatMessageEvent`
4. Obtém a estratégia apropriada através do `MetaEventStrategyFactory`
5. Executa a estratégia para processar o evento

## 5. Conversão do Evento (MetaChatMessageEventConverter)

O `MetaChatMessageEventConverter` é responsável por:

1. Extrair informações importantes do payload:
   - ID do telefone do Meta
   - Tipo de chat
   - Número de telefone
   - Informações da mensagem
   - Mídia (se houver)
   - Referral (se houver)
2. Buscar ou criar um chat no banco de dados através do `ChatService`
3. Criar um objeto `ChatMessageEvent` com todas as informações necessárias

## 6. Estrutura do Payload (MetaWebhookPayload)

O payload do Meta contém várias informações importantes:

- `object`: Tipo do objeto
- `entry`: Lista de entradas contendo:
  - `id`: ID da entrada
  - `changes`: Lista de mudanças contendo:
    - `field`: Campo que mudou (ex: "messages")
    - `value`: Valor da mudança contendo:
      - `messaging_product`: Produto de mensageria
      - `metadata`: Metadados (incluindo phoneNumberId)
      - `contacts`: Informações de contato
      - `messages`: Lista de mensagens
      - `statuses`: Lista de status

## 7. Tipos de Mensagem Suportados

O sistema suporta os seguintes tipos de mensagem do Meta:

- Texto (`text`)
- Áudio (`audio`)
- Imagem (`image`)
- Documento (`document`)

## 8. Fluxo de Erros

Em caso de erro em qualquer etapa:

1. O erro é registrado nos logs com detalhes
2. Em caso de erro no controller:
   - Retorna HTTP 500
3. Em caso de erro no consumidor SQS:
   - O erro é registrado
   - A mensagem pode ser retentada pela fila SQS

## 9. Logging

O sistema mantém logs detalhados em cada etapa:

- Recebimento do webhook
- Processamento inicial
- Conversão do evento
- Processamento da mensagem
- Erros (se houver)

## 10. Segurança

- O payload é recebido como string raw e convertido de forma segura
- Erros de parsing são tratados adequadamente
- Informações sensíveis são registradas de forma segura nos logs

## Diagrama de Sequência

```
[Meta] -> [MetaWebhookController]
    -> [ProcessMetaWebhookUseCase]
        -> [SQS Queue]
            -> [WebhookMetaQueueConsumer]
                -> [MetaChatMessageEventConverter]
                    -> [ChatService]
                    -> [MetaEventStrategy]
                        -> [Processamento Final]
```

## Observações Importantes

1. O sistema é assíncrono, usando SQS para garantir processamento confiável
2. Suporta diferentes tipos de mensagem (texto, áudio, imagem, documento)
3. Mantém rastreabilidade através de logs detalhados
4. Implementa tratamento de erros em todas as camadas
5. Utiliza injeção de dependência para componentes
6. Segue princípios SOLID e padrões de projeto 