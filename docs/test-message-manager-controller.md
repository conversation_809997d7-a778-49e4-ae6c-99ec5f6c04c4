# TestMessageManagerController

This controller provides a test endpoint for the `MessageManagerService.sendAgentResponses` method.

## Endpoint

```
POST /api/v1/test/message-manager/send-agent-responses
```

## Request Format

```json
{
  "userPhoneNumber": "*************",
  "metaPhoneNumberId": "*********",
  "agentResponses": [
    // List of AgentResponse objects
  ]
}
```

## AgentResponse Types

Jackson uses **field-based type deduction** to automatically determine the correct AgentResponse subclass based on which fields are present in the JSON.

### 1. TextResponse
**Identified by:** `content` field

```json
{
  "content": "Hello, this is a text message!",
  "manual": true
}
```

### 2. ButtonResponse
**Identified by:** `message` and `buttons` fields

```json
{
  "message": "Choose an option:",
  "buttons": [
    {
      "id": "btn1",
      "title": "Option 1",
      "type": "REPLY",
      "payload": "callback1:value1"
    },
    {
      "id": "btn2",
      "title": "Option 2",
      "type": "REPLY",
      "payload": "callback2:value2"
    }
  ]
}
```

### 3. MediaResponse
**Identified by:** `mediaType` and `url` fields

```json
{
  "mediaType": "IMAGE",
  "url": "https://example.com/image.jpg",
  "caption": "This is an image caption",
  "filename": "image.jpg"
}
```

### 4. FlowResponse
**Identified by:** `flowId` field

```json
{
  "flowId": "onboarding.v1.create_jaf_account",
  "flowData": {
    "user_name": "João Silva",
    "company_cnpj": "**************",
    "step": "initial"
  },
  "triggerText": "Clique aqui para iniciar o cadastro"
}
```

### 5. CompositeResponse
**Identified by:** `responses` field (array of other AgentResponse objects)

```json
{
  "responses": [
    {
      "content": "First message",
      "manual": true
    },
    {
      "message": "Then choose:",
      "buttons": [
        {
          "id": "btn1",
          "title": "Yes",
          "type": "REPLY"
        }
      ]
    }
  ]
}
```

## Complete Example Request

```json
{
  "userPhoneNumber": "*************",
  "metaPhoneNumberId": "*********",
  "agentResponses": [
    {
      "content": "Welcome! Let me help you.",
      "manual": true
    },
    {
      "message": "What would you like to do?",
      "buttons": [
        {
          "id": "create_company",
          "title": "Create Company",
          "type": "REPLY",
          "payload": "create_company:start"
        },
        {
          "id": "view_companies",
          "title": "View Companies", 
          "type": "REPLY",
          "payload": "view_companies:list"
        }
      ]
    },
    {
      "mediaType": "IMAGE",
      "url": "https://example.com/help-image.jpg",
      "caption": "Here's a helpful image"
    }
  ]
}
```

## Response Format

```json
{
  "success": true,
  "externalMessageIds": [
    "msg_123456",
    "msg_123457", 
    "msg_123458"
  ],
  "totalSent": 3,
  "totalRequested": 3,
  "message": "Agent responses sent successfully",
  "errors": null
}
```

## Usage with curl

```bash
curl -X POST http://localhost:8080/api/v1/test/message-manager/send-agent-responses \
  -H "Content-Type: application/json" \
  -d '{
    "userPhoneNumber": "*************",
    "metaPhoneNumberId": "*********",
    "agentResponses": [
      {
        "content": "Hello from test!",
        "manual": true
      }
    ]
  }'
```

## How JSON Type Deduction Works

The `AgentResponse` sealed class uses Jackson's `@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)` annotation, which automatically determines the correct subclass based on **which fields are present** in the JSON:

- **TextResponse**: Has `content` field (and optionally `manual`)
- **ButtonResponse**: Has `message` field and `buttons` array
- **MediaResponse**: Has `mediaType`, `url` fields (and optionally `caption`, `filename`)
- **CompositeResponse**: Has `responses` array containing other AgentResponse objects
- **FlowResponse**: Has `flowId` field (and optionally `flowData`, `triggerText`)
- **ListResponse**: Has `title` and `items` fields (and optionally `buttonText`)
- **TemplateResponse**: Has `templateName` field (and optionally `parameters`, `languageCode`)

Jackson analyzes the JSON structure and matches it to the appropriate subclass based on these unique field combinations. **No explicit type field is needed** in the JSON - the structure itself determines the type.

### Important Notes:
- Each AgentResponse subclass must have **unique field combinations** that don't overlap
- If JSON contains fields that match multiple types, Jackson will choose the first matching type
- Missing required fields will cause deserialization errors

## Notes

- The controller creates a temporary `ChatEntity` for testing purposes
- All AgentResponse types from the domain model are supported
- The method tests the complete flow including message sending and database persistence
- External message IDs are returned for verification
- Any errors during sending are logged but don't stop the process for other messages
