# Focus NFSe API Client Documentation

This document explains how to use the Focus NFSe API client implemented in the MeContaAI project. The client follows the Clean Architecture principles and provides an interface for issuing, checking, canceling and sending electronic service invoices (NFSe) through the Focus API.

## Table of Contents

1. [Overview](#overview)
2. [Setup and Configuration](#setup-and-configuration)
3. [API Endpoints](#api-endpoints)
4. [Examples](#examples)
5. [Error Handling](#error-handling)
6. [Testing](#testing)

## Overview

The Focus API client is integrated with our application following the Clean Architecture principles:

- **Presentation Layer**: Controllers that accept HTTP requests and return HTTP responses
- **Application Layer**: Use cases and application services that orchestrate business logic
- **Domain Layer**: Domain entities, repositories, and services containing business rules
- **Infrastructure Layer**: Implementation of repositories, API clients, and other technical concerns

The NFSe process follows these steps:

1. Submit an NFSe for processing (async operation)
2. Check NFSe status until it's processed
3. If authorized, the NFSe is valid and can be accessed via URL
4. If rejected, error information is provided

## Setup and Configuration

### Configuration Properties

The Focus API client requires the following configuration in your `application.yaml`:

```yaml
focus:
  api:
    url: https://homologacao.focusnfe.com.br  # Sandbox URL
    token: YOUR_API_TOKEN
  prestador:
    cnpj: YOUR_COMPANY_CNPJ
    inscricao-municipal: YOUR_MUNICIPAL_REGISTRATION
    codigo-municipio: YOUR_MUNICIPALITY_CODE
```

For production environments:

```yaml
focus:
  api:
    url: https://api.focusnfe.com.br  # Production URL
    token: ${FOCUS_API_TOKEN}
  prestador:
    cnpj: ${FOCUS_PRESTADOR_CNPJ}
    inscricao-municipal: ${FOCUS_PRESTADOR_INSCRICAO_MUNICIPAL}
    codigo-municipio: ${FOCUS_PRESTADOR_CODIGO_MUNICIPIO}
```

### Required Dependencies

The client uses Feign for HTTP requests. The required dependencies are already in the project's `build.gradle.kts`.

## API Endpoints

The NFSe API provides the following endpoints:

### 1. Create an NFSe

```
POST /api/v1/nfse
```

Request body:
```json
{
  "customerId": "customer-uuid",
  "valorServico": 100.00,
  "discriminacao": "Serviço de consultoria em desenvolvimento de software",
  "dataEmissao": "2023-04-20T14:00:00",
  "itemListaServico": "0107",
  "issRetido": false,
  "aliquota": 3.0,
  "codigoTributarioMunicipio": "*********"
}
```

### 2. Check NFSe Status

```
GET /api/v1/nfse/{reference}
```

### 3. Cancel an NFSe

```
DELETE /api/v1/nfse/{reference}
```

Request body:
```json
{
  "justificativa": "Emissão incorreta de NFSe"
}
```

### 4. Send NFSe Email

```
POST /api/v1/nfse/{reference}/email
```

Request body:
```json
{
  "emails": ["<EMAIL>", "<EMAIL>"]
}
```

### 5. List Customer NFSes

```
GET /api/v1/nfse/customer/{customerId}
```

## Examples

### Using the NFSe Service in a Domain Service

```kotlin
@Service
class InvoiceService(private val nfseService: NfseService) {
    
    fun issueInvoice(customer: Customer, amount: Double, description: String): Nfse {
        return nfseService.createNfse(
            customer = customer,
            valorServico = amount,
            discriminacao = description,
            itemListaServico = "0107",
            issRetido = false,
            aliquota = 3.0,
            codigoMunicipioPrestador = "3550308",
            inscricaoMunicipalPrestador = "12345",
            cnpjPrestador = "12345678000199",
            codigoTributarioMunicipio = "*********"
        )
    }
    
    fun checkInvoiceStatus(reference: String): Nfse {
        return nfseService.getNfseStatus(reference)
    }
}
```

### Using the Use Case Directly

```kotlin
@Service
class YourService(private val emitirNfseUseCase: EmitirNfseUseCase) {
    
    fun issueInvoice(customerId: String, amount: Double, description: String): NfseDto {
        val createNfseDto = CreateNfseDto(
            customerId = customerId,
            valorServico = amount,
            discriminacao = description,
            itemListaServico = "0107",
            issRetido = false,
            aliquota = 3.0
        )
        
        return emitirNfseUseCase.execute(createNfseDto)
    }
}
```

## Error Handling

The client provides detailed error information through the `FocusException` and `NfseException` classes:

- `FocusException` contains details from the Focus API, including error codes and messages
- `NfseException` is a domain exception that may wrap FocusException or provide domain-specific errors

Example of handling errors:

```kotlin
try {
    val nfse = nfseService.createNfse(/* parameters */)
    // Process NFSe
} catch (e: NfseException) {
    logger.error("Domain error: ${e.message}", e)
    // Handle domain error
} catch (e: FocusException) {
    logger.error("Focus API error: Status ${e.httpStatus}, Code: ${e.errorCode}, Message: ${e.message}", e)
    // Handle API error
}
```

### Common Error Codes

- `requisicao_invalida`: Invalid request (missing fields, invalid values)
- `nfe_nao_encontrada`: NFSe not found
- `nfe_cancelada`: NFSe already canceled
- Various municipality-specific error codes starting with "E" (e.g., "E145")

## Testing

For automated testing, the client can be mocked using any mocking framework like Mockito. Example:

```kotlin
@ExtendWith(MockitoExtension::class)
class YourServiceTest {
    
    @Mock
    private lateinit var focusClient: FocusClient
    
    @InjectMocks
    private lateinit var nfseService: NfseService
    
    @Test
    fun `should create NFSe successfully`() {
        // Setup mock response
        whenever(focusClient.createNfse(any(), any())).thenReturn(
            NfseEnvioResponseDto(
                cnpj_prestador = "12345678000199",
                ref = "test-ref",
                status = "processando_autorizacao"
            )
        )
        
        // Execute service method
        val nfse = nfseService.createNfse(/* parameters */)
        
        // Assertions
        assertEquals("processando_autorizacao", nfse.status)
        assertEquals("test-ref", nfse.reference)
    }
}
```

For integration testing, we recommend using a sandbox environment and real API calls with test data.