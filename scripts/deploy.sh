#!/bin/bash

# Verifica se a AWS CLI está instalada
if ! command -v aws &> /dev/null; then
  echo "❌ AWS CLI não está instalada. Por favor, instale-a antes de continuar."
  exit 1
fi

# Verifica se as credenciais da AWS estão configuradas
if ! aws sts get-caller-identity &> /dev/null; then
  echo "❌ AWS CLI não está configurada corretamente."
  echo "Iniciando o assistente de configuração da AWS CLI..."
  aws configure
  # Após a configuração, verifica novamente
  if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ Falha na configuração da AWS CLI. Verifique suas credenciais e tente novamente."
    exit 1
  fi
fi

# Configurações
APP_NAME="contacomjaf"
ENV_NAME="Contacomjaf-prod-env"
S3_BUCKET="elasticbeanstalk-us-east-1-418295705357"
REGION="us-east-1"  # Altere para sua região
# Versão baseada na data e hora atual
VERSION=$(date +"%Y%m%d%H%M%S")
# Diretório de deploy e nome do pacote
DEPLOY_DIR="deploy"
DEPLOY_ZIP="$APP_NAME-$VERSION-deploy.zip"
# Slack
SLACK_TOKEN="*********************************************************"
SLACK_CHANNEL="#deploys"

# Função para enviar notificação ao Slack
send_slack_message() {
  local message="$1"
  curl -s -X POST -H "Authorization: Bearer $SLACK_TOKEN" \
    -H "Content-type: application/json" \
    --data "{\"channel\": \"$SLACK_CHANNEL\", \"text\": \"$message\", \"username\": \"Jafinho\"}" \
    https://slack.com/api/chat.postMessage > /dev/null
}

# Função de limpeza
cleanup() {
  echo "🧹  Limpando arquivos temporários $DEPLOY_DIR e $DEPLOY_ZIP..."
  rm -rf "$DEPLOY_DIR" "$DEPLOY_ZIP"
}

# Configura o trap para chamar a função cleanup ao sair do script
trap cleanup EXIT

# Início do processo
send_slack_message ":rocket: *$(whoami)* iniciou o deploy da aplicação *$APP_NAME* versão *$VERSION* no ambiente *$ENV_NAME*."

# 1. Build da aplicação
echo "🏗️  Building application [$APP_NAME]..."
#./gradlew clean build
./gradlew clean bootJar -x test || {
  send_slack_message ":x: Erro ao compilar a aplicação *$APP_NAME*."
  exit 1
}

# 2. Criar pacote de deploy
echo "📦  Creating deployment package for application [$APP_NAME] with version [$VERSION]..."
DEPLOY_DIR="deploy"

mkdir -p $DEPLOY_DIR
cp build/libs/*.jar $DEPLOY_DIR/application.jar
#cp -r .ebextensions $DEPLOY_DIR/
cd $DEPLOY_DIR && zip -r ../$APP_NAME-$VERSION-deploy.zip . && cd ..

# 3. Upload para S3
echo "☁️  Uploading to S3 at $S3_BUCKET/$APP_NAME-$VERSION-deploy.zip..."
aws s3 cp $DEPLOY_ZIP s3://$S3_BUCKET/$DEPLOY_ZIP --region $REGION || {
  send_slack_message ":x: Falha ao enviar o pacote *$DEPLOY_ZIP* para o S3."
  exit 1
}

# 4. Deploy no Elastic Beanstalk
echo " 🚀  Creating application [$APP_NAME] version [$VERSION] to Elastic Beanstalk from S3 [$S3_BUCKET] with key [$APP_NAME-$VERSION-deploy.zip]..."
aws elasticbeanstalk create-application-version \
  --application-name $APP_NAME \
  --version-label $VERSION \
  --process \
  --source-bundle S3Bucket=$S3_BUCKET,S3Key=$APP_NAME-$VERSION-deploy.zip \
  --region $REGION \
  --no-cli-pager || {
  send_slack_message ":x: Falha ao criar a versão *$VERSION* da aplicação *$APP_NAME*."
  exit 1
}

# 5. Aguardar até que a versão seja processada
echo "⏳  Waiting for application version [$VERSION] to be processed..."
while true; do
  STATUS=$(aws elasticbeanstalk describe-application-versions \
    --application-name $APP_NAME \
    --version-label $VERSION \
    --region $REGION \
    --query 'ApplicationVersions[0].Status' \
    --output text)

  echo "Current status: $STATUS"

  if [ "$STATUS" == "PROCESSED" ]; then
    echo "✅  Application version [$VERSION] processed successfully."
    break
  elif [ "$STATUS" == "FAILED" ]; then
    echo "❌  Failed to process application version [$VERSION]."
    send_slack_message ":x: Falha no processamento da versão *$VERSION* da aplicação *$APP_NAME*."
    exit 1
  else
    echo "⏳  Still processing... waiting 3 seconds."
    sleep 3
  fi
done

# 6. Implantar a nova versão no ambiente
echo "🚀  Deploying application [$APP_NAME] version [$VERSION] to environment [$ENV_NAME]..."
aws elasticbeanstalk update-environment \
  --application-name $APP_NAME \
  --environment-name $ENV_NAME \
  --version-label $VERSION \
  --region $REGION \
  --no-cli-pager || {
  send_slack_message ":x: Falha ao fazer deploy da versão *$VERSION* no ambiente *$ENV_NAME*."
  exit 1
}

# 7. Aguardar até que o ambiente esteja pronto
echo "⏳  Waiting for environment [$ENV_NAME] to finish updating..."
aws elasticbeanstalk wait environment-updated \
  --environment-name "$ENV_NAME" \
  --region "$REGION" || {
  send_slack_message ":x: Timeout ou erro ao esperar a atualização do ambiente *$ENV_NAME*."
  exit 1
}

send_slack_message ":white_check_mark: Deploy da aplicação *$APP_NAME* versão *$VERSION* concluído com sucesso no ambiente *$ENV_NAME*."
echo "✅  Deployment of [$APP_NAME] version [$VERSION] to environment [$ENV_NAME] complete!"