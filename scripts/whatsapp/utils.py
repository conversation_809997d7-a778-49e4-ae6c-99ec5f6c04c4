import hashlib
import hmac
import json
import psycopg
import sys
from config import DATABASE_CONFIG, WEBHOOK_SECRET

def generate_signature(request_body):
    """
    Gera a assinatura X-Hub-Signature-256.

    Args:
      request_body: Corpo da requisição.

    Returns:
      A assinatura X-Hub-Signature-256.
    """
    signature = hmac.new(
        WEBHOOK_SECRET.encode('utf-8'),
        msg=request_body.encode('utf-8'),
        digestmod=hashlib.sha256
    ).hexdigest()

    print(signature)
    return f"sha256={signature}"

def connect_to_database():
    try:
        return psycopg.connect(**DATABASE_CONFIG)
    except Exception as e:
        print(f"Não foi possível conectar ao banco de dados: {e}")
        sys.exit(1)

def fetch_messages():
    conn = connect_to_database()
    cur = conn.cursor()
    cur.execute("SELECT message, is_message_from_me, created_at FROM chat_messages ORDER BY created_at")
    messages = cur.fetchall()
    cur.close()
    conn.close()
    return messages

def get_interactive_uuid(index):
    conn = connect_to_database()
    cur = conn.cursor()
    cur.execute("SELECT uuid FROM meta_interactive_requests ORDER BY created_at DESC LIMIT " + str(index + 1))
    last_uuids = cur.fetchall()  # Pega o uuid indicado pelo index (0 para último e 1 para penúltimo)
    cur.close()
    conn.close()
    return last_uuids[index][0] 