import json
import random
import requests
from utils import generate_signature, get_interactive_uuid
from config import API_TOKEN, API_ENDPOINT, LOCAL_ENDPOINT

def send_message(message):
    random_id = random.randint(0, 1000000)
    data = {
        "object": "whatsapp_business_account",
        "entry": [
            {
                "id": "***************",
                "changes": [
                    {
                        "value": {
                            "messaging_product": "whatsapp",
                            "metadata": {
                                "display_phone_number": "************",
                                "phone_number_id": "***************"
                            },
                            "contacts": [
                                {
                                    "profile": {
                                        "name": "J\u00falio C\u00e9sar"
                                    },
                                    "wa_id": "*************"
                                }
                            ],
                            "messages": [
                                {
                                    "from": "*************",
                                    "id": f"wamid.HBgNNTUxMTk1MTE3MTc2NhUCABJYFDNBMjU4RjIyNjNEQjgwREHFQzBCABf{random_id}==",
                                    "timestamp": "1711136372",
                                    "text": {
                                        "body": f"{message}"
                                    },
                                    "type": "text"
                                }
                            ]
                        },
                        "field": "messages"
                    }
                ]
            }
        ]
    }

    signature = generate_signature(json.dumps(data))
    headers = {
        'Authorization': API_TOKEN,
        'Content-Type': 'application/json',
        'X-Hub-Signature-256': signature,
    }

    try:
        response = requests.post(API_ENDPOINT, json=data, headers=headers)
        if response.status_code == 200:
            print('Mensagem enviada com sucesso')
            return True
        else:
            print('Falha ao enviar mensagem', response.status_code)
            return False
    except requests.RequestException as e:
        print(f'Erro ao enviar mensagem: {e}')
        return False

def press_button(index):
    last_uuid = str(get_interactive_uuid(index))
    data = {
        "object": "whatsapp_business_account",
        "entry": [{
            "id": "***************",
            "changes": [{
                "value": {
                    "messaging_product": "whatsapp",
                    "metadata": {
                        "display_phone_number": "************",
                        "phone_number_id": "***************"
                    },
                    "contacts": [{
                        "profile": {
                            "name": "Itamar Rocha Filho"
                        },
                        "wa_id": "************"
                    }],
                    "messages": [{
                        "context": {
                            "from": "************",
                            "id": "wamid.HBgMNTU4Mzk0NDQ3Mjg5FQIAERgSNDYyOUM1RjY0NjFDNkJGOUYzAA=="
                        },
                        "from": "************",
                        "id": f"wamid.HBgMNTU4Mzk0NDQ3Mjg5FQIAEhgUM0FCMjUzODdGNDNFMENGMjQ1MTkA",
                        "timestamp": "**********",
                        "type": "interactive",
                        "interactive": {
                            "type": "button_reply",
                            "button_reply": {
                                "id": last_uuid,
                                "title": "Confirmar"
                            }
                        }
                    }]
                },
                "field": "messages"
            }]
        }]
    }
    signature = generate_signature(json.dumps(data))
    headers = {
        'Authorization': API_TOKEN,
        'Content-Type': 'application/json',
        'X-Hub-Signature-256': signature,
    }
    response = requests.post(LOCAL_ENDPOINT, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        print('Ação de botão enviada com sucesso.')
        return True
    else:
        print('Falha ao enviar ação de botão', response.status_code)
        return False 