import time
from PyQt5.QtCore import pyqtSignal, QThread
from utils import fetch_messages

class AdaptivePollingWorker(QThread):
    update_signal = pyqtSignal(list)

    def __init__(self):
        super().__init__()
        self.last_user_action_time = time.time()
        self.intensive_polling = False
        self.intensive_polling_start_time = 0
        self.last_message_count = 0

    def set_user_action(self):
        # Chamado quando o usuário envia mensagem ou interage
        self.last_user_action_time = time.time()
        self.intensive_polling = True
        self.intensive_polling_start_time = time.time()
        print("Ação do usuário detectada - iniciando polling intensivo")

    def run(self):
        while True:
            current_time = time.time()
            
            # Sempre fazer polling, mas ajustar a frequência
            if self.intensive_polling:
                # Polling intensivo por 15 segundos após ação do usuário
                if current_time - self.intensive_polling_start_time < 15:
                    sleep_time = 1  # Polling a cada 1 segundo
                    print("Polling intensivo: verificando a cada 1 segundo")
                else:
                    self.intensive_polling = False
                    sleep_time = 5  # Voltar ao polling normal
                    print("Polling intensivo concluído - voltando ao normal")
            else:
                # Polling normal
                sleep_time = 5  # Polling a cada 5 segundos
            
            # Sempre buscar mensagens
            try:
                messages = fetch_messages()
                message_count = len(messages)
                
                # Emitir sinal apenas se houver novas mensagens ou for a primeira carga
                if message_count > self.last_message_count or self.last_message_count == 0:
                    print(f"Novas mensagens detectadas: {message_count} (anterior: {self.last_message_count})")
                    self.update_signal.emit(messages)
                    self.last_message_count = message_count
            except Exception as e:
                print(f"Erro ao buscar mensagens: {e}")
            
            time.sleep(sleep_time) 