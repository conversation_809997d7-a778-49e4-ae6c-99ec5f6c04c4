from PyQt5.Qt<PERSON>ore import pyqtSlot, QTimer, Qt
from PyQt5.QtWidgets import Q<PERSON>ain<PERSON>indow, QTextEdit, QPushButton, QVBoxLayout, QWidget, QLineEdit, QLabel, QHBoxLayout
from PyQt5.QtGui import QPixmap
import os

from api_client import send_message, press_button
from polling_worker import AdaptivePollingWorker


class WhatsAppWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.start_message_updater()

    def initUI(self):
        self.setWindowTitle('JAF Zap')
        self.setStyleSheet("""
            QMainWindow {
                background-color: #e5ddd5;
                background-image: url('whatsapp_bg.png');
            }
            QTextEdit {
                background-color: transparent;
                border: none;
                font-family: Arial, sans-serif;
                font-size: 14px;
                padding: 10px;
            }
            QScrollBar {
                width: 0px;
                height: 0px;
                background: transparent;
                margin: 0px;
                padding: 0px;
            }
            QScrollBar::handle {
                background: transparent;
                border: none;
            }
            QScrollBar::add-line, QScrollBar::sub-line,
            QScrollBar::add-page, QScrollBar::sub-page {
                background: transparent;
                border: none;
                height: 0px;
                width: 0px;
            }
            QLineEdit {
                border-radius: 20px;
                padding: 10px 15px;
                background-color: white;
                font-family: Arial, sans-serif;
                font-size: 14px;
                min-height: 40px;
                color: black !important;
            }
            QPushButton {
                background-color: #128C7E;
                color: white;
                border: none;
                border-radius: 15px;
                padding: 8px 12px;
                font-weight: bold;
                min-height: 30px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #075E54;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)

        # Cabeçalho do WhatsApp
        header_layout = QHBoxLayout()
        header = QWidget()
        header.setStyleSheet("background-color: #075E54; color: white; border-radius: 10px 10px 0 0;")
        header.setMinimumHeight(60)
        header_layout.setContentsMargins(15, 15, 15, 15)

        # Logo
        logo_label = QLabel()
        logo_pixmap = QPixmap(os.path.join(os.path.dirname(__file__), 'image', 'jaf_logo.png'))
        scaled_pixmap = logo_pixmap.scaled(40, 40, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        logo_label.setPixmap(scaled_pixmap)
        logo_label.setFixedSize(40, 40)
        header_layout.addWidget(logo_label)

        # Espaçamento entre logo e título
        header_layout.addSpacing(10)

        # Título
        header_title = QLabel("JAF Zap")
        header_title.setStyleSheet("font-size: 18px; font-weight: bold;")
        header_layout.addWidget(header_title)

        # Espaço flexível para empurrar tudo para a esquerda
        header_layout.addStretch()

        header.setLayout(header_layout)
        layout.addWidget(header)

        # Área de mensagens
        self.text_area = QTextEdit()
        self.text_area.setReadOnly(True)
        self.text_area.setStyleSheet("""
            QScrollBar:vertical {
                border: none;
                background: #f1f1f1;
                width: 10px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical {
                background: #888;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.text_area)

        # Área de entrada de mensagem e botão de envio
        input_layout = QHBoxLayout()
        input_layout.setSpacing(10)

        self.message_entry = QLineEdit()
        self.message_entry.setPlaceholderText("Digite uma mensagem")
        self.message_entry.returnPressed.connect(self.send_message)
        input_layout.addWidget(self.message_entry, 4)

        send_button = QPushButton("Enviar")
        send_button.clicked.connect(self.send_message)
        send_button.setFixedWidth(100)
        input_layout.addWidget(send_button, 1)

        input_widget = QWidget()
        input_widget.setLayout(input_layout)
        layout.addWidget(input_widget)

        # Botões de ação
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        press_last_button = QPushButton("Clicar no último botão")
        press_last_button.clicked.connect(lambda: self.press_button_action(0))
        press_last_button.setFixedHeight(20)
        press_last_button.setStyleSheet("""
            QPushButton {
                background-color: #128C7E;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 5px 10px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #075E54;
            }
        """)
        button_layout.addWidget(press_last_button)

        press_prev_button = QPushButton("Clicar no penúltimo botão")
        press_prev_button.clicked.connect(lambda: self.press_button_action(1))
        press_prev_button.setFixedHeight(20)
        press_prev_button.setStyleSheet("""
            QPushButton {
                background-color: #128C7E;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 5px 10px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #075E54;
            }
        """)
        button_layout.addWidget(press_prev_button)

        button_widget = QWidget()
        button_widget.setLayout(button_layout)
        layout.addWidget(button_widget)

        central_widget = QWidget()
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)

        # Definir tamanho inicial da janela
        self.setMinimumSize(500, 700)

    def press_button_action(self, index):
        try:
            press_button(index)
            print("Botão pressionado.")
        except Exception as e:
            print(f"Erro ao pressionar o botão: {e}")

    def send_message(self):
        message = self.message_entry.text()
        if message:
            if send_message(message):
                # Notificar o worker sobre a ação do usuário
                self.worker.set_user_action()  # Ativar polling intensivo
            self.message_entry.clear()
            
            # Force scroll to bottom after sending a message
            QTimer.singleShot(100, self.scroll_to_bottom)

    def scroll_to_bottom(self):
        """Scroll the text area to the bottom"""
        scrollbar = self.text_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def update_messages(self, messages):
        self.text_area.clear()

        # Primeiro, construa todo o HTML como uma única string
        html_content = """
        <style>
            body {
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
                color: black !important;
            }
            .message-container {
                width: 100%;
                overflow: hidden;
                margin: 5px 0;
            }
            .time {
                font-size: 11px;
                color: black !important;
                margin-top: 5px;
            }
            .blue-ticks {
                color: #34B7F1;
            }
            td {
                color: black !important;
            }
            div {
                color: black !important;
            }
        </style>
        """

        for message, is_from_me, created_at in messages:
            # Ajustar o horário subtraindo 3 horas
            adjusted_time = created_at.replace(hour=(created_at.hour - 3) % 24)
            time_str = adjusted_time.strftime("%H:%M")

            # Tratar quebras de linha de forma mais robusta
            if message:
                # Primeiro, normalizar todas as quebras de linha para \n
                normalized_message = message.replace('\\n', '\n')

                # Agora, dividir a mensagem em linhas
                lines = normalized_message.split('\n')

                # Criar uma tabela para cada linha da mensagem
                message_html = ""
                for line in lines:
                    if line.strip():  # Ignorar linhas vazias
                        message_html += f"<div>{line}</div>"
                    else:
                        message_html += "<div>&nbsp;</div>"  # Espaço não quebrável para linhas vazias

                # Log para depuração
                print(f"Mensagem original: '{message}'")
                print(f"Mensagem normalizada: '{normalized_message}'")
                print(f"Linhas: {lines}")
                print(f"HTML da mensagem: '{message_html}'")
            else:
                message_html = ""

            if is_from_me:
                # Mensagem do bot (à esquerda, fundo branco)
                html_content += f"""
                <div class="message-container">
                    <table cellpadding="0" cellspacing="0" border="0" align="left" style="max-width: 80%;">
                        <tr>
                            <td style="background-color: white; padding: 8px 12px; max-width: 100%;">
                                {message_html}
                                <div class="time">{time_str}</div>
                            </td>
                        </tr>
                    </table>
                </div>
                """
            else:
                # Mensagem do usuário (à direita, fundo verde)
                html_content += f"""
                <div class="message-container" style="text-align: right;">
                    <table cellpadding="0" cellspacing="0" border="0" align="right" style="max-width: 80%;">
                        <tr>
                            <td style="background-color: #d9fdd3; padding: 8px 12px; max-width: 100%;">
                                {message_html}
                                <div class="time" style="text-align: right;">{time_str} <span class="blue-ticks">✓✓</span></div>
                            </td>
                        </tr>
                    </table>
                </div>
                """

        # Defina o HTML completo de uma vez
        self.text_area.setHtml(html_content)

        # Rola para o final do texto usando o método dedicado
        QTimer.singleShot(50, self.scroll_to_bottom)

    def start_message_updater(self):
        self.worker = AdaptivePollingWorker()
        self.worker.update_signal.connect(self.update_messages)
        self.worker.start()
