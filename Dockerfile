# ---- Stage 1: Build the application ----
FROM openjdk:21-jdk-slim AS build

WORKDIR /app

# Copy Gradle wrapper and project files
COPY gradlew .
COPY gradle ./gradle
COPY build.gradle.kts .
COPY settings.gradle.kts .
COPY src ./src

RUN chmod +x gradlew

# Build the application
RUN ./gradlew clean build -x test --stacktrace --info

# ---- Stage 2: Create a minimal runtime image ----
FROM openjdk:21-jdk-slim

WORKDIR /app

# Copy the jar from the build stage
COPY --from=build /app/build/libs/*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]