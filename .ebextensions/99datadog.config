# .ebextensions/99datadog.config
option_settings:
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: DD_AGENT_MAJOR_VERSION
      value: "7"
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: DD_AGENT_MINOR_VERSION
      value: "" # Eg: 34 to install 7.34.x, leave empty to install the latest 7.x

files:
  "/etc/datadog-agent/datadog.yaml":
    mode: "000640"
    owner: root # will be changed to dd-agent after the installation
    group: root
    content: |
      # Add here the Agent configuration
      api_key: "7cd8f333a7e24ede975602b80cfaa323"
      site: datadoghq.com

  "/datadog_install_script.sh":
    mode: "000700"
    owner: root
    group: root
    source: https://install.datadoghq.com/scripts/install_script_agent7.sh

container_commands:
    05setup_datadog:
        command: "DD_API_KEY=unused /datadog_install_script.sh; sed -i 's/ install_script/ ebs_install_script/' /etc/datadog-agent/install_info"