-- Adicionar company_id à tabela users_chats
ALTER TABLE users_chats ADD COLUMN company_id BIGINT REFERENCES companies(id);

-- <PERSON><PERSON>r índice para o company_id
CREATE INDEX idx_users_chats_company_id ON users_chats(company_id);

-- Remover constraint única original (user_id, chat_id)
ALTER TABLE users_chats DROP CONSTRAINT users_chats_user_id_chat_id_key;

-- Criar índice único parcial para garantir que só pode existir um registro ativo por (user_id, chat_id)
CREATE UNIQUE INDEX idx_users_chats_active_unique ON users_chats(user_id, chat_id) WHERE is_active = true;

-- Comentário para a nova coluna
COMMENT ON COLUMN users_chats.company_id IS 'ID da empresa associada a esta relação usuário-chat - pode ser NULL até ser definida'; 