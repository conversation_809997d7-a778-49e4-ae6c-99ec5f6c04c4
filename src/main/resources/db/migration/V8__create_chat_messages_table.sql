-- <PERSON><PERSON><PERSON> da tabela de mensagens de chat
CREATE TABLE chat_messages (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID NOT NULL UNIQUE,
    chat_id BIGINT NOT NULL REFERENCES chats(id),
    external_message_id VARCHAR(255),
    meta_phone_number_id VARCHAR(255),
    status VARCHAR(20) NOT NULL,
    is_message_to_me BOOLEAN NOT NULL DEFAULT FALSE,
    is_message_from_me BOOLEAN NOT NULL DEFAULT FALSE,
    is_group_message BOOLEAN NOT NULL DEFAULT FALSE,
    is_forwarded_message BOOLEAN NOT NULL DEFAULT FALSE,
    is_from_bot BOOLEAN NOT NULL DEFAULT FALSE,
    message TEXT,
    message_author_name VA<PERSON>HAR(255),
    message_author_phone_number VARCHAR(255),
    media_mimetype VARCHAR(100),
    media_download_url VARCHAR(255),
    media_filename VARCHAR(255),
    media_caption TEXT,
    sent_at TIMESTAMP,
    delivery_at TIMESTAMP,
    read_at TIMESTAMP,
    originating_message_uuid UUID,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Índices
CREATE INDEX idx_chat_messages_uuid ON chat_messages(uuid);
CREATE INDEX idx_chat_messages_chat_id ON chat_messages(chat_id);
CREATE INDEX idx_chat_messages_external_message_id ON chat_messages(external_message_id);
CREATE INDEX idx_chat_messages_meta_phone_number_id ON chat_messages(meta_phone_number_id);
CREATE INDEX idx_chat_messages_status ON chat_messages(status);
CREATE INDEX idx_chat_messages_originating_message_uuid ON chat_messages(originating_message_uuid);

-- Comentários
COMMENT ON TABLE chat_messages IS 'Tabela que armazena mensagens de chat do WhatsApp';
COMMENT ON COLUMN chat_messages.id IS 'Identificador único da mensagem';
COMMENT ON COLUMN chat_messages.uuid IS 'UUID único da mensagem';
COMMENT ON COLUMN chat_messages.chat_id IS 'ID do chat ao qual a mensagem pertence';
COMMENT ON COLUMN chat_messages.external_message_id IS 'ID da mensagem no sistema externo (WhatsApp)';
COMMENT ON COLUMN chat_messages.meta_phone_number_id IS 'ID do número de telefone da Meta associado à mensagem';
COMMENT ON COLUMN chat_messages.status IS 'Status da mensagem (RECEIVED, PROCESSED, FAILED, etc)';
COMMENT ON COLUMN chat_messages.is_message_to_me IS 'Indica se a mensagem foi enviada para o bot';
COMMENT ON COLUMN chat_messages.is_message_from_me IS 'Indica se a mensagem foi enviada pelo bot';
COMMENT ON COLUMN chat_messages.is_group_message IS 'Indica se a mensagem é de um grupo';
COMMENT ON COLUMN chat_messages.is_forwarded_message IS 'Indica se a mensagem foi encaminhada';
COMMENT ON COLUMN chat_messages.is_from_bot IS 'Indica se a mensagem foi enviada por um bot';
COMMENT ON COLUMN chat_messages.message IS 'Conteúdo da mensagem';
COMMENT ON COLUMN chat_messages.message_author_name IS 'Nome do autor da mensagem';
COMMENT ON COLUMN chat_messages.message_author_phone_number IS 'Número de telefone do autor da mensagem';
COMMENT ON COLUMN chat_messages.media_mimetype IS 'Tipo MIME da mídia';
COMMENT ON COLUMN chat_messages.media_download_url IS 'URL para download da mídia';
COMMENT ON COLUMN chat_messages.media_filename IS 'Nome do arquivo de mídia';
COMMENT ON COLUMN chat_messages.media_caption IS 'Legenda da mídia';
COMMENT ON COLUMN chat_messages.sent_at IS 'Data e hora de envio da mensagem';
COMMENT ON COLUMN chat_messages.delivery_at IS 'Data e hora de entrega da mensagem';
COMMENT ON COLUMN chat_messages.read_at IS 'Data e hora de leitura da mensagem';
COMMENT ON COLUMN chat_messages.originating_message_uuid IS 'UUID da mensagem original (para mensagens encaminhadas)';
COMMENT ON COLUMN chat_messages.is_active IS 'Indica se a mensagem está ativa';
COMMENT ON COLUMN chat_messages.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN chat_messages.updated_at IS 'Data e hora da última atualização do registro'; 