-- Add external_message_id column to button_callbacks table
-- This column stores the Meta/WhatsApp external message ID returned when sending interactive messages
-- It's used to link button callbacks to the specific interactive message that generated them

ALTER TABLE button_callbacks 
ADD COLUMN external_message_id VARCHAR(255);

-- Add index for performance when searching by external_message_id
CREATE INDEX idx_button_callbacks_external_message_id 
ON button_callbacks(external_message_id);

-- Add composite index for the most common query pattern
CREATE INDEX idx_button_callbacks_external_message_id_status 
ON button_callbacks(external_message_id, status);
