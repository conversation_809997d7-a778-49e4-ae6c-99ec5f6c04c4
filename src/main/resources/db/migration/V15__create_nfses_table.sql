-- Migration: Create nfses table

-- Table: nfses
-- Records each invoice issued by a company to a customer.
CREATE TABLE IF NOT EXISTS nfses (
    -- Sequential table ID
    id BIGSERIAL PRIMARY KEY,
    
    -- Unique invoice identifier
    uuid UUID NOT NULL UNIQUE,
    
    -- Foreign key to the issuing company
    company_id BIGINT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Foreign key to the customer
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    
    -- Service description
    description TEXT NOT NULL,
    
    -- Service amount (with 2 decimal places precision)
    amount DECIMAL(10,2) NOT NULL,
    
    -- NFSe status (according to Focus API)
    status VARCHAR(30) NOT NULL,
    
    -- External process ID for tracking in external systems
    external_process_id VARCHAR,
    
    -- Whether the NFSe is active
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Creation date
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Last update date
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster lookups
CREATE INDEX idx_nfses_uuid ON nfses(uuid);
CREATE INDEX idx_nfses_company_id ON nfses(company_id);
CREATE INDEX idx_nfses_customer_id ON nfses(customer_id);
CREATE INDEX idx_nfses_status ON nfses(status);
CREATE INDEX idx_nfses_external_process_id ON nfses(external_process_id);

-- Add comment to the table
COMMENT ON TABLE nfses IS 'Records each invoice issued by a company to a customer';

-- Add comments to the columns
COMMENT ON COLUMN nfses.id IS 'Sequential table ID';
COMMENT ON COLUMN nfses.uuid IS 'Unique invoice identifier';
COMMENT ON COLUMN nfses.company_id IS 'Foreign key to the issuing company';
COMMENT ON COLUMN nfses.customer_id IS 'Foreign key to the customer';
COMMENT ON COLUMN nfses.description IS 'Service description';
COMMENT ON COLUMN nfses.amount IS 'Service amount (with 2 decimal places precision)';
COMMENT ON COLUMN nfses.status IS 'NFSe status (according to Focus API)';
COMMENT ON COLUMN nfses.external_process_id IS 'External process ID for tracking in external systems';
COMMENT ON COLUMN nfses.is_active IS 'Whether the NFSe is active';
COMMENT ON COLUMN nfses.created_at IS 'Creation date';
COMMENT ON COLUMN nfses.updated_at IS 'Last update date';
