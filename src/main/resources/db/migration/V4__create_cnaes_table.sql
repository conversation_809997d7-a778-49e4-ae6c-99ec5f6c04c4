-- Migration: Create CNAEs table

-- Table: cnaes
-- Stores all CNAE (Classificação Nacional de Atividades Econômicas) codes available in the system.
CREATE TABLE IF NOT EXISTS cnaes (
    -- Sequential table ID
    id BIGSERIAL PRIMARY KEY,
    
    -- Unique CNAE identifier
    uuid UUID NOT NULL UNIQUE,
    
    -- CNAE code
    code VARCHAR NOT NULL UNIQUE,
    
    -- CNAE description
    description VARCHAR NOT NULL,
    
    -- Annexo do Simples Nacional (1-5)
    simples_nacional_annexo INT,
    
    -- Whether the CNAE is active
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Creation date
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Last update date
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index on uuid for faster lookups
CREATE INDEX idx_cnaes_uuid ON cnaes(uuid);

-- Create index on code for faster lookups
CREATE INDEX idx_cnaes_code ON cnaes(code);

-- Add comment to the table
COMMENT ON TABLE cnaes IS 'Stores all CNAE (Classificação Nacional de Atividades Econômicas) codes available in the system';

-- Add comments to the columns
COMMENT ON COLUMN cnaes.id IS 'Sequential table ID';
COMMENT ON COLUMN cnaes.uuid IS 'Unique CNAE identifier';
COMMENT ON COLUMN cnaes.code IS 'CNAE code';
COMMENT ON COLUMN cnaes.description IS 'CNAE description';
COMMENT ON COLUMN cnaes.simples_nacional_annexo IS 'Annexo do Simples Nacional (1-5)';
COMMENT ON COLUMN cnaes.is_active IS 'Whether the CNAE is active';
COMMENT ON COLUMN cnaes.created_at IS 'Creation date';
COMMENT ON COLUMN cnaes.updated_at IS 'Last update date'; 