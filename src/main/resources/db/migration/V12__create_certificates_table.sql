-- Migration: Create certificates table
-- Description: Creates the certificates table to store digital certificates for companies

CREATE TABLE certificates (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID NOT NULL UNIQUE,
    company_id BIGINT NOT NULL,
    expiration_date DATE NOT NULL,
    blob_uuid VARCHAR NOT NULL,
    password VARCHAR,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- Add foreign key constraint
ALTER TABLE certificates 
ADD CONSTRAINT fk_certificates_company_id 
FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;

-- Índices para buscas comuns
CREATE INDEX idx_certificates_company_id ON certificates(company_id);
CREATE INDEX idx_certificates_is_active ON certificates(is_active);
CREATE INDEX idx_certificates_expiration_date ON certificates(expiration_date);
CREATE INDEX idx_certificates_blob_uuid ON certificates(blob_uuid);

-- Add comments for documentation
COMMENT ON TABLE certificates IS 'Stores digital certificates for companies';
COMMENT ON COLUMN certificates.id IS 'Sequential table ID';
COMMENT ON COLUMN certificates.uuid IS 'Unique certificate identifier';
COMMENT ON COLUMN certificates.company_id IS 'FK to the company that owns this certificate';
COMMENT ON COLUMN certificates.expiration_date IS 'Certificate expiration date';
COMMENT ON COLUMN certificates.blob_uuid IS 'UUID of the certificate file in BlobService';
COMMENT ON COLUMN certificates.password IS 'Certificate password (stored in plain text)';
COMMENT ON COLUMN certificates.is_active IS 'Indicates if this is the company active certificate';
COMMENT ON COLUMN certificates.created_at IS 'Creation timestamp';
COMMENT ON COLUMN certificates.updated_at IS 'Last update timestamp'; 