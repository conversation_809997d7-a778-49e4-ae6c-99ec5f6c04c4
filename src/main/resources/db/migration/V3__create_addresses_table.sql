-- Migration: Create addresses table

-- Table: addresses
-- Centralized table to store addresses for all entities in the system using a polymorphic approach.
CREATE TABLE IF NOT EXISTS addresses (
    -- Sequential table ID
    id BIGSERIAL PRIMARY KEY,
    
    -- Unique address identifier
    uuid UUID NOT NULL UNIQUE,
    
    -- Entity type ('user', 'company', 'customer', etc.)
    entity_type VARCHAR(20) NOT NULL,
    
    -- ID of the entity this address belongs to
    entity_id BIGINT NOT NULL,
    
    -- Street name
    street VARCHAR(255) NOT NULL,
    
    -- Street number
    number VARCHAR(20) NOT NULL,
    
    -- Additional information
    complement VARCHAR(255),
    
    -- Neighborhood
    neighborhood VARCHAR(255) NOT NULL,
    
    -- City
    city VARCHAR(255) NOT NULL,
    
    -- State code
    state VARCHAR(2) NOT NULL,
    
    -- ZIP code
    zip_code VARCHAR(10) NOT NULL,
    
    -- Indicates if this is the entity's primary address
    is_primary BOOLEAN NOT NULL DEFAULT false,
    
    -- Type of address (e.g., 'fiscal', 'correspondence', 'residential')
    address_type VARCHAR(50),
    
    -- Whether the address is active
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Creation date
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Last update date
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index on uuid for faster lookups
CREATE INDEX idx_addresses_uuid ON addresses(uuid);

-- Create composite index on entity_type and entity_id for faster lookups
CREATE INDEX idx_addresses_entity ON addresses(entity_type, entity_id);

-- Add comment to the table
COMMENT ON TABLE addresses IS 'Centralized table to store addresses for all entities in the system using a polymorphic approach';

-- Add comments to the columns
COMMENT ON COLUMN addresses.id IS 'Sequential table ID';
COMMENT ON COLUMN addresses.uuid IS 'Unique address identifier';
COMMENT ON COLUMN addresses.entity_type IS 'Entity type (''user'', ''company'', ''customer'', etc.)';
COMMENT ON COLUMN addresses.entity_id IS 'ID of the entity this address belongs to';
COMMENT ON COLUMN addresses.street IS 'Street name';
COMMENT ON COLUMN addresses.number IS 'Street number';
COMMENT ON COLUMN addresses.complement IS 'Additional information';
COMMENT ON COLUMN addresses.neighborhood IS 'Neighborhood';
COMMENT ON COLUMN addresses.city IS 'City';
COMMENT ON COLUMN addresses.state IS 'State code';
COMMENT ON COLUMN addresses.zip_code IS 'ZIP code';
COMMENT ON COLUMN addresses.is_primary IS 'Indicates if this is the entity''s primary address';
COMMENT ON COLUMN addresses.address_type IS 'Type of address (e.g., ''fiscal'', ''correspondence'', ''residential'')';
COMMENT ON COLUMN addresses.is_active IS 'Whether the address is active';
COMMENT ON COLUMN addresses.created_at IS 'Creation date';
COMMENT ON COLUMN addresses.updated_at IS 'Last update date'; 