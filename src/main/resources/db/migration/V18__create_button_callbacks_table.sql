-- Migration para criar a tabela button_callbacks
-- Responsável por armazenar callbacks de botões interativos

CREATE TABLE button_callbacks (
    id BIGSERIAL PRIMARY KEY,
    callback_id VARCHAR(255) NOT NULL UNIQUE,
    chat_uuid UUID NOT NULL,
    tool_name VARCHAR(255) NOT NULL,
    action_type VARCHAR(255) NOT NULL,
    context_data TEXT,
    expires_at TIMESTAMP NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    executed_at TIMESTAMP
);

-- Índices para melhorar performance
CREATE INDEX idx_button_callbacks_callback_id ON button_callbacks(callback_id);
CREATE INDEX idx_button_callbacks_chat_uuid ON button_callbacks(chat_uuid);
CREATE INDEX idx_button_callbacks_status ON button_callbacks(status);
CREATE INDEX idx_button_callbacks_expires_at ON button_callbacks(expires_at);
CREATE INDEX idx_button_callbacks_action_type ON button_callbacks(action_type);

-- Comentários para documentação
COMMENT ON TABLE button_callbacks IS 'Armazena callbacks de botões interativos para processamento posterior';
COMMENT ON COLUMN button_callbacks.callback_id IS 'ID único do callback (UUID)';
COMMENT ON COLUMN button_callbacks.chat_uuid IS 'UUID do chat relacionado';
COMMENT ON COLUMN button_callbacks.tool_name IS 'Nome da tool que criou o callback';
COMMENT ON COLUMN button_callbacks.action_type IS 'Tipo de ação a ser executada';
COMMENT ON COLUMN button_callbacks.context_data IS 'Dados de contexto em formato JSON';
COMMENT ON COLUMN button_callbacks.expires_at IS 'Data/hora de expiração do callback';
COMMENT ON COLUMN button_callbacks.status IS 'Status do callback: PENDING, EXECUTED, EXPIRED, CANCELLED';
COMMENT ON COLUMN button_callbacks.created_at IS 'Data/hora de criação';
COMMENT ON COLUMN button_callbacks.executed_at IS 'Data/hora de execução (quando aplicável)';
