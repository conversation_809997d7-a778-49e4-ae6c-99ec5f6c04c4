-- Migration: Create customers table

-- Table: customers
-- Stores the customers to whom companies provide services.
CREATE TABLE IF NOT EXISTS customers (
    -- Sequential table ID
    id BIGSERIAL PRIMARY KEY,
    
    -- Unique customer identifier
    uuid UUID NOT NULL UNIQUE,
    
    -- Foreign key to the company that registered the customer
    company_id BIGINT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Customer name
    name VARCHAR(255) NOT NULL,
    
    -- Customer tax ID (CPF/CNPJ)
    document VARCHAR(20) NOT NULL,
    
    -- Type of document (CPF or CNPJ)
    document_type VARCHAR(10) NOT NULL,
    
    -- Indicates if the customer is a foreigner
    is_foreigner BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Contact email
    email VARCHAR(255),
    
    -- Contact phone
    phone VARCHAR(20),
    
    -- Whether the customer is active
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Creation date
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Last update date
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON>reate indexes for faster lookups
CREATE INDEX idx_customers_uuid ON customers(uuid);
CREATE INDEX idx_customers_company_id ON customers(company_id);
CREATE INDEX idx_customers_document ON customers(document);

-- Add comment to the table
COMMENT ON TABLE customers IS 'Stores the customers to whom companies provide services';

-- Add comments to the columns
COMMENT ON COLUMN customers.id IS 'Sequential table ID';
COMMENT ON COLUMN customers.uuid IS 'Unique customer identifier';
COMMENT ON COLUMN customers.company_id IS 'Foreign key to the company that registered the customer';
COMMENT ON COLUMN customers.name IS 'Customer name';
COMMENT ON COLUMN customers.document IS 'Customer tax ID (CPF/CNPJ)';
COMMENT ON COLUMN customers.document_type IS 'Type of document (CPF or CNPJ)';
COMMENT ON COLUMN customers.is_foreigner IS 'Indicates if the customer is a foreigner';
COMMENT ON COLUMN customers.email IS 'Contact email';
COMMENT ON COLUMN customers.phone IS 'Contact phone';
COMMENT ON COLUMN customers.is_active IS 'Whether the customer is active';
COMMENT ON COLUMN customers.created_at IS 'Creation date';
COMMENT ON COLUMN customers.updated_at IS 'Last update date'; 