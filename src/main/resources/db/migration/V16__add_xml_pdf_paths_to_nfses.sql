-- Migration: Add XML and PDF blob UUIDs to nfses table

-- Add xml_blob_uuid column to reference the XML blob
ALTER TABLE nfses ADD COLUMN xml_blob_uuid UUID;

-- Add pdf_blob_uuid column to reference the PDF blob  
ALTER TABLE nfses ADD COLUMN pdf_blob_uuid UUID;

-- Add foreign key constraints to ensure referential integrity
ALTER TABLE nfses ADD CONSTRAINT fk_nfses_xml_blob 
    FOREIGN KEY (xml_blob_uuid) REFERENCES blobs(uuid);

ALTER TABLE nfses ADD CONSTRAINT fk_nfses_pdf_blob 
    FOREIGN KEY (pdf_blob_uuid) REFERENCES blobs(uuid);

-- Add comments to the new columns
COMMENT ON COLUMN nfses.xml_blob_uuid IS 'UUID reference to the NFSe XML file blob (when authorized)';
COMMENT ON COLUMN nfses.pdf_blob_uuid IS 'UUID reference to the NFSe PDF/DANFSE file blob (when authorized)'; 