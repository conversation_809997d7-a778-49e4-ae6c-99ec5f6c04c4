-- Migration: Create codigos_servico_municipais and companies_cnaes_servicos tables

-- Table: codigos_servico_municipais
-- Stores municipal service codes used in invoice issuance.
CREATE TABLE IF NOT EXISTS codigos_servico_municipais (
    -- Sequential table ID
    id BIGSERIAL PRIMARY KEY,
    
    -- Unique identifier
    uuid UUID NOT NULL UNIQUE,
    
    -- Service code
    codigo VARCHAR NOT NULL,
    
    -- Service description
    descricao VARCHAR NOT NULL,
    
    -- Municipality code
    codigo_municipio VARCHAR NOT NULL,
    
    -- Whether the service code is active
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Creation date
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Last update date
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure a municipality doesn't have duplicate service codes
    UNIQUE(codigo, codigo_municipio)
);

-- Create index on uuid for faster lookups
CREATE INDEX idx_codigos_servico_municipais_uuid ON codigos_servico_municipais(uuid);

-- Create index on codigo for faster lookups
CREATE INDEX idx_codigos_servico_municipais_codigo ON codigos_servico_municipais(codigo);

-- Create index on codigo_municipio for faster lookups
CREATE INDEX idx_codigos_servico_municipais_codigo_municipio ON codigos_servico_municipais(codigo_municipio);

-- Add comment to the table
COMMENT ON TABLE codigos_servico_municipais IS 'Stores municipal service codes used in invoice issuance';

-- Add comments to the columns
COMMENT ON COLUMN codigos_servico_municipais.id IS 'Sequential table ID';
COMMENT ON COLUMN codigos_servico_municipais.uuid IS 'Unique identifier';
COMMENT ON COLUMN codigos_servico_municipais.codigo IS 'Service code';
COMMENT ON COLUMN codigos_servico_municipais.descricao IS 'Service description';
COMMENT ON COLUMN codigos_servico_municipais.codigo_municipio IS 'Municipality code';
COMMENT ON COLUMN codigos_servico_municipais.is_active IS 'Whether the service code is active';
COMMENT ON COLUMN codigos_servico_municipais.created_at IS 'Creation date';
COMMENT ON COLUMN codigos_servico_municipais.updated_at IS 'Last update date';

-- Table: companies_cnaes_servicos
-- Junction table that associates companies with their CNAEs and municipal service codes.
CREATE TABLE IF NOT EXISTS companies_cnaes_servicos (
    -- Sequential table ID
    id BIGSERIAL PRIMARY KEY,
    
    -- Unique identifier
    uuid UUID NOT NULL UNIQUE,
    
    -- Foreign key to the company
    company_id BIGINT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    
    -- Foreign key to the CNAE
    cnae_id BIGINT NOT NULL REFERENCES cnaes(id) ON DELETE CASCADE,
    
    -- Foreign key to the municipal service code
    codigo_servico_id BIGINT NOT NULL REFERENCES codigos_servico_municipais(id) ON DELETE CASCADE,
    
    -- Indicates if this is the company's primary CNAE
    is_primary BOOLEAN NOT NULL DEFAULT false,
    
    -- Indicates if this CNAE is active for the company
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Creation date
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Last update date
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure a company cannot have the same combination of CNAE and service code multiple times
    UNIQUE(company_id, cnae_id, codigo_servico_id)
);

-- Create index on uuid for faster lookups
CREATE INDEX idx_companies_cnaes_servicos_uuid ON companies_cnaes_servicos(uuid);

-- Create index on company_id for faster lookups
CREATE INDEX idx_companies_cnaes_servicos_company_id ON companies_cnaes_servicos(company_id);

-- Create index on cnae_id for faster lookups
CREATE INDEX idx_companies_cnaes_servicos_cnae_id ON companies_cnaes_servicos(cnae_id);

-- Create index on codigo_servico_id for faster lookups
CREATE INDEX idx_companies_cnaes_servicos_codigo_servico_id ON companies_cnaes_servicos(codigo_servico_id);

-- Add comment to the table
COMMENT ON TABLE companies_cnaes_servicos IS 'Junction table that associates companies with their CNAEs and municipal service codes';

-- Add comments to the columns
COMMENT ON COLUMN companies_cnaes_servicos.id IS 'Sequential table ID';
COMMENT ON COLUMN companies_cnaes_servicos.uuid IS 'Unique identifier';
COMMENT ON COLUMN companies_cnaes_servicos.company_id IS 'Foreign key to the company';
COMMENT ON COLUMN companies_cnaes_servicos.cnae_id IS 'Foreign key to the CNAE';
COMMENT ON COLUMN companies_cnaes_servicos.codigo_servico_id IS 'Foreign key to the municipal service code';
COMMENT ON COLUMN companies_cnaes_servicos.is_primary IS 'Indicates if this is the company''s primary CNAE';
COMMENT ON COLUMN companies_cnaes_servicos.is_active IS 'Indicates if this CNAE is active for the company';
COMMENT ON COLUMN companies_cnaes_servicos.created_at IS 'Creation date';
COMMENT ON COLUMN companies_cnaes_servicos.updated_at IS 'Last update date'; 