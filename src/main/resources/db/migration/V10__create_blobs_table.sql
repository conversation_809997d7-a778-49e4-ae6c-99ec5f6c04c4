CREATE TABLE blobs (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID NOT NULL UNIQUE,
    content_type VARCHAR NOT NULL,
    content_category VARCHAR NOT NULL,
    filename VA<PERSON>HAR,
    size BIGINT NOT NULL,
    entity_type VA<PERSON>HA<PERSON>,
    entity_id VARCHAR,
    storage_type VA<PERSON>HAR NOT NULL,
    storage_path VARCHAR NOT NULL,
    last_accessed_at TIMESTAMP,
    metadata VARCHAR,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- Índices para buscas comuns
CREATE INDEX idx_blobs_entity ON blobs(entity_type, entity_id);
CREATE INDEX idx_blobs_content_type ON blobs(content_type);
CREATE INDEX idx_blobs_uuid ON blobs(uuid); 