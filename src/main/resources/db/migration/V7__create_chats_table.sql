-- <PERSON><PERSON><PERSON> da tabela de chats
CREATE TABLE chats (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID NOT NULL UNIQUE,
    user_phone_number VARCHAR(255) NOT NULL,
    conversational_window_started_at TIMESTAMP,
    meta_phone_number_id VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> da tabela de junção users_chats
CREATE TABLE users_chats (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID NOT NULL UNIQUE,
    user_id BIGINT NOT NULL REFERENCES users(id),
    chat_id BIGINT NOT NULL REFERENCES chats(id),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, chat_id)
);

-- Índices
CREATE INDEX idx_chats_user_phone_number ON chats(user_phone_number);
CREATE INDEX idx_chats_uuid ON chats(uuid);
CREATE INDEX idx_users_chats_user_id ON users_chats(user_id);
CREATE INDEX idx_users_chats_chat_id ON users_chats(chat_id);
CREATE INDEX idx_users_chats_uuid ON users_chats(uuid);

-- Comentários
COMMENT ON TABLE chats IS 'Tabela que armazena informações sobre conversas do WhatsApp';
COMMENT ON COLUMN chats.id IS 'Identificador único da conversa';
COMMENT ON COLUMN chats.uuid IS 'UUID único da conversa';
COMMENT ON COLUMN chats.user_phone_number IS 'Número de telefone do usuário com código do país';
COMMENT ON COLUMN chats.conversational_window_started_at IS 'Data e hora de início da janela conversacional';
COMMENT ON COLUMN chats.meta_phone_number_id IS 'ID do número de telefone da Meta';
COMMENT ON COLUMN chats.is_active IS 'Indica se o chat está ativo';
COMMENT ON COLUMN chats.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN chats.updated_at IS 'Data e hora da última atualização do registro';

COMMENT ON TABLE users_chats IS 'Tabela de junção que relaciona usuários e chats';
COMMENT ON COLUMN users_chats.id IS 'Identificador único da relação';
COMMENT ON COLUMN users_chats.uuid IS 'UUID único da relação';
COMMENT ON COLUMN users_chats.user_id IS 'ID do usuário';
COMMENT ON COLUMN users_chats.chat_id IS 'ID do chat';
COMMENT ON COLUMN users_chats.is_active IS 'Indica se a relação está ativa';
COMMENT ON COLUMN users_chats.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN users_chats.updated_at IS 'Data e hora da última atualização do registro'; 