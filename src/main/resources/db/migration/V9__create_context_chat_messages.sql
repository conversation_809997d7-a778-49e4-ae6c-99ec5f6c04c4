CREATE TABLE context_chat_messages (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID NOT NULL UNIQUE,
    chat_id BIGINT NOT NULL REFERENCES chats(id),
    role VARCHAR(10) NOT NULL,
    content TEXT NOT NULL,
    external_message_id VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_context_chat_messages_chat_id ON context_chat_messages(chat_id);
CREATE INDEX idx_context_chat_messages_uuid ON context_chat_messages(uuid);