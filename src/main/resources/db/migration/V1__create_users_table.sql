-- Migration: Create users table

-- Table: users
-- Stores the users who access the system.
CREATE TABLE IF NOT EXISTS users (
    -- Sequential table ID
    id BIGSERIAL PRIMARY KEY,
    
    -- Unique user identifier
    uuid UUID NOT NULL UNIQUE,
    
    -- User's name
    name VA<PERSON>HAR(255) NOT NULL,
    
    -- <PERSON>ail (used for login)
    email VARCHAR(255) NOT NULL UNIQUE,
    
    -- User's document (CPF)
    document VARCHAR(20),
    
    -- Password (hashed)
    password TEXT NOT NULL,
    
    -- Whether the user is active
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Creation date
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Last update date
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index on uuid for faster lookups (if not exists)
CREATE INDEX idx_users_uuid ON users(uuid);

-- <PERSON>reate index on email for faster lookups (if not exists)
CREATE INDEX idx_users_email ON users(email);

-- Add comment to the table (comments are idempotent)
COMMENT ON TABLE users IS 'Stores the users who access the system';

-- Add comments to the columns (comments are idempotent)
COMMENT ON COLUMN users.id IS 'Sequential table ID';
COMMENT ON COLUMN users.uuid IS 'Unique user identifier';
COMMENT ON COLUMN users.name IS 'User''s name';
COMMENT ON COLUMN users.email IS 'Email (used for login)';
COMMENT ON COLUMN users.document IS 'User''s document (CPF)';
COMMENT ON COLUMN users.password IS 'Password (hashed)';
COMMENT ON COLUMN users.is_active IS 'Whether the user is active';
COMMENT ON COLUMN users.created_at IS 'Creation date';
COMMENT ON COLUMN users.updated_at IS 'Last update date';