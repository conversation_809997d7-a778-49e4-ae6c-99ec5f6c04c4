-- Migration: Create companies table

-- Table: companies
-- Stores information about companies in the system.
CREATE TABLE IF NOT EXISTS companies (
    -- Sequential table ID
    id BIGSERIAL PRIMARY KEY,
    
    -- Unique company identifier
    uuid UUID NOT NULL UNIQUE,
    
    -- Company name
    name VARCHAR(255) NOT NULL,
    
    -- Company document (CNPJ)
    document VARCHAR(20) NOT NULL UNIQUE,
    
    -- Municipal registration (Inscrição Municipal)
    municipal_registration VARCHAR(50),
    
    -- State registration (Inscrição Estadual)
    state_registration VARCHAR(50),
    
    -- Indicates if the company is enrolled in Simples Nacional
    simples_nacional BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Whether the company is active
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Creation date
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Last update date
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index on uuid for faster lookups
CREATE INDEX idx_companies_uuid ON companies(uuid);

-- Create index on document for faster lookups
CREATE INDEX idx_companies_document ON companies(document);

-- Add comment to the table
COMMENT ON TABLE companies IS 'Stores information about companies in the system';

-- Add comments to the columns
COMMENT ON COLUMN companies.id IS 'Sequential table ID';
COMMENT ON COLUMN companies.uuid IS 'Unique company identifier';
COMMENT ON COLUMN companies.name IS 'Company name';
COMMENT ON COLUMN companies.document IS 'Company document (CNPJ)';
COMMENT ON COLUMN companies.municipal_registration IS 'Municipal registration (Inscrição Municipal)';
COMMENT ON COLUMN companies.state_registration IS 'State registration (Inscrição Estadual)';
COMMENT ON COLUMN companies.simples_nacional IS 'Indicates if the company is enrolled in Simples Nacional';
COMMENT ON COLUMN companies.is_active IS 'Whether the company is active';
COMMENT ON COLUMN companies.created_at IS 'Creation date';
COMMENT ON COLUMN companies.updated_at IS 'Last update date'; 
