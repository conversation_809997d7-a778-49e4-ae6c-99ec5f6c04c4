management:
  metrics:
    export:
      statsd:
        enabled: true
        host: localhost
        port: 8125
        flavor: datadog

server:
  port: ${SERVER_PORT:8080}

# LLM related config
llm:
  openai:
    api-key: ${LLM_OPENAI_API_KEY}
  gemini:
    api-key: ${LLM_GEMINI_API_KEY}
    base-url: https://generativelanguage.googleapis.com

spring:
  output:
    ansi:
      enabled: ALWAYS
  jackson:
    deserialization:
      fail-on-unknown-properties: false # Disabling failing on JSON unknown properties for now
  application:
    name: api

  # Connection to database
  datasource:
    url: ${SPRING_DATASOURCE_URL:**************************************}
    username: ${SPRING_DATASOURCE_USERNAME:jafai}
    password: ${SPRING_DATASOURCE_PASSWORD:jafai1234}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
  
  # Flyway configuration
  flyway:
    enabled: true
    schemas: jafai
    create-schemas: true
    baseline-on-migrate: true
    # if there are divergences (different checksum, the migration won't be applied)
    validate-on-migrate: true
    locations: classpath:db/migration

focus:
  api:
    url: https://homologacao.focusnfe.com.br
    token_principal: ${FOCUS_TOKEN_PRINCIPAL}

whatsapp:
  flows:
    private-key-path: ${WHATSAPP_FLOWS_PRIVATE_KEY_PATH:keys/private_unencrypted.pem}
  api:
    base-url: https://graph.facebook.com
    token: ${WHATSAPP_API_TOKEN}
    version: v22.0
    phone-number-id: ${WHATSAPP_PHONE_NUMBER_ID}

slack:
  token: ${SLACK_TOKEN:*********************************************************}
  support:
    channel: ${SLACK_SUPPORT_CHANNEL:#support}
  username: ${SLACK_USERNAME:Jafinho}

stripe:
  api:
    secret-key: ${STRIPE_SECRET_KEY:sk_test_51Rb5VWQttxQ8VREdZ5b5Q9R7piyuju5Cq4hywGY9lmkyieGSKSDDGeuOyd2tCo9NBwJsFywps6oV75fAzX4C3zLU004t7vKMVl}
    publishable-key: ${STRIPE_PUBLISHABLE_KEY:pk_test_51Rb5VWQttxQ8VREdbTYKUjyR7badFFWlHrDpAOGidyYTC0cFDb8DuVwMc8KWGAVSeDQGMqQYhu7Psbcmriow8KLS00CXq9g851}
  webhook:
    endpoint-secret: ${STRIPE_WEBHOOK_SECRET:whsec_0yVulV0dAe49PCznMYOZyAhwhvgBqlIt}
  urls:
    success: ${STRIPE_SUCCESS_URL:https://contacomjaf.com.br/payment/success}
    cancel: ${STRIPE_CANCEL_URL:https://contacomjaf.com.br/payment/cancel}

aws:
  region: us-east-1
  access-key-id: ${AWS_ACCESS_KEY_ID}
  secret-access-key: ${AWS_SECRET_ACCESS_KEY}

logging:
  config: classpath:logback.xml
  level:
    br.com.mecontaai.api.infrastructure.client.focus: DEBUG
    feign: DEBUG

receitaws:
  api:
    url: https://receitaws.com.br

infosimples:
  api:
    url: https://api.infosimples.com
    token: ${INFOSIMPLES_TOKEN}