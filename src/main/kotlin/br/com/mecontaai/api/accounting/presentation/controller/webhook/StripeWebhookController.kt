package br.com.mecontaai.api.accounting.presentation.controller.webhook

import com.fasterxml.jackson.databind.ObjectMapper
import com.stripe.exception.SignatureVerificationException
import com.stripe.net.Webhook
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * Controller para receber webhooks do Stripe.
 *
 * Os webhooks do Stripe são enviados para notificar sobre eventos como:
 * - Pagamentos bem-sucedidos
 * - Falhas de pagamento
 * - Cancelamentos de assinatura
 * - Atualizações de status
 */
@RestController
@RequestMapping("/api/v1/webhooks/stripe")
class StripeWebhookController(
    private val objectMapper: ObjectMapper,
    @Value("\${stripe.webhook.endpoint-secret}")
    private val webhookSecret: String,
) {
    private val logger = LoggerFactory.getLogger(StripeWebhookController::class.java)

    /**
     * Valida a assinatura do webhook do Stripe para garantir autenticidade.
     */
    private fun validateWebhookSignature(
        payload: String,
        signature: String?,
    ): Boolean {
        if (signature.isNullOrBlank()) {
            logger.error("❌ Stripe-Signature header ausente")
            return false
        }

        return try {
            // Usa a biblioteca do Stripe para validar a assinatura
            Webhook.constructEvent(payload, signature, webhookSecret)
            logger.debug("✅ Assinatura do webhook validada com sucesso")
            true
        } catch (e: SignatureVerificationException) {
            logger.error("❌ Falha na validação da assinatura do webhook: ${e.message}")
            false
        } catch (e: Exception) {
            logger.error("❌ Erro inesperado na validação da assinatura: ${e.message}")
            false
        }
    }

    /**
     * Endpoint principal para receber webhooks snapshot do Stripe.
     */
    @PostMapping("/snapshot")
    fun receiveStripeSnapshotWebhook(
        @RequestBody payload: String,
        @RequestHeader("Stripe-Signature", required = false) stripeSignature: String?,
    ): ResponseEntity<Unit> {
        return try {
            // Valida a assinatura do webhook
            if (!validateWebhookSignature(payload, stripeSignature)) {
                logger.error("❌ Webhook rejeitado - assinatura inválida")
                return ResponseEntity.badRequest().build()
            }

            // Parse do JSON e converte para uma linha compacta
            val webhookData = objectMapper.readTree(payload)
            val compactPayload = objectMapper.writeValueAsString(webhookData)

            // Log do payload completo em uma única linha
            logger.info("🛜 Stripe snapshot webhook: $compactPayload")

            // TODO: Processar o webhook (enviar para fila, chamar use case, etc.)

            ResponseEntity.ok().build()
        } catch (ex: Exception) {
            logger.error("❌ Erro ao processar snapshot webhook do Stripe", ex)
            logger.error("📦 Payload que causou erro: $payload")
            ResponseEntity.internalServerError().build()
        }
    }

    /**
     * Endpoint principal para receber webhooks thin do Stripe.
     */
    @PostMapping("/thin")
    fun receiveStripeThinWebhook(
        @RequestBody payload: String,
        @RequestHeader("Stripe-Signature", required = false) stripeSignature: String?,
    ): ResponseEntity<Unit> {
        return try {
            // Valida a assinatura do webhook
            if (!validateWebhookSignature(payload, stripeSignature)) {
                logger.error("❌ Webhook rejeitado - assinatura inválida")
                return ResponseEntity.badRequest().build()
            }

            // Parse do JSON e converte para uma linha compacta
            val webhookData = objectMapper.readTree(payload)
            val compactPayload = objectMapper.writeValueAsString(webhookData)

            // Log do payload completo em uma única linha
            logger.info("🛜 Stripe thin webhook: $compactPayload")

            // TODO: Processar o webhook (enviar para fila, chamar use case, etc.)

            ResponseEntity.ok().build()
        } catch (ex: Exception) {
            logger.error("❌ Erro ao processar thin webhook do Stripe", ex)
            logger.error("📦 Payload que causou erro: $payload")
            ResponseEntity.internalServerError().build()
        }
    }
}
