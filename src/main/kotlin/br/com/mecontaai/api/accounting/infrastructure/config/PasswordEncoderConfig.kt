package br.com.mecontaai.api.accounting.infrastructure.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder

/**
 * Configuration for password encoding.
 */
@Configuration
class PasswordEncoderConfig {

    /**
     * Bean for BCryptPasswordEncoder.
     * 
     * @return an instance of BCryptPasswordEncoder
     */
    @Bean
    fun bCryptPasswordEncoder(): BCryptPasswordEncoder {
        return BCryptPasswordEncoder()
    }
} 