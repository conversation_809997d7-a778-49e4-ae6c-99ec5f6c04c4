package br.com.mecontaai.api.accounting.infrastructure.service.MessageSenderServiceImpl

import br.com.mecontaai.api.accounting.domain.service.MessageSenderService
import br.com.mecontaai.api.infrastructure.client.whatsapp.dto.WhatsAppInteractiveMessageDto
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import java.util.UUID

/**
 * Implementação mock do MessageSenderService para ambientes de desenvolvimento e testes.
 * Apenas loga as mensagens ao invés de enviá-las para um provedor real.
 */
@Service
@Profile("!production & !staging")
class MockMessageSenderServiceImpl : MessageSenderService {
    private val logger = LoggerFactory.getLogger(MockMessageSenderServiceImpl::class.java)

    override fun sendTextMessage(to: String, text: String): String {
        val messageId = generateMockMessageId()
        logger.info("MOCK: Enviando mensagem de texto para $to")
        logger.info("MOCK: Conteúdo: $text")
        logger.info("MOCK: Message ID: $messageId")
        return messageId
    }

    override fun sendImageMessage(to: String, imageId: String, caption: String?): String {
        val messageId = generateMockMessageId()
        logger.info("MOCK: Enviando mensagem com imagem para $to")
        logger.info("MOCK: Image ID: $imageId")
        logger.info("MOCK: Caption: ${caption ?: "sem legenda"}")
        logger.info("MOCK: Message ID: $messageId")
        return messageId
    }

    override fun sendImageMessageWithUrl(to: String, imageUrl: String, caption: String?): String {
        val messageId = generateMockMessageId()
        logger.info("MOCK: Enviando mensagem com imagem via URL para $to")
        logger.info("MOCK: Image URL: $imageUrl")
        logger.info("MOCK: Caption: ${caption ?: "sem legenda"}")
        logger.info("MOCK: Message ID: $messageId")
        return messageId
    }

    override fun sendDocumentMessage(to: String, documentUrl: String, caption: String?): String {
        val messageId = generateMockMessageId()
        logger.info("MOCK: Enviando mensagem com documento via URL para $to")
        logger.info("MOCK: Document URL: $documentUrl")
        logger.info("MOCK: Caption: ${caption ?: "sem legenda"}")
        logger.info("MOCK: Message ID: $messageId")
        return messageId
    }

    /**
     * Mock do envio de mensagens interativas.
     *
     * @param to Número de telefone do destinatário
     * @param payload Dados da mensagem interativa
     * @return Um identificador mock da mensagem gerado
     */
    override fun sendInteractiveMessage(to: String, payload: WhatsAppInteractiveMessageDto): String {
        val messageId = generateMockMessageId()
        logger.info("MOCK: Enviando mensagem interativa para $to")
        logger.info("MOCK: Payload: $payload")
        logger.info("MOCK: Message ID: $messageId")
        return messageId
    }

    private fun generateMockMessageId(): String {
        return "mock.${UUID.randomUUID()}"
    }
} 