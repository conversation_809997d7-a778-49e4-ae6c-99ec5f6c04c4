package br.com.mecontaai.api.accounting.infrastructure.service.MessageSenderServiceImpl

import br.com.mecontaai.api.accounting.domain.service.MessageSenderService
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.WhatsAppClient
import br.com.mecontaai.api.infrastructure.client.whatsapp.dto.WhatsAppInteractiveMessageDto
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

/**
 * Implementação do MessageSenderService para o WhatsApp.
 */
@Service
@Profile("production", "staging")
class WhatsAppMessageSenderServiceImpl(
    @Value("\${whatsapp.api.phone-number-id}")
    private val phoneNumberId: String,
    private val whatsAppClient: WhatsAppClient
) : MessageSenderService {

    override fun sendTextMessage(to: String, text: String): String {
        val parts = text.split("\n\n")
        val ids = mutableListOf<String>()
        for (part in parts) {
            val response = whatsAppClient.sendMessage(
                phoneNumberId = phoneNumberId,
                to = to,
                text = part.trim()
            )
            ids.add(response.messages.first().id)
        }
        return ids.first() // Retorna o ID da primeira mensagem enviada
    }

    override fun sendImageMessage(to: String, imageId: String, caption: String?): String {
        val response = whatsAppClient.sendImageMessage(
            phoneNumberId = phoneNumberId,
            to = to,
            imageId = imageId,
            caption = caption
        )
        return response.messages.first().id
    }

    override fun sendImageMessageWithUrl(to: String, imageUrl: String, caption: String?): String {
        val response = whatsAppClient.sendImageMessageWithUrl(
            phoneNumberId = phoneNumberId,
            to = to,
            imageUrl = imageUrl,
            caption = caption
        )
        return response.messages.first().id
    }

    override fun sendDocumentMessage(to: String, documentUrl: String, caption: String?): String {
        val response = whatsAppClient.sendDocumentMessageWithUrl(
            phoneNumberId = phoneNumberId,
            to = to,
            documentUrl = documentUrl,
            caption = caption
        )
        return response.messages.first().id
    }

    override fun sendInteractiveMessage(to: String, payload: WhatsAppInteractiveMessageDto): String {
        // Log para acompanhamento do envio
        println("Enviando mensagem interativa para o WhatsApp com o destinatário $to e payload $payload")

        // Envia a mensagem interativa usando um WhatsAppClient
        val response = whatsAppClient.sendInteractiveMessage(
            phoneNumberId = phoneNumberId,
            to = to,
            payload = payload
        )

        // Retorna o ID único da primeira mensagem na resposta
        return response.messages.first().id
    }
} 