package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*

/**
 * Entity class representing a company in the database.
 */
@Entity
@Table(name = "companies")
class CompanyEntity(
    @Column(nullable = false)
    val name: String,

    @Column(nullable = false, unique = true)
    val document: String,

    @Column(name = "municipal_registration")
    val municipalRegistration: String? = null,

    @Column(name = "state_registration")
    val stateRegistration: String? = null,

    @Column(name = "simples_nacional")
    val simplesNacional: Boolean = false,

    @Column(name = "codigo_municipio")
    val codigoMunicipio: String? = null,

    @Column(name = "codigo_siafi")
    val codigoSiafi: String? = null,

    @Column(name = "codigo_ibge")
    val codigoIbge: String? = null,

    @Column(name = "natureza_juridica")
    @Enumerated(EnumType.STRING)
    val naturezaJuridica: String? = null,

    @Column(name = "token_producao")
    var tokenProducao: String? = null,

    @Column(name = "focus_id")
    var focusId: String? = null,

    @Column(name = "token_homologacao")
    var tokenHomologacao: String? = null
) : BaseEntity()