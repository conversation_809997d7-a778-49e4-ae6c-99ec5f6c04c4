package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.enum.MetaWebhookType
import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.service.ChatService
import br.com.mecontaai.api.accounting.domain.service.MessageManagerService
import br.com.mecontaai.api.accounting.domain.service.MetaWebhookProcessor
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.webhook.model.InteractivePayload
import br.com.mecontaai.api.accounting.infrastructure.webhook.model.ProcessingResult
import br.com.mecontaai.api.accounting.infrastructure.webhook.strategy.InteractionStrategyRegistry
import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Processor for handling interactive message webhooks from Meta.
 * Uses Strategy Pattern to delegate processing to appropriate handlers
 * based on the type of interaction (button callbacks, quick replies, etc.).
 */
@Component
class InteractiveMetaWebhookProcessor(
    private val strategyRegistry: InteractionStrategyRegistry,
    private val chatService: ChatService,
    private val messageManagerService: MessageManagerService,
) : MetaWebhookProcessor {
    private val logger = LoggerFactory.getLogger(InteractiveMetaWebhookProcessor::class.java)

    override val webhookType: MetaWebhookType = MetaWebhookType.INTERACTIVE

    override fun canProcess(payload: MetaWebhookPayload): Boolean =
        payload.entry.any { entry ->
            entry.changes.any { change ->
                change.field == "messages" &&
                    change.value.messages?.any { message ->
                        message.type == "interactive" &&
                            (message.interactive?.buttonReply != null || message.interactive?.nfmReply != null)
                    } == true
            }
        }

    override fun process(payload: MetaWebhookPayload) {
        logger.info("🔄 Processando webhook interativo para entry: {}", payload.getEntryId())

        payload.entry.forEach { entry ->
            entry.changes.forEach { change ->
                if (change.field == "messages") {
                    change.value.messages?.forEach { message ->
                        if (message.type == "interactive") {
                            processInteractiveMessage(message, change.value.metadata?.phoneNumberId ?: "")
                        }
                    }
                }
            }
        }

        logger.info("✅ Webhook interativo processado para entry: {}", payload.getEntryId())
    }

    /**
     * Processa uma mensagem interativa específica.
     */
    private fun processInteractiveMessage(
        message: MetaWebhookPayload.Message,
        metaPhoneNumberId: String,
    ) {
        logger.info("🎯 Processando mensagem interativa")
        logger.info("📱 From: ${message.from}")
        logger.info("📞 Meta Phone Number ID: $metaPhoneNumberId")
        logger.info("🔍 Interactive: ${message.interactive}")

        try {
            // Extrai o payload da interação
            val interaction = InteractivePayload.fromMetaWebhook(message)
            logger.info("📦 Interação extraída: $interaction")

            // Sanitiza o número de telefone e busca ou cria o chat
            val sanitizedPhoneNumber = sanitizePhoneNumber(message.from ?: "")
            val chat =
                chatService.findOrCreateChat(
                    userPhoneNumber = sanitizedPhoneNumber,
                    metaPhoneNumberId = metaPhoneNumberId,
                )
            logger.info("💬 Chat: ${chat.uuid}")

            // Encontra a estratégia apropriada
            val strategy = strategyRegistry.findStrategy(interaction.type)
            if (strategy == null) {
                logger.error("❌ Nenhuma estratégia encontrada para tipo: ${interaction.type}")
                sendFallbackMessage(chat, "Tipo de interação não suportado.")
                return
            }

            logger.info("🎯 Usando estratégia: ${strategy::class.simpleName}")

            // Processa a interação
            val result = strategy.process(interaction, chat)
            handleProcessingResult(result, chat)
        } catch (e: Exception) {
            logger.error("❌ Erro ao processar mensagem interativa", e)

            // Tenta buscar o chat para enviar mensagem de erro
            try {
                val sanitizedPhoneNumber = sanitizePhoneNumber(message.from ?: "")
                val chat =
                    chatService.findOrCreateChat(
                        userPhoneNumber = sanitizedPhoneNumber,
                        metaPhoneNumberId = metaPhoneNumberId,
                    )
                sendFallbackMessage(chat, "Ocorreu um erro ao processar sua solicitação.")
            } catch (chatError: Exception) {
                logger.error("❌ Erro ao buscar chat para envio de mensagem de erro", chatError)
            }
        }
    }

    /**
     * Trata o resultado do processamento da interação.
     */
    private fun handleProcessingResult(
        result: ProcessingResult,
        chat: ChatEntity,
    ) {
        when (result) {
            is ProcessingResult.Success -> {
                logger.info("✅ Processamento bem-sucedido, enviando ${result.responses.size} resposta(s)")
                if (result.responses.isNotEmpty()) {
                    messageManagerService.sendAgentResponses(chat, result.responses)
                }
            }

            is ProcessingResult.Fallback -> {
                logger.warn("⚠️ Processamento com fallback: ${result.message}")
                sendFallbackMessage(chat, result.message)
            }

            is ProcessingResult.Error -> {
                logger.error("❌ Erro no processamento: ${result.error}", result.cause)
                sendFallbackMessage(chat, "Ocorreu um erro ao processar sua solicitação.")
            }
        }
    }

    /**
     * Sanitiza o número de telefone garantindo que tenha "+" no início.
     */
    private fun sanitizePhoneNumber(phoneNumber: String): String {
        if (phoneNumber.isBlank()) {
            logger.warn("⚠️ Número de telefone vazio recebido")
            return phoneNumber
        }

        val sanitized = if (phoneNumber.startsWith("+")) {
            phoneNumber
        } else {
            "+$phoneNumber"
        }

        logger.debug("📞 Número sanitizado: '$phoneNumber' → '$sanitized'")
        return sanitized
    }

    /**
     * Envia uma mensagem de fallback para o usuário.
     */
    private fun sendFallbackMessage(
        chat: ChatEntity,
        message: String,
    ) {
        try {
            messageManagerService.sendAgentResponse(
                chat,
                AgentResponse.TextResponse(message),
            )
            logger.info("📤 Mensagem de fallback enviada: $message")
        } catch (e: Exception) {
            logger.error("❌ Erro ao enviar mensagem de fallback", e)
        }
    }
}
