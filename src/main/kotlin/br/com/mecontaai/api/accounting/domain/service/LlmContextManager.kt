package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.dto.LlmResponse
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.ContextChatMessage
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Component responsible for managing LLM conversation context.
 * Handles the persistence of temporary context messages generated during LLM processing.
 */
@Component
class LlmContextManager(
    private val contextChatMessageService: ContextChatMessageService,
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(LlmContextManager::class.java)

    /**
     * Saves the temporary context messages from LLM response.
     *
     * @param llmResponse The LLM response containing temporary context messages
     * @param chat The chat entity where the context belongs
     * @throws LlmContextException if context saving fails
     */
    fun saveResponseContext(
        llmResponse: LlmResponse,
        chat: ChatEntity,
    ) {
        logger.debug("Saving LLM response context for chat: {}", chat.uuid)

        val tempContextMessages = llmResponse.tempContextMessages

        if (tempContextMessages.isEmpty()) {
            logger.debug("No temporary context messages to save")
            return
        }

        var savedCount = 0
        var errorCount = 0

        tempContextMessages.forEach { tempChatMessage ->
            try {
                val chatMessageJsonStr = objectMapper.writeValueAsString(tempChatMessage)

                val contextChatMessage =
                    ContextChatMessage(
                        chatId = chat.id!!,
                        role = tempChatMessage.role.name.lowercase(),
                        content = chatMessageJsonStr,
                        externalMessageId = null,
                    )

                val savedContextChatMessage = contextChatMessageService.save(contextChatMessage)
                logger.debug("Temporary context message saved: {}", savedContextChatMessage.uuid)
                savedCount++
            } catch (e: Exception) {
                logger.error("Error saving temporary context message: ${e.message}", e)
                errorCount++
            }
        }

        logger.info("Context saving completed - Saved: $savedCount, Errors: $errorCount")

        if (errorCount > 0 && savedCount == 0) {
            throw LlmContextException("Failed to save any context messages for chat ${chat.uuid}")
        }
    }
}

/**
 * Exception thrown when LLM context management fails.
 */
class LlmContextException(
    message: String,
    cause: Throwable? = null,
) : Exception(message, cause)
