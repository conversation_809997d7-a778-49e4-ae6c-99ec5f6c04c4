package br.com.mecontaai.api.accounting.infrastructure.notification

import br.com.mecontaai.api.accounting.domain.model.notification.SupportNotification
import br.com.mecontaai.api.accounting.domain.service.notification.NotificationService
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

/**
 * Implementação do NotificationService para Slack.
 */
@Service
class SlackNotificationService(
    @Value("\${slack.token}")
    private val slackToken: String,
    @Value("\${slack.support.channel:#support}")
    private val supportChannel: String,
    @Value("\${slack.username:<PERSON><PERSON><PERSON><PERSON>}")
    private val username: String,
    private val restTemplate: RestTemplate,
    private val objectMapper: ObjectMapper,
) : NotificationService {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val slackApiUrl = "https://slack.com/api/chat.postMessage"

    override fun sendSupportNotification(
        userPhone: String,
        reason: String?,
        chatUuid: String,
        userName: String?,
    ) {
        if (slackToken.isBlank()) {
            logger.warn("Slack token não configurado. Notificação de suporte não será enviada.")
            return
        }

        val notification =
            SupportNotification(
                userPhone = userPhone,
                userName = userName,
                reason = reason,
                chatUuid = chatUuid,
            )

        val message = notification.formatMessage()

        logger.info("Enviando notificação de suporte para Slack")
        logger.info("Canal: $supportChannel")
        logger.info("Usuário: $userPhone")
        logger.info("Motivo: $reason")

        sendSlackMessage(message, supportChannel)
    }

    override fun sendNotification(
        message: String,
        channel: String,
    ) {
        if (slackToken.isBlank()) {
            logger.warn("Slack token não configurado. Notificação não será enviada.")
            return
        }

        sendSlackMessage(message, channel)
    }

    private fun sendSlackMessage(
        message: String,
        channel: String,
    ) {
        try {
            val headers =
                HttpHeaders().apply {
                    contentType = MediaType.APPLICATION_JSON
                    setBearerAuth(slackToken)
                }

            val payload =
                mapOf(
                    "channel" to channel,
                    "text" to message,
                    "username" to username,
                )

            val request = HttpEntity(objectMapper.writeValueAsString(payload), headers)

            val response =
                restTemplate.exchange(
                    slackApiUrl,
                    HttpMethod.POST,
                    request,
                    String::class.java,
                )

            if (response.statusCode.is2xxSuccessful) {
                logger.info("✅ Notificação Slack enviada com sucesso para $channel")
            } else {
                logger.error("❌ Erro ao enviar notificação Slack. Status: ${response.statusCode}")
                logger.error("Response: ${response.body}")
            }
        } catch (e: Exception) {
            logger.error("❌ Erro ao enviar notificação para Slack", e)
        }
    }
}
