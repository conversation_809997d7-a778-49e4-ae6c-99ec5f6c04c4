package br.com.mecontaai.api.accounting.infrastructure.repository

import br.com.mecontaai.api.accounting.domain.entity.User
import br.com.mecontaai.api.accounting.domain.repository.UserRepository
import br.com.mecontaai.api.accounting.infrastructure.database.model.UserEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.UserEntityRepository
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Implementation of UserRepository using JPA.
 */
@Repository
class UserRepositoryImpl(
    private val userEntityRepository: UserEntityRepository
) : UserRepository {

    /**
     * Save a user to the database.
     *
     * @param user The user to save
     * @return The saved user with generated ID
     */
    override fun save(user: User): User {
        val userEntity = mapToEntity(user)
        val savedEntity = userEntityRepository.save(userEntity)
        return mapToDomain(savedEntity)
    }

    /**
     * Find a user by ID.
     *
     * @param id The user ID
     * @return The user if found, null otherwise
     */
    override fun findById(id: Long): User? {
        return userEntityRepository.findById(id).map { mapToDomain(it) }.orElse(null)
    }

    /**
     * Find a user by UUID.
     *
     * @param uuid The user UUID
     * @return The user if found, null otherwise
     */
    override fun findByUuid(uuid: UUID): User? {
        return userEntityRepository.findByUuid(uuid)?.let { mapToDomain(it) }
    }

    /**
     * Find a user by email.
     *
     * @param email The user email
     * @return The user if found, null otherwise
     */
    override fun findByEmail(email: String): User? {
        return userEntityRepository.findByEmail(email)?.let { mapToDomain(it) }
    }

    /**
     * Check if a user with the given email exists.
     *
     * @param email The email to check
     * @return true if a user with the email exists, false otherwise
     */
    override fun existsByEmail(email: String): Boolean {
        return userEntityRepository.existsByEmail(email)
    }

    /**
     * Check if a user with the given document exists.
     *
     * @param document The document to check
     * @return true if a user with the document exists, false otherwise
     */
    override fun existsByDocument(document: String): Boolean {
        // If document is null or empty, consider it as not existing
        if (document.isNullOrBlank()) {
            return false
        }
        return userEntityRepository.existsByDocument(document)
    }

    /**
     * Map a domain User to a database UserEntity.
     */
    private fun mapToEntity(user: User): UserEntity {
        return UserEntity(
            name = user.name,
            email = user.email,
            password = user.password,
            document = user.document
        )
        // Os campos de BaseEntity (id, uuid, isActive, createdAt, updatedAt) 
        // são inicializados automaticamente na construção da UserEntity
    }

    /**
     * Map a database UserEntity to a domain User.
     */
    private fun mapToDomain(entity: UserEntity): User {
        return User(
            id = entity.id,
            uuid = entity.uuid,
            name = entity.name,
            email = entity.email,
            password = entity.password,
            document = entity.document,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
} 