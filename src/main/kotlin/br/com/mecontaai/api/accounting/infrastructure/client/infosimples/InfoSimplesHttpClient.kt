package br.com.mecontaai.api.accounting.infrastructure.client.infosimples

import br.com.mecontaai.api.accounting.infrastructure.client.infosimples.dto.InfoSimplesResponseDto
import br.com.mecontaai.api.accounting.infrastructure.config.InfoSimplesConfig
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import feign.Feign
import feign.Headers
import feign.Param
import feign.RequestLine
import feign.Response
import feign.codec.ErrorDecoder
import feign.jackson.JacksonDecoder
import feign.jackson.JacksonEncoder
import feign.okhttp.OkHttpClient
import feign.slf4j.Slf4jLogger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.nio.charset.StandardCharsets

/**
 * Interface Feign para a API do InfoSimples, definindo os endpoints HTTP.
 */
@Headers("Content-Type: application/x-www-form-urlencoded")
interface InfoSimplesHttpApi {
    /**
     * Consulta dados de CCM de um CNPJ na API do InfoSimples.
     *
     * @param cnpj CNPJ a ser consultado (apenas números)
     * @param token Token de autenticação
     * @param timeout Timeout da requisição em segundos
     * @return Resposta com os dados do CCM
     */
    @RequestLine("POST /api/v2/consultas/pref/sp/sao-paulo/ccm")
    fun consultarCcm(
        @Param("cnpj") cnpj: String,
        @Param("token") token: String,
        @Param("timeout") timeout: Int = 300
    ): InfoSimplesResponseDto
}

/**
 * Implementação do InfoSimplesClient usando o cliente HTTP Feign.
 */
@Component
class InfoSimplesHttpClient(
    private val infoSimplesConfig: InfoSimplesConfig,
    private val objectMapper: ObjectMapper
) : InfoSimplesClient {

    private val logger = LoggerFactory.getLogger(javaClass)
    private lateinit var httpClient: InfoSimplesHttpApi

    init {
        val objectMapper =
            ObjectMapper()
                .registerModule(JavaTimeModule())
                .registerKotlinModule()

        httpClient =
            Feign
                .builder()
                .client(OkHttpClient())
                .encoder(JacksonEncoder(objectMapper))
                .decoder(JacksonDecoder(objectMapper))
                .logger(Slf4jLogger(this.javaClass.canonicalName))
                .logLevel(feign.Logger.Level.FULL)
                .errorDecoder(InfoSimplesErrorDecoder(objectMapper))
                .target(InfoSimplesHttpApi::class.java, infoSimplesConfig.apiUrl)
    }

    override fun consultarCcm(cnpj: String): InfoSimplesResponseDto {
        val cnpjLimpo = cnpj.replace(Regex("[^0-9]"), "")
        logger.info("🔍 InfoSimples API - POST /api/v2/consultas/pref/sp/sao-paulo/ccm - CNPJ: $cnpjLimpo")

        return try {
            val response = httpClient.consultarCcm(cnpjLimpo, infoSimplesConfig.token)

            // Verificar se houve erro na resposta
            if (response.code != 200) {
                logger.warn("⚠️ InfoSimples API - POST /api/v2/consultas/pref/sp/sao-paulo/ccm [${response.code}] - Erro: ${response.codeMessage}")
                throw InfoSimplesException(response.code, response.codeMessage)
            }

            // Verificar se há dados
            if (response.dataCount == 0 || response.data.isEmpty()) {
                logger.warn("⚠️ InfoSimples API - POST /api/v2/consultas/pref/sp/sao-paulo/ccm [200] - CNPJ não encontrado")
                throw InfoSimplesCnpjNotFoundException(cnpj)
            }

            val responseJson = objectMapper.writeValueAsString(response)
            logger.info("✅ InfoSimples API - POST /api/v2/consultas/pref/sp/sao-paulo/ccm [200] - Response: $responseJson")
            response
        } catch (e: InfoSimplesException) {
            if (e !is InfoSimplesCnpjNotFoundException) {
                logger.error("❌ InfoSimples API - POST /api/v2/consultas/pref/sp/sao-paulo/ccm [${e.httpStatus}] - Error: ${e.message}")
            }
            throw e
        } catch (e: Exception) {
            logger.error("❌ InfoSimples API - POST /api/v2/consultas/pref/sp/sao-paulo/ccm [ERROR] - Exception: ${e.message}", e)
            throw InfoSimplesException("Erro ao consultar CCM: ${e.message}", e)
        }
    }
}

/**
 * Decodificador de erro personalizado para a API do InfoSimples.
 */
class InfoSimplesErrorDecoder(
    private val objectMapper: ObjectMapper,
) : ErrorDecoder {
    override fun decode(
        methodKey: String,
        response: Response,
    ): Exception {
        val statusCode = response.status()

        // Se não for um erro, não transforma
        if (statusCode < 400) {
            return feign.codec.ErrorDecoder
                .Default()
                .decode(methodKey, response)
        }

        // Tentar ler o corpo da resposta
        val responseBody = try {
            response.body()?.asInputStream()?.readBytes()?.toString(StandardCharsets.UTF_8) ?: ""
        } catch (e: Exception) {
            ""
        }

        return when (statusCode) {
            400 -> InfoSimplesException(statusCode, "Requisição inválida: $responseBody")
            401 -> InfoSimplesException(statusCode, "Token de autenticação inválido")
            403 -> InfoSimplesException(statusCode, "Acesso negado")
            404 -> InfoSimplesException(statusCode, "Endpoint não encontrado")
            429 -> InfoSimplesException(statusCode, "Limite de requisições excedido")
            in 500..599 -> InfoSimplesException(statusCode, "Erro interno do servidor InfoSimples: $responseBody")
            else -> InfoSimplesException(statusCode, "Erro desconhecido: $responseBody")
        }
    }
}
