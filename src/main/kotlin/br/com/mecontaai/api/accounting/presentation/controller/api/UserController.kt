package br.com.mecontaai.api.accounting.presentation.controller.api

import br.com.mecontaai.api.accounting.application.usecase.impl.CreateUserUseCase
import br.com.mecontaai.api.accounting.presentation.dto.CreateUserRequestDto
import br.com.mecontaai.api.accounting.presentation.dto.UserResponseDto
import br.com.mecontaai.api.accounting.presentation.mapper.UserMapper
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * REST controller for user operations.
 */
@RestController
@RequestMapping("/api/v1/users")
class UserController(
    private val createUserUseCase: CreateUserUseCase,
    private val userMapper: UserMapper
) {
    /**
     * Create a new user.
     *
     * @param requestDto User creation request data
     * @return The created user
     */
    @PostMapping
    fun createUser(@Valid @RequestBody requestDto: CreateUserRequestDto): ResponseEntity<UserResponseDto> {
        // Map the request DTO to application DTO
        val createUserDto = userMapper.toCreateUserDto(requestDto)
        
        // Execute the use case
        val userDto = createUserUseCase.execute(createUserDto)
        
        // Map the application DTO to response DTO
        val responseDto = userMapper.toUserResponseDto(userDto)
        
        // Return with HTTP 201 Created status
        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto)
    }
} 