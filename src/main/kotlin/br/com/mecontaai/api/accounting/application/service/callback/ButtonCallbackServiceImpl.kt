package br.com.mecontaai.api.accounting.application.service.callback

import br.com.mecontaai.api.accounting.domain.model.callback.ButtonAction
import br.com.mecontaai.api.accounting.domain.service.callback.ActionType
import br.com.mecontaai.api.accounting.domain.service.callback.ButtonCallbackHandler
import br.com.mecontaai.api.accounting.domain.service.callback.ButtonCallbackService
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.ToolExecutionResult
import br.com.mecontaai.api.accounting.infrastructure.database.model.ButtonCallbackEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.ButtonCallbackStatus
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.ButtonCallbackRepository
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.UUID

@Service
class ButtonCallbackServiceImpl(
    private val buttonCallbackRepository: ButtonCallbackRepository,
    private val callbackHandlers: List<ButtonCallbackHandler>,
    private val objectMapper: ObjectMapper
) : ButtonCallbackService {

    private val logger = LoggerFactory.getLogger(javaClass)
    private val handlersMap = callbackHandlers.associateBy { it.getActionType() }

    override fun registerCallback(
        chatUuid: UUID,
        toolName: String,
        actionType: ActionType,
        contextData: Map<String, Any>,
        expirationMinutes: Int
    ): String {
        val callbackId = UUID.randomUUID().toString()
        val expiresAt = LocalDateTime.now().plusMinutes(expirationMinutes.toLong())

        logger.info("Registrando callback: $callbackId para ação: $actionType")
        logger.info("Contexto: $contextData")

        val callback = ButtonCallbackEntity(
            callbackId = callbackId,
            chatUuid = chatUuid,
            toolName = toolName,
            actionType = actionType.name,
            contextData = objectMapper.writeValueAsString(contextData),
            expiresAt = expiresAt
        )

        buttonCallbackRepository.save(callback)
        logger.info("Callback $callbackId registrado com sucesso")
        return callbackId
    }

    override fun updateCallbackWithExternalMessageId(callbackId: String, externalMessageId: String) {
        logger.info("Atualizando callback $callbackId com external message ID: $externalMessageId")

        val callback = buttonCallbackRepository.findByCallbackId(callbackId)
        if (callback == null) {
            logger.error("Callback $callbackId não encontrado para atualização")
            return
        }

        val updatedCallback = callback.copy(externalMessageId = externalMessageId)
        buttonCallbackRepository.save(updatedCallback)

        logger.info("Callback $callbackId atualizado com external message ID: $externalMessageId")
    }

    override fun executeCallback(callbackId: String, clickedValue: String, chat: ChatEntity): ToolExecutionResult? {
        logger.info("Executando callback: $callbackId com valor clicado: $clickedValue")

        val callback = buttonCallbackRepository.findByCallbackIdAndStatus(
            callbackId,
            ButtonCallbackStatus.PENDING
        )

        if (callback == null) {
            logger.warn("Callback $callbackId não encontrado ou já executado")
            return null
        }

        return executeCallbackInternal(callback, clickedValue, chat)
    }

    override fun executeCallbackByExternalMessageId(externalMessageId: String, clickedValue: String, chat: ChatEntity): ToolExecutionResult? {
        logger.info("Executando callback por external message ID: $externalMessageId com valor clicado: $clickedValue")

        val callback = buttonCallbackRepository.findByExternalMessageIdAndStatus(
            externalMessageId,
            ButtonCallbackStatus.PENDING
        )

        if (callback == null) {
            logger.warn("Callback com external message ID $externalMessageId não encontrado ou já executado")
            return null
        }

        return executeCallbackInternal(callback, clickedValue, chat)
    }

    private fun executeCallbackInternal(callback: ButtonCallbackEntity, clickedValue: String, chat: ChatEntity): ToolExecutionResult? {
        if (callback.expiresAt.isBefore(LocalDateTime.now())) {
            logger.warn("Callback ${callback.callbackId} expirado")
            buttonCallbackRepository.save(callback.copy(status = ButtonCallbackStatus.EXPIRED))
            return null
        }

        val actionType = try {
            ActionType.valueOf(callback.actionType)
        } catch (e: IllegalArgumentException) {
            logger.error("Tipo de ação inválido: ${callback.actionType}")
            return null
        }

        val handler = handlersMap[actionType]
        if (handler == null) {
            logger.error("Handler não encontrado para ação: ${callback.actionType}")
            return null
        }

        val contextData = objectMapper.readValue(callback.contextData, Map::class.java) as Map<String, Any>
        logger.info("Contexto recuperado: $contextData")

        val action = ButtonAction(
            actionType = callback.actionType,
            contextData = contextData,
            chatUuid = callback.chatUuid,
            toolName = callback.toolName,
            clickedValue = clickedValue
        )

        val result = handler.execute(action, chat)

        // Marcar como executado
        buttonCallbackRepository.save(
            callback.copy(
                status = ButtonCallbackStatus.EXECUTED,
                executedAt = LocalDateTime.now()
            )
        )

        logger.info("Callback ${callback.callbackId} executado com sucesso")
        return result
    }

    override fun cleanupExpiredCallbacks() {
        val expiredCallbacks = buttonCallbackRepository.findExpiredCallbacks(LocalDateTime.now())
        expiredCallbacks.forEach { callback ->
            buttonCallbackRepository.save(callback.copy(status = ButtonCallbackStatus.EXPIRED))
        }
        logger.info("Limpeza de callbacks expirados: ${expiredCallbacks.size} callbacks marcados como expirados")
    }
}
