package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.model.ChatMessageEvent
import br.com.mecontaai.api.accounting.domain.service.step.ChatResolutionStep
import br.com.mecontaai.api.accounting.domain.service.step.LlmProcessingStep
import br.com.mecontaai.api.accounting.domain.service.step.MessagePersistenceStep
import br.com.mecontaai.api.accounting.domain.service.step.ResponseDeliveryStep
import br.com.mecontaai.api.accounting.infrastructure.logging.JsonLogger
import br.com.mecontaai.api.accounting.infrastructure.logging.infoJson
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Service responsible for orchestrating the complete chat message processing pipeline.
 * This service coordinates all the steps required to process a user message and generate a response.
 *
 * Processing Pipeline:
 * 1. Chat Resolution - Find or create the chat entity
 * 2. Message Persistence - Save the message and context
 * 3. LLM Processing - Generate AI response
 * 4. Response Delivery - Send response back to user
 */
@Service
class ChatMessageProcessingService(
    private val chatResolutionStep: ChatResolutionStep,
    private val messagePersistenceStep: MessagePersistenceStep,
    private val llmProcessingStep: LlmProcessingStep,
    private val responseDeliveryStep: ResponseDeliveryStep,
    private val jsonLogger: JsonLogger,
) {
    private val logger = LoggerFactory.getLogger(ChatMessageProcessingService::class.java)

    /**
     * Processes a chat message through the complete pipeline.
     *
     * @param chatMessageEvent The chat message event to process
     * @throws ChatMessageProcessingException if any step in the pipeline fails
     */
    fun processUserMessage(chatMessageEvent: ChatMessageEvent) {
        logger.info("Starting message processing pipeline for: ${chatMessageEvent.chatId}")

        try {
            // Step 1: Resolve or create chat
            val chat = chatResolutionStep.execute(chatMessageEvent)
            logger.debug("Chat resolution completed for: {}", chat.uuid)

            // Step 2: Persist message and context
            messagePersistenceStep.execute(chatMessageEvent, chat)
            logger.debug("Message persistence completed")

            // Step 3: Process with LLM (using enhanced method for agent responses)
            val llmProcessingResult = llmProcessingStep.execute(chatMessageEvent, chat)
            logger.infoJson("LLM processing result completed:", llmProcessingResult, jsonLogger)

            // Step 4: Deliver agent responses
            val externalMessageIds = responseDeliveryStep.execute(llmProcessingResult.agentResponses, chat)
            logger.debug("Response delivery completed with {} message IDs: {}", externalMessageIds.size, externalMessageIds)

            logger.info("Message processing pipeline completed successfully for: ${chatMessageEvent.externalMessageId}")
        } catch (e: Exception) {
            logger.error("Error in message processing pipeline for: ${chatMessageEvent.externalMessageId}", e)
            throw ChatMessageProcessingException(
                "Failed to process message ${chatMessageEvent.externalMessageId}",
                e,
            )
        }
    }
}

/**
 * Exception thrown when the chat message processing pipeline fails.
 */
class ChatMessageProcessingException(
    message: String,
    cause: Throwable? = null,
) : Exception(message, cause)
