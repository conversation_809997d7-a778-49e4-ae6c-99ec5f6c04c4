package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.application.dto.EmitirNfseRequest
import br.com.mecontaai.api.accounting.application.dto.NfseDto

/**
 * Interface para o caso de uso de emissão de NFS-e.
 * 
 * Define o contrato para emissão de NFS-e baseado no fluxo de negócio:
 * 1. Cliente seleciona/cadastra tomador (brasileiro, estrangeiro ou não informado)
 * 2. Cliente informa dados do serviço (valor, CNAE, descrição, onde será utilizado)
 * 3. Cliente confirma os dados e informa senha do certificado
 * 4. Sistema emite NFS-e via FocusNFE
 */
interface EmitirNfseUseCase {
    
    /**
     * Executa a emissão de uma NFS-e.
     *
     * @param request dados necessários para emissão da NFS-e
     * @return dados da NFS-e emitida
     */
    fun execute(request: EmitirNfseRequest): NfseDto
}
