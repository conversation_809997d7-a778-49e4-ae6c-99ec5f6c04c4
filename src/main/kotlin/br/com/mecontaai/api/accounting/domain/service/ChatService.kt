package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.entity.Chat
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity

/**
 * Service interface for Chat operations.
 */
interface ChatService {
    /**
     * Find a chat by ID.
     *
     * @param id The chat ID
     * @return The chat if found, null otherwise
     */
    fun findById(id: Long): Chat?

    /**
     * Busca um chat pelo telefone do usuário e metaPhoneNumberId.
     * Se não existir, cria um novo chat.
     *
     * @param userPhoneNumber O número de telefone do usuário
     * @param metaPhoneNumberId O ID do número de telefone da Meta
     * @return O chat encontrado ou criado
     */
    fun findOrCreateChat(userPhoneNumber: String, metaPhoneNumberId: String): ChatEntity
} 