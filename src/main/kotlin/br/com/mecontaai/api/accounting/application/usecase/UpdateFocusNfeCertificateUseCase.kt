package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.domain.dto.FocusNfeResponseDto

/**
 * Use case para atualizar certificado na FocusNFE.
 */
interface UpdateFocusNfeCertificateUseCase {
    /**
     * Atualiza o certificado de uma empresa na FocusNFE.
     * 
     * Processo:
     * 1. Faz chamada PUT para /v2/empresas/{id} na FocusNFE
     * 2. Envia certificado em base64 e senha
     * 3. Valida resposta e extrai informações do certificado
     * 4. Verifica se CNPJ do certificado corresponde à empresa
     *
     * @param empresaId ID da empresa
     * @param certificadoBase64 Certificado convertido em base64
     * @param senha Senha do certificado
     * @param cnpjEmpresa CNPJ da empresa para validação
     * @return Resposta da FocusNFE com informações do certificado
     */
    fun execute(
        empresaId: Long, 
        certificadoBase64: String, 
        senha: String,
        cnpjEmpresa: String
    ): FocusNfeResponseDto
} 