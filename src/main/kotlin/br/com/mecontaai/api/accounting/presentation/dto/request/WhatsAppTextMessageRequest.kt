package br.com.mecontaai.api.accounting.presentation.dto.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern

/**
 * DTO para requisição de envio de mensagem via WhatsApp.
 */
data class WhatsAppTextMessageRequest(
    @field:NotBlank(message = "O ID do número do WhatsApp é obrigatório")
    val metaPhoneNumberId: String,
    @field:NotBlank(message = "O número do destinatário é obrigatório")
    @field:Pattern(
        regexp = "^\\d{10,13}$",
        message = "O número do destinatário deve conter entre 10 e 13 dígitos",
    )
    val to: String,
    @field:NotBlank(message = "A mensagem é obrigatória")
    val message: String,
)
