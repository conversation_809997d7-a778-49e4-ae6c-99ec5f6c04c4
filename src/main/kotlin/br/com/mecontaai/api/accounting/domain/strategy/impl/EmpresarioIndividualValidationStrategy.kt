package br.com.mecontaai.api.accounting.domain.strategy.impl

import br.com.mecontaai.api.accounting.domain.dto.CompanyValidationResultDto
import br.com.mecontaai.api.accounting.domain.strategy.CompanyValidationStrategy
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto

/**
 * Estratégia de validação para Empresário Individual.
 * Para esta natureza jurídica, não validamos QSA e assumimos 1 sócio.
 */
class EmpresarioIndividualValidationStrategy : CompanyValidationStrategy {
    override fun validate(cnpjData: ReceitaWsResponseDto): CompanyValidationResultDto? {
        // Para Empresário Individual, não validamos QSA
        return null
    }
    
    override fun calculatePartnerCount(cnpjData: ReceitaWsResponseDto): Int {
        // Empresário Individual sempre tem 1 sócio (o próprio empresário)
        return 1
    }
    
    override fun hasLegalEntityPartner(cnpjData: ReceitaWsResponseDto): Boolean {
        // Empresário Individual não pode ter sócio pessoa jurídica
        return false
    }
}
