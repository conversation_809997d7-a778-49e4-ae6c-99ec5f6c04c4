package br.com.mecontaai.api.accounting.infrastructure.client.receitaws

import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto

/**
 * Interface que define o contrato para interação com a API da Receita WS.
 *
 * Este client permite consultar dados de empresas através do CNPJ,
 * fornecendo informações como dados cadastrais, endereço, atividades e situação fiscal.
 */
interface ReceitaWsClient {

    /**
     * Consulta dados de uma empresa através do CNPJ na Receita WS.
     *
     * @param cnpj CNPJ a ser consultado (apenas dígitos)
     * @return Dados da empresa, incluindo informações cadastrais, endereço, atividades e situação fiscal
     * @throws ReceitaWsException se o CNPJ não for encontrado ou ocorrer outro erro na consulta
     */
    fun consultarCnpj(cnpj: String): ReceitaWsResponseDto
} 