package br.com.mecontaai.api.accounting.infrastructure.meta.adapter

import br.com.mecontaai.api.accounting.domain.model.InteractiveMessage
import br.com.mecontaai.api.accounting.infrastructure.meta.dto.WhatsAppButtonDto
import br.com.mecontaai.api.accounting.infrastructure.meta.dto.WhatsAppInteractiveMessageDto

class WhatsAppMessageAdapter {
    fun toWhatsAppMessage(interactiveMessage: InteractiveMessage, to: String): WhatsAppInteractiveMessageDto {
        val buttons = interactiveMessage.buttons.map { button ->
            WhatsAppButtonDto(
                id = button.id,
                title = button.label
            )
        }

        return WhatsAppInteractiveMessageDto(
            to = to,
            text = interactiveMessage.text,
            buttons = buttons
        )
    }
}