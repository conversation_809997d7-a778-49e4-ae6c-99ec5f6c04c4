package br.com.mecontaai.api.accounting.infrastructure.webhook.strategy

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Registry para descoberta e seleção de estratégias de processamento de interações.
 * Utiliza injeção de dependência do Spring para auto-descobrir todas as estratégias disponíveis.
 */
@Component
class InteractionStrategyRegistry(
    strategies: List<InteractionStrategy>,
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val strategyMap: Map<String, InteractionStrategy> = strategies.associateBy { it.getInteractionType() }

    init {
        logger.info("🔧 InteractionStrategyRegistry inicializado com ${strategies.size} estratégias:")
        strategies.forEach { strategy ->
            logger.info(" - ${strategy.getInteractionType()}: ${strategy::class.simpleName}")
        }
    }

    /**
     * Encontra a estratégia apropriada para o tipo de interação.
     */
    fun findStrategy(interactionType: String): InteractionStrategy? {
        val strategy = strategyMap[interactionType]

        if (strategy != null) {
            logger.debug("✅ Estratégia encontrada para '$interactionType': ${strategy::class.simpleName}")
        } else {
            logger.warn("⚠️ Nenhuma estratégia encontrada para tipo: '$interactionType'")
            logger.debug("📋 Tipos disponíveis: ${strategyMap.keys}")
        }

        return strategy
    }

    /**
     * Lista todos os tipos de interação suportados.
     */
    fun getSupportedTypes(): Set<String> = strategyMap.keys

    /**
     * Verifica se um tipo de interação é suportado.
     */
    fun isSupported(interactionType: String): Boolean = strategyMap.containsKey(interactionType)
}
