package br.com.mecontaai.api.accounting.presentation.dto.request

import br.com.mecontaai.api.infrastructure.client.whatsapp.dto.WhatsAppInteractiveMessageDto
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * Classe que representa uma requisição de envio de uma mensagem interativa via WhatsApp.
 */
data class WhatsAppInteractiveMessageRequest(
    @field:NotBlank
    val metaPhoneNumberId: String,
    @field:NotBlank
    val to: String,
    @field:NotNull
    val payload: WhatsAppInteractiveMessageDto,
)
