package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.UserChat
import java.util.UUID

/**
 * Repository interface for UserChat operations.
 */
interface UserChatRepository {
    /**
     * Save a user chat relationship.
     * 
     * @param userChat The user chat relationship to save
     * @return The saved user chat relationship with generated ID and UUID
     */
    fun save(userChat: UserChat): UserChat
    
    /**
     * Find a user chat relationship by UUID.
     * 
     * @param uuid The user chat UUID
     * @return The user chat relationship if found, null otherwise
     */
    fun findByUuid(uuid: UUID): UserChat?
    
    /**
     * Find active user chat relationship by user and chat IDs.
     * 
     * @param userId The user ID
     * @param chatId The chat ID
     * @return The active user chat relationship if found, null otherwise
     */
    fun findActiveByUserIdAndChatId(userId: Long, chatId: Long): UserChat?
    
    /**
     * Check if an active relationship exists between a user and a chat.
     * 
     * @param userId The user ID
     * @param chatId The chat ID
     * @return true if an active relationship exists, false otherwise
     */
    fun existsActiveByUserIdAndChatId(userId: Long, chatId: Long): Boolean
    
    /**
     * Update user chat relationship.
     * 
     * @param userChat The user chat relationship to update
     * @return The updated user chat relationship
     */
    fun update(userChat: UserChat): UserChat
} 