package br.com.mecontaai.api.accounting.presentation.dto.webhook

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class FocusNfseWebhookPayload(
    @JsonProperty("cnpj_prestador")
    val cnpjEmitente: String,

    @JsonProperty("ref")
    val ref: String,

    @JsonProperty("status")
    val status: String,

    @JsonProperty("numero")
    val numero: String? = null,

    @JsonProperty("numero_rps")
    val numeroRps: String? = null,

    @JsonProperty("serie_rps")
    val serieRps: String? = null,

    @JsonProperty("tipo_rps")
    val tipoRps: String? = null,

    @JsonProperty("codigo_verificacao")
    val codigoVerificacao: String? = null,

    @JsonProperty("data_emissao")
    val dataEmissao: String? = null,

    @JsonProperty("url")
    val urlConsultaPrefeitura: String? = null,

    @JsonProperty("url_danfse")
    val caminhoDanfe: String? = null,

    @JsonProperty("caminho_xml_nota_fiscal")
    val caminhoXmlNotaFiscal: String? = null,

    @JsonProperty("id")
    val id: String? = null,

    // Campos opcionais originais
    @JsonProperty("status_sefaz")
    val statusSefaz: String? = null,

    @JsonProperty("mensagem_sefaz")
    val mensagemSefaz: String? = null,

    @JsonProperty("chave_nfe")
    val chaveNfe: String? = null
)
