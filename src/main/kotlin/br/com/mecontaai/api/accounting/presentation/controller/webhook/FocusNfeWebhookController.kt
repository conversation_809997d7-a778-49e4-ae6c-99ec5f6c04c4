package br.com.mecontaai.api.accounting.presentation.controller.webhook

import br.com.mecontaai.api.accounting.application.usecase.ProcessFocusNfseWebhookUseCase
import br.com.mecontaai.api.accounting.presentation.dto.webhook.FocusNfseWebhookPayload
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/webhooks/focus")
class FocusNfeWebhookController(
    private val processFocusNfseWebhookUseCase: ProcessFocusNfseWebhookUseCase,
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(FocusNfeWebhookController::class.java)

    @PostMapping
    fun receiveFocusWebhooks(
        @RequestBody payload: FocusNfseWebhookPayload,
        @RequestHeader headers: Map<String, String>,
    ): ResponseEntity<Unit> {
        logger.info("🛜 FocusNFSE webhook: $payload")
        return try {
            // Processa o webhook através do use case
            processFocusNfseWebhookUseCase.execute(payload)
            ResponseEntity.ok().build()
        } catch (ex: Exception) {
            logger.error("Erro ao processar webhook do Focus NFSe - Ref: ${payload.ref}, Payload: $payload", ex)
            ResponseEntity.internalServerError().build()
        }
    }

//    /**
//     * Endpoint genérico para receber todos os tipos de webhooks da Focus API.
//     *
//     * A Focus API pode enviar diferentes tipos de eventos:
//     * - NFSe processada/autorizada/rejeitada
//     * - Empresa criada/atualizada
//     * - Certificado vencendo/expirado
//     * - Outros eventos futuros
//     *
//     * Como os payloads variam por tipo de evento, recebemos como String
//     * e fazemos parse genérico para logging e processamento.
//     */
//    @PostMapping
//    fun receiveFocusWebhooks(
//        @RequestBody payload: String,
//        @RequestHeader headers: Map<String, String>,
//    ): ResponseEntity<Unit> {
//        logger.info("🔔 Focus API webhook recebido")
//        logger.info("📦 Payload size: ${payload.length} characters")
//
//        return try {
//            // Parse do JSON para verificar se é válido e extrair informações básicas
//            val webhookData = objectMapper.readTree(payload)
//            val compactPayload = objectMapper.writeValueAsString(webhookData)
//
//            // Log do payload completo em uma única linha
//            logger.info("📄 Focus webhook payload: $compactPayload")
//            ResponseEntity.ok().build()
//        } catch (ex: Exception) {
//            logger.error("❌ Erro ao processar webhook da Focus API", ex)
//            logger.error("📦 Payload que causou erro: $payload")
//            ResponseEntity.internalServerError().build()
//        }
//    }
}
