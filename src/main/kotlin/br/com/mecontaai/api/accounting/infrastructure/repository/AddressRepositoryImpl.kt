package br.com.mecontaai.api.accounting.infrastructure.repository

import br.com.mecontaai.api.accounting.domain.entity.Address
import br.com.mecontaai.api.accounting.domain.enum.EntityType
import br.com.mecontaai.api.accounting.domain.repository.AddressRepository
import br.com.mecontaai.api.accounting.infrastructure.database.repository.AddressEntityRepository
import br.com.mecontaai.api.accounting.infrastructure.mapper.AddressMapper
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Implementação do repositório de endereços.
 */
@Repository
class AddressRepositoryImpl(
    private val addressEntityRepository: AddressEntityRepository,
    private val addressMapper: AddressMapper
) : AddressRepository {

    override fun save(address: Address): Address {
        val entity = addressMapper.toEntity(address)
        val savedEntity = addressEntityRepository.save(entity)
        return addressMapper.toDomain(savedEntity)
    }

    override fun findById(id: Long): Address? {
        return addressEntityRepository.findById(id)
            .map { addressMapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByUuid(uuid: String): Address? {
        return try {
            val uuidObj = UUID.fromString(uuid)
            addressEntityRepository.findByUuid(uuidObj)
                .map { addressMapper.toDomain(it) }
                .orElse(null)
        } catch (e: IllegalArgumentException) {
            null
        }
    }

    override fun findByEntityTypeAndEntityId(entityType: EntityType, entityId: Long): List<Address> {
        return addressEntityRepository.findByEntityTypeAndEntityId(entityType, entityId)
            .map { addressMapper.toDomain(it) }
    }

    override fun findPrimaryByEntityTypeAndEntityId(entityType: EntityType, entityId: Long): Address? {
        return addressEntityRepository.findPrimaryAddress(entityType, entityId)
            .map { addressMapper.toDomain(it) }
            .orElse(null)
    }
} 