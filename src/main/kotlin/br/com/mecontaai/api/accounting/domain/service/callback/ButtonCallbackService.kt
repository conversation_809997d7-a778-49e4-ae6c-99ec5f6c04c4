package br.com.mecontaai.api.accounting.domain.service.callback

import br.com.mecontaai.api.accounting.domain.model.callback.ButtonAction
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.ToolExecutionResult
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import java.util.UUID

interface ButtonCallbackService {
    /**
     * Registra um callback para um botão
     */
    fun registerCallback(
        chatUuid: UUID,
        toolName: String,
        actionType: ActionType,
        contextData: Map<String, Any>,
        expirationMinutes: Int = 30
    ): String

    /**
     * Atualiza um callback com o external message ID após o envio da mensagem
     */
    fun updateCallbackWithExternalMessageId(callbackId: String, externalMessageId: String)

    /**
     * Executa um callback baseado no payload do botão
     */
    fun executeCallback(callbackId: String, clickedValue: String, chat: ChatEntity): ToolExecutionResult?

    /**
     * Executa um callback baseado no external message ID
     */
    fun executeCallbackByExternalMessageId(externalMessageId: String, clickedValue: String, chat: ChatEntity): ToolExecutionResult?

    /**
     * Limpa callbacks expirados
     */
    fun cleanupExpiredCallbacks()
}

interface ButtonCallbackHandler {
    /**
     * Tipo de ação que este handler pode processar
     */
    fun getActionType(): ActionType

    /**
     * Executa a ação quando o botão é clicado
     */
    fun execute(action: ButtonAction, chat: ChatEntity): ToolExecutionResult

    /**
     * Valida se o contexto é válido para execução
     */
    fun validateContext(contextData: Map<String, Any>): Boolean
}
