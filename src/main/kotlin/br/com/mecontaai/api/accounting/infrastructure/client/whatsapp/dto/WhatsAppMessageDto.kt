package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto

import com.fasterxml.jackson.annotation.JsonProperty

data class WhatsAppMessageDto(
    @JsonProperty("messaging_product")
    val messagingProduct: String = "whatsapp",
    @JsonProperty("recipient_type")
    val recipientType: String = "individual",
    val to: String,
    val type: String = "text",
    val text: Text
) {
    data class Text(
        @JsonProperty("preview_url")
        val previewUrl: Boolean = false,
        val body: String
    )
} 