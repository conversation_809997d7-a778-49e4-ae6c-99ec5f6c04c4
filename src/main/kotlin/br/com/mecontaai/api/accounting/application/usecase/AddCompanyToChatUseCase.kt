package br.com.mecontaai.api.accounting.application.usecase

import java.util.UUID

interface AddCompanyToChatUseCase {
    /**
     * Associa uma empresa a um chat específico de um usuário.
     * 
     * @param userId ID do usuário
     * @param chatId ID do chat
     * @param companyId ID da empresa
     * @return UUID da relação atualizada
     */
    fun execute(userId: Long, chatId: Long, companyId: Long): UUID
} 