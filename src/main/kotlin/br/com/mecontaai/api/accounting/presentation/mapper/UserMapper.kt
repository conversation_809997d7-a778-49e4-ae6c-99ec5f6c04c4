package br.com.mecontaai.api.accounting.presentation.mapper

import br.com.mecontaai.api.accounting.application.dto.CreateUserDto
import br.com.mecontaai.api.accounting.application.dto.UserDto
import br.com.mecontaai.api.accounting.presentation.dto.CreateUserRequestDto
import br.com.mecontaai.api.accounting.presentation.dto.UserResponseDto
import org.springframework.stereotype.Component

/**
 * Mapper for converting between user DTOs.
 */
@Component
class UserMapper {
    /**
     * Convert a presentation CreateUserRequestDto to an application CreateUserDto.
     *
     * @param requestDto The presentation DTO
     * @return The application DTO
     */
    fun toCreateUserDto(requestDto: CreateUserRequestDto): CreateUserDto {
        return CreateUserDto(
            name = requestDto.name,
            email = requestDto.email,
            password = requestDto.password,
            document = requestDto.document
        )
    }
    
    /**
     * Convert an application UserDto to a presentation UserResponseDto.
     *
     * @param userDto The application DTO
     * @return The presentation DTO
     */
    fun toUserResponseDto(userDto: UserDto): UserResponseDto {
        return UserResponseDto(
            id = userDto.id,
            uuid = userDto.uuid,
            name = userDto.name,
            email = userDto.email,
            document = userDto.document,
            isActive = userDto.isActive,
            createdAt = userDto.createdAt,
            updatedAt = userDto.updatedAt
        )
    }
} 