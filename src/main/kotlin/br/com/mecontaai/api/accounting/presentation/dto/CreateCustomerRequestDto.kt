package br.com.mecontaai.api.accounting.presentation.dto

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern

/**
 * DTO para a requisição de criação de cliente.
 */
data class CreateCustomerRequestDto(
    /**
     * Nome do cliente
     */
    @field:NotBlank(message = "O nome do cliente é obrigatório")
    val name: String,
    
    /**
     * CPF ou CNPJ do cliente
     */
    val document: String,
    
    /**
     * Tipo de documento (CPF ou CNPJ)
     * Na API, recebemos apenas o código do tipo (string),
     * que será convertido para DocumentTypeDto internamente.
     */
    val documentType: String,
    
    /**
     * ID da empresa à qual o cliente pertence
     */
    @field:NotNull(message = "O ID da empresa é obrigatório")
    val companyId: Long,
    
    /**
     * Flag indicando se o cliente é estrangeiro
     */
    val isForeigner: Boolean = false,
    
    /**
     * Email do cliente
     */
    val email: String? = null,
    
    /**
     * Telefone do cliente
     */
    val phone: String? = null
) 