package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.dto.CompanyQualificationDto

/**
 * Service para gerenciar qualificações de empresas.
 * Permite consultar quais serviços uma empresa está qualificada a prestar.
 */
interface CompanyQualificationService {
    
    /**
     * Busca todas as qualificações de uma empresa.
     * Retorna os CNAEs e códigos de serviço municipal associados à empresa.
     *
     * @param companyId ID da empresa
     * @return Lista de qualificações da empresa
     */
    fun findByCompanyId(companyId: Long): List<CompanyQualificationDto>
} 