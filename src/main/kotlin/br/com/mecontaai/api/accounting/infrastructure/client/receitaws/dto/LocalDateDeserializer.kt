package br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

/**
 * Deserializador personalizado para LocalDate que aceita múltiplos formatos de data.
 */
class LocalDateDeserializer : JsonDeserializer<LocalDate>() {
    private val formatters = listOf(
        DateTimeFormatter.ofPattern("dd/MM/yyyy"),
        DateTimeFormatter.ISO_DATE_TIME,
        DateTimeFormatter.ISO_DATE
    )

    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): LocalDate? {
        val dateStr = p.valueAsString ?: return null
        
        for (formatter in formatters) {
            try {
                return LocalDate.parse(dateStr, formatter)
            } catch (e: DateTimeParseException) {
                // Tenta o próximo formatador
                continue
            }
        }
        
        return null
    }
} 