package br.com.mecontaai.api.accounting.domain.exception

/**
 * Exceção lançada quando um cliente não é encontrado.
 */
class CustomerNotFoundException(message: String) : RuntimeException(message)

/**
 * Exceção lançada quando se tenta criar um cliente com um documento que já existe para uma empresa.
 */
class CustomerAlreadyExistsException(document: String, companyId: Long) : 
    RuntimeException("Cliente com documento $document já existe para a empresa $companyId") 