package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.presentation.dto.webhook.FocusNfseWebhookPayload

/**
 * Use case para processar webhooks recebidos do Focus NFSe.
 * Atualiza o status da NFSe no banco de dados baseado na notificação recebida.
 */
interface ProcessFocusNfseWebhookUseCase {
    /**
     * Processa um webhook do Focus NFSe.
     *
     * @param payload Dados do webhook recebido
     */
    fun execute(payload: FocusNfseWebhookPayload)
} 