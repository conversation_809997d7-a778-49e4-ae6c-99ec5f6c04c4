package br.com.mecontaai.api.accounting.domain.exception

/**
 * Base exception for user-related operations.
 */
open class UserException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

/**
 * Exception thrown when a user with the given identifier is not found.
 */
open class UserNotFoundException(message: String) : UserException(message)

/**
 * Exception thrown when trying to create a user with an email that is already in use.
 */
open class EmailAlreadyExistsException(email: String) : 
    UserException("Email $email is already in use by another user")

/**
 * Exception thrown when trying to create a user with a document (CPF) that is already in use.
 */
open class DocumentAlreadyExistsException(document: String) : 
    UserException("Document $document is already in use by another user")