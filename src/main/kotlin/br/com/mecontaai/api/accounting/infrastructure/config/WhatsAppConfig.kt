package br.com.mecontaai.api.accounting.infrastructure.config

import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.WhatsAppErrorDecoder
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.WhatsAppHttpClient
import com.fasterxml.jackson.databind.ObjectMapper
import feign.*
import feign.jackson.JacksonDecoder
import feign.jackson.JacksonEncoder
import feign.okhttp.OkHttpClient
import feign.slf4j.Slf4jLogger
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 * Configuração do cliente Feign para a API do WhatsApp.
 */
@Configuration
class WhatsAppConfig {

    @Bean
    fun whatsAppHttpApi(
        @Value("\${whatsapp.api.base-url}")
        baseUrl: String,
        @Value("\${whatsapp.api.token}")
        token: String,
        objectMapper: ObjectMapper
    ): WhatsAppHttpClient {
        return Feign.builder()
            .client(OkHttpClient())
            .encoder(JacksonEncoder(objectMapper))
            .decoder(JacksonDecoder(objectMapper))
            .logger(Slf4jLogger(WhatsAppHttpClient::class.java))
            .errorDecoder(WhatsAppErrorDecoder(objectMapper))
            .requestInterceptor { template ->
                template.header("Authorization", "Bearer $token")
            }
            .target(WhatsAppHttpClient::class.java, baseUrl)
    }
} 