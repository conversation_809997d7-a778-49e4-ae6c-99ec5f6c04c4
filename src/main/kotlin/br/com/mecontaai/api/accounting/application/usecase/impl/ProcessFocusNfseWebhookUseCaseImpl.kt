package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.usecase.ProcessFocusNfseWebhookUseCase
import br.com.mecontaai.api.accounting.domain.dto.BlobMetadata
import br.com.mecontaai.api.accounting.domain.entity.Nfse
import br.com.mecontaai.api.accounting.domain.enum.BlobEntityType
import br.com.mecontaai.api.accounting.domain.enum.ContentCategory
import br.com.mecontaai.api.accounting.domain.enum.NfseStatus
import br.com.mecontaai.api.accounting.domain.enum.StorageType
import br.com.mecontaai.api.accounting.domain.repository.CompanyRepository
import br.com.mecontaai.api.accounting.domain.service.BlobService
import br.com.mecontaai.api.accounting.domain.service.NfseService
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusClient
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusException
import br.com.mecontaai.api.accounting.infrastructure.config.FocusConfig
import br.com.mecontaai.api.accounting.infrastructure.event.SqsQueues
import br.com.mecontaai.api.accounting.infrastructure.event.dto.NfseWebhookProcessedDto
import br.com.mecontaai.api.accounting.infrastructure.service.SqsService
import br.com.mecontaai.api.accounting.presentation.dto.webhook.FocusNfseWebhookPayload
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.time.Duration
import java.util.UUID
import kotlin.math.log

/**
 * Implementação do use case para processar webhooks do Focus NFSe.
 * Mapeia os status do Focus para os status internos e atualiza a NFSe no banco.
 */
@Service
class ProcessFocusNfseWebhookUseCaseImpl(
    private val nfseService: NfseService,
    private val blobService: BlobService,
    private val focusClient: FocusClient,
    private val companyRepository: CompanyRepository,
    private val focusConfig: FocusConfig,
    private val sqsService: SqsService,
) : ProcessFocusNfseWebhookUseCase {
    companion object {
        private val logger = LoggerFactory.getLogger(ProcessFocusNfseWebhookUseCaseImpl::class.java)
        private val httpClient =
            HttpClient
                .newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build()
    }

    override fun execute(payload: FocusNfseWebhookPayload) {
        logger.info("Processando webhook do Focus NFSe - Ref: ${payload.ref}, Status: ${payload.status}")

        try {
            // Mapeia o status do Focus para o status interno
            val internalStatus = mapFocusStatusToInternal(payload.status)

            // Atualiza o status da NFSe no banco
            val updatedNfse =
                nfseService.updateNfseStatus(
                    reference = payload.ref,
                    status = internalStatus.name,
                    mensagemErro = payload.mensagemSefaz,
                )

            if (updatedNfse != null) {
                logger.info("NFSe atualizada com sucesso - Ref: ${payload.ref}, Novo status: ${internalStatus.name}")

                // Logs específicos por tipo de status
                when (internalStatus) {
                    NfseStatus.AUTHORIZED -> {
                        logger.info("NFSe autorizada - Ref: ${payload.ref}, Número: ${payload.numero}, Chave: ${payload.chaveNfe}")

                        // Processa arquivos XML e PDF quando autorizada
                        processAuthorizedFiles(payload, updatedNfse)
                    }

                    NfseStatus.DENEGADA -> {
                        logger.warn(
                            "NFSe denegada pela SEFAZ - Ref: ${payload.ref}, Status SEFAZ: ${payload.statusSefaz}, Mensagem: ${payload.mensagemSefaz}",
                        )
                    }

                    NfseStatus.AUTHORIZATION_ERROR -> {
                        logger.error(
                            "Erro na autorização da NFSe - Ref: ${payload.ref}, Status SEFAZ: ${payload.statusSefaz}, Mensagem: ${payload.mensagemSefaz}",
                        )
                    }

                    NfseStatus.CANCELED -> {
                        logger.info("NFSe cancelada - Ref: ${payload.ref}")
                    }

                    else -> {
                        // Log padrão já foi feito acima
                    }
                }
            } else {
                logger.warn("NFSe não encontrada para atualização - Ref: ${payload.ref}")
            }
        } catch (exception: Exception) {
            logger.error("Erro ao processar webhook do Focus NFSe - Ref: ${payload.ref}", exception)
            throw exception
        }
    }

    /**
     * Mapeia os status do Focus NFSe para os status internos do sistema.
     * Baseado na documentação oficial do Focus NFe.
     */
    private fun mapFocusStatusToInternal(focusStatus: String): NfseStatus =
        when (focusStatus.lowercase()) {
            "autorizado" -> NfseStatus.AUTHORIZED
            "cancelado" -> NfseStatus.CANCELED
            "erro_autorizacao" -> NfseStatus.AUTHORIZATION_ERROR
            "processando_autorizacao" -> NfseStatus.PROCESSING_AUTHORIZATION
            "denegado" -> NfseStatus.DENEGADA
            else -> {
                logger.warn("Status desconhecido recebido do Focus: $focusStatus, usando PROCESSING_AUTHORIZATION como padrão")
                NfseStatus.PROCESSING_AUTHORIZATION
            }
        }

    /**
     * Processa os arquivos XML e PDF quando a NFSe é autorizada.
     * Busca os dados atualizados via FocusClient para obter URLs corretas.
     */
    private fun processAuthorizedFiles(
        payload: FocusNfseWebhookPayload,
        nfse: br.com.mecontaai.api.accounting.domain.entity.Nfse,
    ) {
        try {
            // Busca a empresa pelo CNPJ para obter o focusKey
            val company = companyRepository.findByDocument(payload.cnpjEmitente)
            if (company?.token == null) {
                logger.error("Empresa não encontrada ou sem focusKey - CNPJ: ${payload.cnpjEmitente}")
                return
            }

            // Busca dados atualizados da NFSe via Focus API
            val nfseStatus =
                try {
                    // TODO: undestand when to use company's token
                  focusClient.getNfseStatus(payload.ref, company.token!!)
                } catch (e: FocusException) {
                    logger.error("Erro ao buscar status da NFSe via Focus API - Ref: ${payload.ref}", e)
                    return
                }

            var xmlBlobUuid: UUID? = null
            var pdfBlobUuid: UUID? = null

            // Processa o arquivo XML se disponível
            nfseStatus.caminho_xml_nota_fiscal?.let { xmlPath ->
                xmlBlobUuid =
                    downloadAndSaveFile(
                        url = "${focusConfig.apiUrl}$xmlPath",
                        filename = "${payload.ref}.xml",
                        contentType = "application/xml",
                        contentCategory = ContentCategory.XML,
                        nfseUuid = nfse.uuid.toString(),
                    )
                logger.info("XML da NFSe salvo como blob - Ref: ${payload.ref}, BlobUUID: $xmlBlobUuid")
            }

            // Processa o arquivo PDF se disponível
            nfseStatus.url_danfse?.let { pdfUrl ->
                pdfBlobUuid =
                    downloadAndSaveFile(
                        url = pdfUrl,
                        filename = "${payload.ref}.pdf",
                        contentType = "application/pdf",
                        contentCategory = ContentCategory.DOCUMENT,
                        nfseUuid = nfse.uuid.toString(),
                    )
                logger.info("PDF da NFSe salvo como blob - Ref: ${payload.ref}, BlobUUID: $pdfBlobUuid")
            }

            // Atualiza a NFSe com os UUIDs dos blobs
            if (xmlBlobUuid != null || pdfBlobUuid != null) {
                nfseService.updateNfseBlobs(payload.ref, xmlBlobUuid, pdfBlobUuid)
                logger.info("NFSe atualizada com referências dos blobs - Ref: ${payload.ref}")
            }

            // Captura a URL do PDF se disponível
            val nfsePdfUrl = nfseStatus.url_danfse ?: ""
            logger.info("URL do PDF da NFSe: $nfsePdfUrl")
            
            sendToQueue(nfse, pdfBlobUuid, nfsePdfUrl)
        } catch (e: Exception) {
            logger.error("Erro ao processar arquivos da NFSe autorizada - Ref: ${payload.ref}", e)
        }
    }

    private fun sendToQueue(nfse: Nfse, pdfBlobUuid: UUID?, nfsePdfUrl: String) {
        try {
            logger.info("Enviando mensagem para fila SQS - NFSe UUID: ${nfse.uuid}, PDF Blob UUID: $pdfBlobUuid")
            val nfseWebhookProcessedDto = NfseWebhookProcessedDto(
                nfseUuid = nfse.uuid,
                pdfBlobUuid = pdfBlobUuid,
                nfsePdfUrl = nfsePdfUrl
            )

            sqsService.sendToQueue(
                payload = nfseWebhookProcessedDto,
                queueName = SqsQueues.NFSE_WEBHOOK_PROCESSED,
            )
            
            logger.info("Mensagem enviada para fila SQS - NFSe UUID: ${nfse.uuid}")
        } catch (e: Exception) {
            logger.error("Erro ao enviar mensagem para fila SQS - NFSe UUID: ${nfse.uuid}", e)
        }
    }

    /**
     * Faz download de um arquivo e salva como blob no S3.
     */
    private fun downloadAndSaveFile(
        url: String,
        filename: String,
        contentType: String,
        contentCategory: ContentCategory,
        nfseUuid: String,
    ): UUID? =
        try {
            logger.info("Fazendo download do arquivo: $url")

            val request =
                HttpRequest
                    .newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(30))
                    .GET()
                    .build()

            val response = httpClient.send(request, HttpResponse.BodyHandlers.ofByteArray())

            if (response.statusCode() == 200) {
                val fileData = response.body()

                val metadata =
                    BlobMetadata(
                        contentType = contentType,
                        contentCategory = contentCategory,
                        filename = filename,
                        size = fileData.size.toLong(),
                        entityType = BlobEntityType.NFSE,
                        entityId = nfseUuid,
                        storageType = StorageType.S3,
                    )

                val blobUuid = blobService.save(fileData, metadata)
                logger.info("Arquivo salvo com sucesso - URL: $url, BlobUUID: $blobUuid, Tamanho: ${fileData.size}")

                blobUuid
            } else {
                logger.error("Erro no download do arquivo - URL: $url, Status: ${response.statusCode()}")
                null
            }
        } catch (e: Exception) {
            logger.error("Erro ao fazer download do arquivo - URL: $url", e)
            null
        }
}
