package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.Nfse

/**
 * Repository interface for NFSe (Nota Fiscal de Serviço Eletrônica) operations.
 */
interface NfseRepository {

    /**
     * Saves a new NFSe or updates an existing one.
     *
     * @param nfse The NFSe to save
     * @return The saved NFSe with any generated IDs
     */
    fun save(nfse: Nfse): Nfse

    /**
     * Finds an NFSe by its reference.
     *
     * @param reference The unique reference identifier
     * @return The NFSe if found, null otherwise
     */
    fun findByReference(reference: String): Nfse?

    /**
     * Finds an NFSe by its UUID.
     *
     * @param uuid The NFSe UUID
     * @return The NFSe if found, null otherwise
     */
    fun findByUuid(uuid: java.util.UUID): Nfse?

    /**
     * Finds all NFSes for a given customer.
     *
     * @param customerId The customer ID
     * @return List of NFSes for the customer
     */
    fun findByCustomerId(customerId: Long): List<Nfse>
    
    /**
     * Updates the status of an NFSe.
     *
     * @param reference The unique reference identifier
     * @param status The new status
     * @param mensagemErro Optional error message
     * @return The updated NFSe
     */
    fun updateStatus(reference: String, status: String, mensagemErro: String? = null): Nfse?
    
    /**
     * Updates the blob UUIDs of an NFSe.
     *
     * @param reference The unique reference identifier
     * @param xmlBlobUuid UUID of the XML blob
     * @param pdfBlobUuid UUID of the PDF blob
     * @return The updated NFSe
     */
    fun updateBlobs(reference: String, xmlBlobUuid: java.util.UUID? = null, pdfBlobUuid: java.util.UUID? = null): Nfse?
}