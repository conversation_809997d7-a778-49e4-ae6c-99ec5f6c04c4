package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.domain.dto.CompanyValidationResultDto

/**
 * Interface para o caso de uso de validação de empresa.
 */
interface ValidateCompanyUseCase {
    /**
     * Valida um CNPJ para verificar se atende aos requisitos para criação de empresa.
     * 
     * Requisitos:
     * - CNPJ válido e ativo na Receita Federal
     * - Empresa deve ser optante do Simples Nacional
     * - Deve possuir apenas um sócio pessoa física (QSA)
     *
     * @param document CNPJ a ser validado
     * @return Resultado da validação contendo status e dados do CNPJ ou motivo da falha
     */
    fun execute(document: String): CompanyValidationResultDto
} 