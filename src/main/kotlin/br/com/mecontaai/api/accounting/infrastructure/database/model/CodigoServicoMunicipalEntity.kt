package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*

/**
 * Entity class representing a municipal service code in the database.
 */
@Entity
@Table(name = "codigos_servico_municipais")
class CodigoServicoMunicipalEntity(
    @Column(name = "codigo", nullable = false)
    val codigo: String,

    @Column(name = "descricao", nullable = false)
    val descricao: String,

    @Column(name = "codigo_municipio", nullable = false)
    val codigoMunicipio: String
) : BaseEntity()