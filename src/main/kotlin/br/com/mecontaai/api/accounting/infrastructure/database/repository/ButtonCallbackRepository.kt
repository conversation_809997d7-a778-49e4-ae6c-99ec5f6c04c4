package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.ButtonCallbackEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.ButtonCallbackStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.UUID

@Repository
interface ButtonCallbackRepository : JpaRepository<ButtonCallbackEntity, Long> {

    fun findByCallbackIdAndStatus(callbackId: String, status: ButtonCallbackStatus): ButtonCallbackEntity?

    fun findByCallbackId(callbackId: String): ButtonCallbackEntity?

    fun findByExternalMessageIdAndStatus(externalMessageId: String, status: ButtonCallbackStatus): ButtonCallbackEntity?

    fun findByExternalMessageId(externalMessageId: String): ButtonCallbackEntity?

    @Query("SELECT b FROM ButtonCallbackEntity b WHERE b.expiresAt < :now AND b.status = :status")
    fun findExpiredCallbacks(
        @Param("now") now: LocalDateTime,
        @Param("status") status: ButtonCallbackStatus = ButtonCallbackStatus.PENDING
    ): List<ButtonCallbackEntity>

    fun findByChatUuidAndStatus(chatUuid: UUID, status: ButtonCallbackStatus): List<ButtonCallbackEntity>
}
