package br.com.mecontaai.api.accounting.domain.service.llm

import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse

/**
 * Result of an agent tool execution.
 * Tools can return data for LLM processing and/or direct responses to the user.
 */
data class AgentToolResult(
    val success: Boolean,
    val data: Map<String, Any> = emptyMap(),
    val llmContext: String? = null,
    val directResponses: List<AgentResponse> = emptyList(),
    val error: String? = null,
    val shouldContinueWithLlm: Boolean = true,
) {
    companion object {
        /**
         * Tool executed successfully with data for LLM processing.
         */
        fun success(
            data: Map<String, Any>,
            llmContext: String? = null,
        ) = AgentToolResult(
            success = true,
            data = data,
            llmContext = llmContext,
            shouldContinueWithLlm = true,
        )

        /**
         * Tool executed successfully with direct responses to user.
         * LLM processing will be skipped.
         */
        fun directResponse(
            responses: List<AgentResponse>,
            data: Map<String, Any> = emptyMap(),
        ) = AgentToolResult(
            success = true,
            data = data,
            directResponses = responses,
            shouldContinueWithLlm = false,
        )

        /**
         * Tool executed successfully with both data and direct responses.
         * LLM will process the data and then direct responses will be sent.
         */
        fun mixed(
            data: Map<String, Any>,
            responses: List<AgentResponse>,
            llmContext: String? = null,
        ) = AgentToolResult(
            success = true,
            data = data,
            llmContext = llmContext,
            directResponses = responses,
            shouldContinueWithLlm = true,
        )

        /**
         * Tool execution failed.
         */
        fun error(
            error: String,
            data: Map<String, Any> = emptyMap(),
        ) = AgentToolResult(
            success = false,
            error = error,
            data = data,
            shouldContinueWithLlm = true,
        )
    }
}
