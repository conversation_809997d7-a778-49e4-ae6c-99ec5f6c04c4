package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*

/**
 * Entity class representing a user in the database.
 */
@Entity
@Table(name = "users")
class UserEntity(
    @Column(nullable = false)
    val name: String,

    @Column(nullable = false, unique = true)
    val email: String,

    @Column(nullable = false)
    val password: String,

    @Column
    val document: String? = null
) : BaseEntity()