package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.dto.BlobData
import br.com.mecontaai.api.accounting.domain.dto.BlobMetadata
import br.com.mecontaai.api.accounting.domain.enum.BlobEntityType
import java.util.UUID

/**
 * Interface para o serviço de gerenciamento de blobs.
 */
interface BlobService {
    /**
     * Salva um blob e retorna seu UUID
     * 
     * @param blob Conteúdo do blob
     * @param metadata Metadados do blob
     * @return UUID do blob salvo
     */
    fun save(blob: ByteArray, metadata: BlobMetadata): UUID
    
    /**
     * Recupera um blob pelo UUID
     * 
     * @param uuid UUID do blob
     * @return O blob com seu conteúdo e metadados, ou null se não encontrado
     */
    fun get(uuid: UUID): BlobData?
    
    /**
     * Recupera apenas os metadados
     * 
     * @param uuid UUID do blob
     * @return Metadados do blob, ou null se não encontrado
     */
    fun getMetadata(uuid: UUID): BlobMetadata?
    
    /**
     * Deleta um blob (marcação lógica)
     * 
     * @param uuid UUID do blob
     * @return true se o blob foi deletado, false caso contrário
     */
    fun delete(uuid: UUID): Boolean
    
    /**
     * Verifica se um blob existe e está ativo
     * 
     * @param uuid UUID do blob
     * @return true se o blob existe e está ativo, false caso contrário
     */
    fun exists(uuid: UUID): Boolean
    
    /**
     * Gera URL pública temporária (se aplicável)
     * 
     * @param uuid UUID do blob
     * @param expirationMinutes Tempo de expiração da URL em minutos
     * @return URL pública temporária, ou null se não for possível gerar
     */
    fun generatePublicUrl(uuid: UUID, expirationMinutes: Int = 10): String?
    
    /**
     * Busca blobs por entidade relacionada
     * 
     * @param entityType Tipo da entidade
     * @param entityId ID da entidade
     * @return Lista de blobs relacionados à entidade
     */
    fun findByEntity(entityType: BlobEntityType, entityId: String): List<BlobMetadata>
} 