package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.entity.Company

/**
 * Service interface for Company operations.
 */
interface CompanyService {
    /**
     * Find a company by ID.
     *
     * @param id The company ID
     * @return The company if found, null otherwise
     */
    fun findById(id: Long): Company?

    /**
     * Adiciona ou atualiza o ID da Focus em uma empresa.
     *
     * @param id ID da empresa
     * @param focusId ID da empresa na Focus
     * @return Empresa atualizada
     * @throws IllegalArgumentException se a empresa não for encontrada
     */
    fun addFocusId(id: Long, focusId: String): Company

    /**
     * Adiciona ou atualiza a chave da Focus em uma empresa.
     *
     * @param id ID da empresa
     * @param tokenProducao Chave da empresa na Focus
     * @return Empresa atualizada
     * @throws IllegalArgumentException se a empresa não for encontrada
     */
    fun addFocusKey(id: Long, tokenProducao: String, tokenHomologacao: String): Company
} 