package br.com.mecontaai.api.accounting.domain.service

/**
 * Interface for password encoding operations.
 */
interface PasswordEncoderService {
    /**
     * Encode a raw password.
     * 
     * @param rawPassword The raw password to encode
     * @return The encoded password
     */
    fun encode(rawPassword: String): String
    
    /**
     * Check if a raw password matches an encoded password.
     * 
     * @param rawPassword The raw password to check
     * @param encodedPassword The encoded password to compare to
     * @return true if the passwords match, false otherwise
     */
    fun matches(rawPassword: String, encodedPassword: String): Boolean
} 