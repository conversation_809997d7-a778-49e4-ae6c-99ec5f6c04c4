package br.com.mecontaai.api.accounting.infrastructure.repository

import br.com.mecontaai.api.accounting.domain.entity.Certificate
import br.com.mecontaai.api.accounting.domain.repository.CertificateRepository
import br.com.mecontaai.api.accounting.infrastructure.database.model.CertificateEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CertificateEntityRepository
import org.springframework.context.annotation.Primary
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Implementation of CertificateRepository using JPA.
 */
@Repository
@Primary
@Qualifier("jpaCertificateRepository")
class CertificateRepositoryImpl(
    private val certificateEntityRepository: CertificateEntityRepository
) : CertificateRepository {

    override fun save(certificate: Certificate): Certificate {
        val certificateEntity = mapToEntity(certificate)
        val savedEntity = certificateEntityRepository.save(certificateEntity)
        return mapToDomain(savedEntity)
    }

    override fun findById(id: Long): Certificate? {
        return certificateEntityRepository.findById(id)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByUuid(uuid: UUID): Certificate? {
        return certificateEntityRepository.findByUuid(uuid)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findActiveByCompanyId(companyId: Long): Certificate? {
        return certificateEntityRepository.findByCompanyIdAndIsActiveTrue(companyId)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByCompanyId(companyId: Long): List<Certificate> {
        return certificateEntityRepository.findByCompanyId(companyId)
            .map { mapToDomain(it) }
    }

    override fun findByBlobUuid(blobUuid: String): Certificate? {
        return certificateEntityRepository.findByBlobUuid(blobUuid)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun hasActiveCertificate(companyId: Long): Boolean {
        return certificateEntityRepository.existsByCompanyIdAndIsActiveTrue(companyId)
    }

    override fun findAll(): List<Certificate> {
        return certificateEntityRepository.findAll()
            .map { mapToDomain(it) }
    }

    /**
     * Map a domain Certificate to a JPA CertificateEntity.
     */
    private fun mapToEntity(certificate: Certificate): CertificateEntity {
        val uuid = certificate.uuid
        
        // If certificate has an ID, try to get existing entity
        val existingEntity = certificate.id?.let {
            certificateEntityRepository.findById(it).orElse(null)
        }
        
        return CertificateEntity(
            companyId = certificate.companyId,
            expirationDate = certificate.expirationDate,
            blobUuid = certificate.blobUuid,
            password = certificate.password
        ).apply {
            // If updating an existing entity, preserve the UUID and set isActive
            if (existingEntity != null) {
                this.uuid = existingEntity.uuid
                this.id = existingEntity.id
            } else {
                this.uuid = uuid
            }
            this.isActive = certificate.isActive
        }
    }

    /**
     * Map a JPA CertificateEntity to a domain Certificate.
     */
    private fun mapToDomain(entity: CertificateEntity): Certificate {
        return Certificate(
            id = entity.id,
            uuid = entity.uuid,
            companyId = entity.companyId,
            expirationDate = entity.expirationDate,
            blobUuid = entity.blobUuid,
            password = entity.password,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
} 