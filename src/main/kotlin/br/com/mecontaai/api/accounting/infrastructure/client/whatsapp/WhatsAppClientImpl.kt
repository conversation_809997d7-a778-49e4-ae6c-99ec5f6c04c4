package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp

import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto.WhatsAppDocumentMessageDto
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto.WhatsAppImageMessageDto
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto.WhatsAppMessageDto
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto.WhatsAppResponseDto
import br.com.mecontaai.api.infrastructure.client.whatsapp.dto.WhatsAppInteractiveMessageDto
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * Implementação do cliente HTTP para a API do WhatsApp.
 */
@Component
class WhatsAppClientImpl(
    @Value("\${whatsapp.api.version}")
    private val apiVersion: String,
    private val whatsAppHttpClient: WhatsAppHttpClient,
    private val objectMapper: ObjectMapper,
) : WhatsAppClient {
    private val logger = LoggerFactory.getLogger(WhatsAppClient::class.java)

    override fun sendMessage(
        phoneNumberId: String,
        to: String,
        text: String,
    ): WhatsAppResponseDto =
        safeCall(
            operation = "sending message",
            generatePayload = {
                val message =
                    WhatsAppMessageDto(
                        to = to,
                        text = WhatsAppMessageDto.Text(body = text),
                    )

                logger.info(
                    "💬 WhatsApp Text | Para: {} | PhoneId: {} | Texto: {} | Payload: {}",
                    to,
                    phoneNumberId,
                    text,
                    objectMapper.writeValueAsString(message),
                )

                whatsAppHttpClient.sendMessage(apiVersion, phoneNumberId, message)
            },
        )

    override fun sendImageMessage(
        phoneNumberId: String,
        to: String,
        imageId: String,
        caption: String?,
    ): WhatsAppResponseDto =
        safeCall(
            operation = "sending image message",
            generatePayload = {
                val message =
                    WhatsAppImageMessageDto(
                        to = to,
                        image =
                            WhatsAppImageMessageDto.Image(
                                id = imageId,
                                caption = caption,
                            ),
                    )

                logger.info(
                    "🖼️ WhatsApp Image (ID) | Para: {} | PhoneId: {} | ImageId: {} | Caption: {} | Payload: {}",
                    to,
                    phoneNumberId,
                    imageId,
                    caption ?: "sem caption",
                    objectMapper.writeValueAsString(message),
                )

                whatsAppHttpClient.sendImageMessage(apiVersion, phoneNumberId, message)
            },
        )

    override fun sendImageMessageWithUrl(
        phoneNumberId: String,
        to: String,
        imageUrl: String,
        caption: String?,
    ): WhatsAppResponseDto =
        safeCall(
            operation = "sending image message with URL",
            generatePayload = {
                val message =
                    WhatsAppImageMessageDto(
                        to = to,
                        image =
                            WhatsAppImageMessageDto.Image(
                                link = imageUrl,
                                caption = caption,
                            ),
                    )

                logger.info(
                    "🌐 WhatsApp Image (URL) | Para: {} | PhoneId: {} | ImageURL: {} | Caption: {} | Payload: {}",
                    to,
                    phoneNumberId,
                    imageUrl,
                    caption ?: "sem caption",
                    objectMapper.writeValueAsString(message),
                )

                whatsAppHttpClient.sendImageMessage(apiVersion, phoneNumberId, message)
            },
        )

    override fun sendDocumentMessageWithUrl(
        phoneNumberId: String,
        to: String,
        documentUrl: String,
        caption: String?,
    ): WhatsAppResponseDto =
        safeCall(
            operation = "sending document message with URL",
            generatePayload = {
                val message =
                    WhatsAppDocumentMessageDto(
                        to = to,
                        document =
                            WhatsAppDocumentMessageDto.Document(
                                link = documentUrl,
                                caption = caption,
                            ),
                    )

                logger.info(
                    "📄 WhatsApp Document | Para: {} | PhoneId: {} | DocURL: {} | Caption: {} | Payload: {}",
                    to,
                    phoneNumberId,
                    documentUrl,
                    caption ?: "sem caption",
                    objectMapper.writeValueAsString(message),
                )

                whatsAppHttpClient.sendDocumentMessage(apiVersion, phoneNumberId, message)
            },
        )

    override fun sendInteractiveMessage(
        phoneNumberId: String,
        to: String,
        payload: WhatsAppInteractiveMessageDto,
    ): WhatsAppResponseDto =
        safeCall(
            operation = "sending interactive message",
            generatePayload = {
                val interactive = payload.interactive
                val buttonsInfo =
                    interactive?.action?.buttons?.let { buttons ->
                        "Botões(${buttons.size}): ${buttons.joinToString(", ") { "${it.reply?.title}(${it.reply?.id})" }}"
                    } ?: ""

                val sectionsInfo =
                    interactive?.action?.sections?.let { sections ->
                        "Seções(${sections.size}): ${sections.joinToString(", ") { "${it.title}(${it.rows?.size ?: 0})" }}"
                    } ?: ""

                logger.info(
                    "🔘 WhatsApp Interactive | Para: {} | PhoneId: {} | Tipo: {} | Header: {} | Body: {} | {} {} | Payload: {}",
                    to,
                    phoneNumberId,
                    interactive.type,
                    interactive.header?.text ?: "sem header",
                    interactive.body.text,
                    buttonsInfo,
                    sectionsInfo,
                    objectMapper.writeValueAsString(payload),
                )

                whatsAppHttpClient.sendInteractiveMessage(apiVersion, phoneNumberId, payload)
            },
        )

    /**
     * Função genérica para tratar chamadas de cliente HTTP.
     */
    private inline fun <T> safeCall(
        operation: String,
        generatePayload: () -> T,
    ): T =
        try {
            val startTime = System.currentTimeMillis()
            val result = generatePayload()
            val duration = System.currentTimeMillis() - startTime

            // Log de sucesso com resposta
            if (result is WhatsAppResponseDto) {
                logger.info(
                    "✅ WhatsApp API Success | Tempo: {}ms | MessageId: {} | Response: {}",
                    duration,
                    result.messages.firstOrNull()?.id
                        ?: "não retornado",
                    objectMapper.writeValueAsString(result),
                )
            } else {
                logger.info("✅ WhatsApp API Success | Tempo: {}ms", duration)
            }

            result
        } catch (e: WhatsAppException) {
            // Propaga qualquer exceção WhatsAppException já existente
            logger.error("❌ WhatsApp API - Erro específico: {}", e.message)
            throw e
        } catch (e: Exception) {
            // Lê informações adicionais da exceção (e.g., status ou detalhes adicionais)
            logger.error("❌ WhatsApp API - Erro ao {}: {}", operation, e.message, e)

            // Proteja contra exceções genéricas e encapsule-as na WhatsAppException
            throw WhatsAppException(
                httpStatus = 0,
                errorCode = null,
                message = "Erro ao $operation via API do WhatsApp: ${e.message}",
                cause = e,
            )
        }
}
