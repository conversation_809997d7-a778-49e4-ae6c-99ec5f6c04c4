package br.com.mecontaai.api.accounting.domain.enum

/**
 * Enum representando os tipos de natureza jurídica suportados pelo sistema.
 * Baseado nos códigos da Receita Federal.
 */
enum class NaturezaJuridicaEnum(
    val codigo: String,
    val descricao: String,
) {
    EMPRESARIO_INDIVIDUAL(
        codigo = "213-5",
        descricao = "Empresário (Individual)",
    ),

    EIRELI(
        codigo = "230-5",
        descricao = "Empresa Individual de Responsabilidade Limitada (de Natureza Empresária)",
    ),

    SOCIEDADE_EMPRESARIA_LIMITADA(
        codigo = "206-2",
        descricao = "Sociedade Empresária Limitada",
    ),

    SOCIEDADE_SIMPLES_PURA(
        codigo = "223-2",
        descricao = "Sociedade Simples Pura",
    ),

    SOCIEDADE_SIMPLES_LIMITADA(
        codigo = "224-0",
        descricao = "Sociedade Simples Limitada",
    ),

    SOCIEDADE_UNIPESSOAL_ADVOCACIA(
        codigo = "232-1",
        descricao = "Sociedade Unipessoal de Advocacia",
    ),

    COOPERATIVA_CONSUMO(
        codigo = "233-0",
        descricao = "Cooperativa de Consumo",
    ),
    ;

    companion object {
        /**
         * Busca a natureza jurídica pelo código.
         *
         * @param codigo Código da natureza jurídica (ex: "206-2")
         * @return NaturezaJuridicaEnum correspondente ou null se não encontrado
         */
        private fun fromCodigo(codigo: String): NaturezaJuridicaEnum? = values().find { it.codigo == codigo }

        /**
         * Extrai o código da natureza jurídica de uma string no formato da Receita Federal.
         *
         * Exemplo: "206-2 - Sociedade Empresária Limitada" -> "206-2"
         *
         * @param naturezaJuridicaString String completa da natureza jurídica
         * @return Código extraído ou null se não conseguir extrair
         */
        private fun extrairCodigo(naturezaJuridicaString: String?): String? {
            if (naturezaJuridicaString.isNullOrBlank()) return null

            // Busca por padrão: "XXX-X - Descrição"
            val regex = Regex("^(\\d{3}-\\d)\\s*-\\s*.*")
            val matchResult = regex.find(naturezaJuridicaString.trim())

            return matchResult?.groupValues?.get(1)
        }

        /**
         * Converte uma string da Receita Federal para o enum correspondente.
         *
         * @param naturezaJuridicaString String no formato "206-2 - Sociedade Empresária Limitada"
         * @return NaturezaJuridicaEnum correspondente ou null se não encontrado/suportado
         */
        fun fromReceitaWsString(naturezaJuridicaString: String?): NaturezaJuridicaEnum{
            val codigo = extrairCodigo(naturezaJuridicaString) ?: return null
            return fromCodigo(codigo)
        }
    }

    /**
     * Retorna a representação completa no formato da Receita Federal.
     *
     * @return String no formato "206-2 - Sociedade Empresária Limitada"
     */
    private fun toReceitaWsFormat(): String = "$codigo - $descricao"

    override fun toString(): String = toReceitaWsFormat()
}
