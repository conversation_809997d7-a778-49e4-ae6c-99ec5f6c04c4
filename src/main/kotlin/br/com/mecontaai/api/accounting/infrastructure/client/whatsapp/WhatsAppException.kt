package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp

/**
 * Exceção personalizada para erros retornados pela API do WhatsApp.
 */
class WhatsAppException(
    val httpStatus: Int,             // Status HTTP retornado na resposta da API
    val errorCode: String? = null,   // Código específico do erro retornado pela API
    val responseBody: String? = null, // Corpo completo da resposta em formato String
    message: String,                 // Mensagem de erro principal
    cause: Throwable? = null         // Causa raiz caso exista
) : RuntimeException(message, cause) {

    override val message: String
        get() {
            val baseMessage = super.message ?: "Erro desconhecido"
            return buildString {
                append("Status: $httpStatus, Message: \"$baseMessage\"")
                if (!errorCode.isNullOrBlank()) append(", Code: $errorCode")
                if (!responseBody.isNullOrBlank()) append(", Body: $responseBody")
            }
        }
}