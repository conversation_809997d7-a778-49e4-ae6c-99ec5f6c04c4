package br.com.mecontaai.api.accounting.presentation.controller.api

import br.com.mecontaai.api.accounting.application.usecase.AddUserToChatUseCase
import br.com.mecontaai.api.accounting.application.usecase.AddCompanyToChatUseCase
import br.com.mecontaai.api.accounting.presentation.dto.AddUserToChatRequestDto
import br.com.mecontaai.api.accounting.presentation.dto.AddCompanyToChatRequestDto
import br.com.mecontaai.api.accounting.presentation.dto.UserChatResponseDto
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

/**
 * REST controller for user chat operations.
 */
@RestController
@RequestMapping("/api/v1/user-chats")
class UserChatController(
    private val addUserToChatUseCase: AddUserToChatUseCase,
    private val addCompanyToChatUseCase: AddCompanyToChatUseCase
) {

    /**
     * Adiciona um usuário a um chat específico.
     *
     * @param requestDto User-to-chat association request data
     * @return The created relationship
     */
    @PostMapping("/add-user")
    fun addUserToChat(@Valid @RequestBody requestDto: AddUserToChatRequestDto): ResponseEntity<UserChatResponseDto> {
        try {
            // Execute the use case
            val relationshipUuid = addUserToChatUseCase.execute(requestDto.userId, requestDto.chatId)
            
            // Create response
            val responseDto = UserChatResponseDto(
                uuid = relationshipUuid,
                message = "User successfully added to chat"
            )
            
            // Return with HTTP 201 Created status
            return ResponseEntity.status(HttpStatus.CREATED).body(responseDto)
        } catch (e: IllegalArgumentException) {
            // Return with HTTP 400 Bad Request for validation errors
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                UserChatResponseDto(
                    uuid = UUID.randomUUID(), // Dummy UUID for error response
                    message = e.message ?: "Invalid request"
                )
            )
        }
    }

    /**
     * Associa uma empresa a um chat específico de um usuário.
     *
     * @param requestDto Company-to-chat association request data
     * @return The updated relationship
     */
    @PostMapping("/add-company")
    fun addCompanyToChat(@Valid @RequestBody requestDto: AddCompanyToChatRequestDto): ResponseEntity<UserChatResponseDto> {
        try {
            // Execute the use case
            val relationshipUuid = addCompanyToChatUseCase.execute(
                requestDto.userId, 
                requestDto.chatId, 
                requestDto.companyId
            )
            
            // Create response
            val responseDto = UserChatResponseDto(
                uuid = relationshipUuid,
                message = "Company successfully associated with chat"
            )
            
            // Return with HTTP 200 OK status
            return ResponseEntity.ok(responseDto)
        } catch (e: IllegalArgumentException) {
            // Return with HTTP 400 Bad Request for validation errors
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                UserChatResponseDto(
                    uuid = UUID.randomUUID(), // Dummy UUID for error response
                    message = e.message ?: "Invalid request"
                )
            )
        }
    }
} 