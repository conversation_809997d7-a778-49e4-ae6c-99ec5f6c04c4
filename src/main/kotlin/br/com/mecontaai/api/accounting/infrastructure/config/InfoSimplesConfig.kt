package br.com.mecontaai.api.accounting.infrastructure.config

import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

/**
 * Configuration properties for the InfoSimples API.
 */
@Configuration
@ConfigurationProperties(prefix = "infosimples.api")
class InfoSimplesConfig {
    private val logger = LoggerFactory.getLogger(javaClass)

    /**
     * The base URL of the InfoSimples API.
     * Production: https://api.infosimples.com
     *
     * This value MUST be configured in application.yaml under infosimples.api.url
     */
    lateinit var url: String

    /**
     * The token for InfoSimples API authentication.
     * This token is used for API authentication and authorization.
     *
     * This value MUST be configured in application.yaml under infosimples.api.token
     */
    lateinit var token: String

    /**
     * Getter for API URL, ensuring it doesn't end with a slash
     * to avoid double slashes in URLs.
     */
    val apiUrl: String
        get() = url.trimEnd('/')

    /**
     * Logs the configured values after the configuration is loaded.
     */
    @PostConstruct
    fun logConfiguration() {
        logger.info("🔧 InfoSimplesConfig inicializado:")
        logger.info("📍 URL configurada: $url")
        logger.info("🌐 API URL (processada): $apiUrl")

        // Log do token (mascarado por segurança)
        val maskedToken =
            if (::token.isInitialized && token.isNotBlank()) {
                "${token.take(8)}${"*".repeat(maxOf(0, token.length - 12))}${token.takeLast(4)}"
            } else {
                "NÃO CONFIGURADO"
            }
        logger.info("🔑 Token: $maskedToken")

        // Validação adicional
        var hasErrors = false

        if (url.isBlank()) {
            logger.error("❌ ERRO: infosimples.api.url não está configurada!")
            hasErrors = true
        } else if (!url.startsWith("http")) {
            logger.warn("⚠️ AVISO: URL não parece ser válida (não começa com http): $url")
        }

        if (!::token.isInitialized || token.isBlank()) {
            logger.error("❌ ERRO: infosimples.api.token não está configurado!")
            hasErrors = true
        }

        if (!hasErrors) {
            logger.info("✅ Configuração InfoSimples API válida")
        } else {
            logger.error("❌ Configuração InfoSimples API incompleta - verifique application.yaml")
        }
    }
}
