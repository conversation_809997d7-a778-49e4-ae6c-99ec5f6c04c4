package br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import java.time.LocalDate

/**
 * DTO que representa a resposta da API da Receita WS para consulta de CNPJ.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class ReceitaWsResponseDto(
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val abertura: LocalDate? = null,
    val situacao: String? = null,
    val tipo: String? = null,
    val nome: String? = null,
    val fantasia: String? = null,
    val porte: String? = null,
    val natureza_juridica: String? = null,
    val atividade_principal: List<AtividadeDto>? = null,
    val atividades_secundarias: List<AtividadeDto>? = null,
    val qsa: List<QsaDto>? = null,
    val logradouro: String? = null,
    val numero: String? = null,
    val complemento: String? = null,
    val municipio: String? = null,
    val bairro: String? = null,
    val uf: String? = null,
    val cep: String? = null,
    val email: String? = null,
    val telefone: String? = null,
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val data_situacao: LocalDate? = null,
    val cnpj: String? = null,
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val ultima_atualizacao: LocalDate? = null,
    val status: String? = null,
    val efr: String? = null,
    val motivo_situacao: String? = null,
    val situacao_especial: String? = null,
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val data_situacao_especial: LocalDate? = null,
    val capital_social: String? = null,
    val extra: Map<String, Any>? = null,
    val billing: BillingDto? = null,
    val simples: SimplesDto? = null,
    val simei: SimeiDto? = null
)

/**
 * DTO que representa uma atividade (principal ou secundária) de uma empresa.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class AtividadeDto(
    val code: String? = null,
    val text: String? = null
)

/**
 * DTO que representa um quadro socios e administradores (QSA) de uma empresa.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class QsaDto(
    val nome: String? = null,
    val qual: String? = null,
    val nome_rep_legal: String? = null
)

/**
 * DTO que representa informações de faturamento da empresa.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class BillingDto(
    val free: Boolean? = null,
    val database: Boolean? = null
)

/**
 * DTO que representa informações do Simples Nacional.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class SimplesDto(
    val optante: Boolean? = null,
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val data_opcao: LocalDate? = null,
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val data_exclusao: LocalDate? = null,
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val ultima_atualizacao: LocalDate? = null
)

/**
 * DTO que representa informações do MEI.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class SimeiDto(
    val optante: Boolean? = null,
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val data_opcao: LocalDate? = null,
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val data_exclusao: LocalDate? = null,
    @JsonDeserialize(using = LocalDateDeserializer::class)
    val ultima_atualizacao: LocalDate? = null
) 