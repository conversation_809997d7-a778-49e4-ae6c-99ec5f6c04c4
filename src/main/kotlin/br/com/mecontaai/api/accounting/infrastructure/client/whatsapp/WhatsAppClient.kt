package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp

import br.com.mecontaai.api.infrastructure.client.whatsapp.dto.WhatsAppInteractiveMessageDto
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto.WhatsAppResponseDto

/**
 * Interface que define o contrato para interação com a API do WhatsApp.
 */
interface WhatsAppClient {
    /**
     * Envia uma mensagem via WhatsApp.
     *
     * @param phoneNumberId ID do número de telefone do WhatsApp Business
     * @param to Número do destinatário
     * @param text Texto da mensagem
     * @return Resposta da API do WhatsApp
     * @throws WhatsAppException em caso de erro na comunicação com a API
     */
    fun sendMessage(phoneNumberId: String, to: String, text: String): WhatsAppResponseDto

    /**
     * Envia uma mensagem com imagem para um número de WhatsApp.
     *
     * @param phoneNumberId ID do número de telefone do WhatsApp Business
     * @param to Número de telefone do destinatário
     * @param imageId ID da imagem previamente enviada para o WhatsApp
     * @param caption Legenda da imagem (opcional)
     * @return Resposta da API do WhatsApp
     * @throws WhatsAppException em caso de erro na comunicação com a API
     */
    fun sendImageMessage(
        phoneNumberId: String,
        to: String,
        imageId: String,
        caption: String? = null
    ): WhatsAppResponseDto

    /**
     * Envia uma mensagem com imagem via URL para um número de WhatsApp.
     *
     * @param phoneNumberId ID do número de telefone do WhatsApp Business
     * @param to Número de telefone do destinatário
     * @param imageUrl URL da imagem
     * @param caption Legenda da imagem (opcional)
     * @return Resposta da API do WhatsApp
     * @throws WhatsAppException em caso de erro na comunicação com a API
     */
    fun sendImageMessageWithUrl(
        phoneNumberId: String,
        to: String,
        imageUrl: String,
        caption: String? = null
    ): WhatsAppResponseDto

    /**
     * Envia uma mensagem com documento via URL para um número de WhatsApp.
     *
     * @param phoneNumberId ID do número de telefone do WhatsApp Business
     * @param to Número de telefone do destinatário
     * @param documentUrl URL do documento
     * @param caption Legenda do documento (opcional)
     * @return Resposta da API do WhatsApp
     * @throws WhatsAppException em caso de erro na comunicação com a API
     */
    fun sendDocumentMessageWithUrl(
        phoneNumberId: String,
        to: String,
        documentUrl: String,
        caption: String? = null
    ): WhatsAppResponseDto

    /**
     * Envia uma mensagem interativa para um número de WhatsApp.
     *
     * @param phoneNumberId ID do número de telefone do WhatsApp Business
     * @param to Número de telefone do destinatário
     * @param payload Dados estruturados da mensagem interativa
     * @return Resposta da API do WhatsApp
     * @throws WhatsAppException em caso de erro na comunicação com a API
     */
    fun sendInteractiveMessage(
        phoneNumberId: String,
        to: String,
        payload: WhatsAppInteractiveMessageDto
    ): WhatsAppResponseDto
} 