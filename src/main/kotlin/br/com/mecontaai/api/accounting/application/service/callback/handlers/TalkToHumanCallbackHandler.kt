package br.com.mecontaai.api.accounting.application.service.callback.handlers

import br.com.mecontaai.api.accounting.domain.model.callback.ButtonAction
import br.com.mecontaai.api.accounting.domain.model.callback.TalkToHumanContext
import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.service.callback.ActionType
import br.com.mecontaai.api.accounting.domain.service.callback.ButtonCallbackHandler
import br.com.mecontaai.api.accounting.domain.service.notification.NotificationService
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.ToolExecutionResult
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class TalkToHumanCallbackHandler(
    private val objectMapper: ObjectMapper,
    private val notificationService: NotificationService
) : ButtonCallbackHandler {

    private val logger = LoggerFactory.getLogger(javaClass)

    override fun getActionType(): ActionType = ActionType.TALK_TO_HUMAN_RESPONSE

    override fun execute(action: ButtonAction, chat: ChatEntity): ToolExecutionResult {
        logger.info("🎯 Executando TalkToHumanCallbackHandler")
        logger.info("📋 Action Type: ${action.actionType}")
        logger.info("💬 Chat UUID: ${action.chatUuid}")
        logger.info("🔧 Tool Name: ${action.toolName}")
        logger.info("👆 Clicked Value: ${action.clickedValue}")
        logger.info("📦 Context Data: ${action.contextData}")

        // Recupera o contexto original
        val context = try {
            objectMapper.convertValue(action.contextData, TalkToHumanContext::class.java)
        } catch (e: Exception) {
            logger.error("Erro ao converter contexto: ${e.message}")
            // Fallback para contexto simples
            TalkToHumanContext(
                reason = action.contextData["reason"]?.toString() ?: "",
                timestamp = action.contextData["timestamp"]?.toString() ?: "",
                chatUuid = action.chatUuid.toString(),
                userPhoneNumber = chat.userPhoneNumber
            )
        }

        logger.info("🔍 Contexto recuperado:")
        logger.info("   - Reason: ${context.reason}")
        logger.info("   - Timestamp: ${context.timestamp}")
        logger.info("   - Chat UUID: ${context.chatUuid}")
        logger.info("   - User Phone: ${context.userPhoneNumber}")

        val responseMessage = when (action.clickedValue) {
            "needs_help" -> {
                logger.info("✋ Usuário clicou em 'Preciso de ajuda'")

                // Envia notificação para o Slack
                try {
                    notificationService.sendSupportNotification(
                        userPhone = chat.userPhoneNumber,
                        reason = context.reason,
                        chatUuid = chat.uuid.toString(),
                        userName = null // Nome do usuário não está disponível no ChatEntity
                    )
                    logger.info("📢 Notificação de suporte enviada para Slack")
                } catch (e: Exception) {
                    logger.error("❌ Erro ao enviar notificação de suporte para Slack", e)
                }

                buildNeedsHelpResponse(context)
            }
            "all_good" -> {
                logger.info("✅ Usuário clicou em 'Está tudo bem'")
                buildAllGoodResponse(context)
            }
            else -> {
                logger.warn("⚠️ Valor clicado desconhecido: ${action.clickedValue}")
                "Opção não reconhecida. Contexto: ${context.reason}"
            }
        }

        logger.info("📤 Resposta gerada: $responseMessage")

        return ToolExecutionResult(
            agentResponses = listOf(
                AgentResponse.TextResponse(content = responseMessage)
            )
        )
    }

    private fun buildNeedsHelpResponse(context: TalkToHumanContext): String {
        val baseMessage = "Você clicou no 'Preciso de ajuda'"
        return if (context.reason.isNotBlank()) {
            "$baseMessage. Motivo original: '${context.reason}'. Transferindo para atendimento humano..."
        } else {
            "$baseMessage. Transferindo para atendimento humano..."
        }
    }

    private fun buildAllGoodResponse(context: TalkToHumanContext): String {
        val baseMessage = "Você clicou no 'Está tudo bem'"
        return if (context.reason.isNotBlank()) {
            "$baseMessage. Que bom que conseguiu resolver! Motivo original era: '${context.reason}'"
        } else {
            "$baseMessage. Ótimo! Estou aqui se precisar de mais alguma coisa."
        }
    }

    override fun validateContext(contextData: Map<String, Any>): Boolean {
        return contextData.containsKey("reason") && contextData.containsKey("timestamp")
    }
}
