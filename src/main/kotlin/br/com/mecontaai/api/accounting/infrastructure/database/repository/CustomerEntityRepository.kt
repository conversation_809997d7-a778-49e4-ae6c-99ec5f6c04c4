package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.CustomerEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.CompanyEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

/**
 * JPA repository for CustomerEntity.
 */
@Repository
interface CustomerEntityRepository : JpaRepository<CustomerEntity, Long> {
    /**
     * Find a customer by UUID.
     *
     * @param uuid The customer UUID
     * @return The customer if found, wrapped in Optional
     */
    fun findByUuid(uuid: UUID): Optional<CustomerEntity>

    /**
     * Find a customer by document.
     *
     * @param document The customer document (CPF/CNPJ)
     * @return The customer if found, wrapped in Optional
     */
    fun findByDocument(document: String): Optional<CustomerEntity>

    /**
     * Find customers by company.
     *
     * @param company The company entity
     * @return List of customers belonging to the company
     */
    fun findByCompany(company: CompanyEntity): List<CustomerEntity>

    /**
     * Find a customer by document and company.
     *
     * @param document The customer document (CPF/CNPJ)
     * @param company The company entity
     * @return The customer if found, wrapped in Optional
     */
    fun findByDocumentAndCompany(document: String, company: CompanyEntity): Optional<CustomerEntity>
} 