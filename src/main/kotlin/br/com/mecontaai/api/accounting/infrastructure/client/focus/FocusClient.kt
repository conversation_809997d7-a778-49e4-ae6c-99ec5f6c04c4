package br.com.mecontaai.api.accounting.infrastructure.client.focus

import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.CnpjConsultaResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusCreateCompanyRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusCreateCompanyResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusUpdateCertificateRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusUpdateCertificateResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseCancelamentoRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseCancelamentoResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEmailRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEmailResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEnvioRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEnvioResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseStatusResponseDto

/**
 * Interface defining the contract for interacting with the FocusNFe API for NFSe operations.
 *
 * This client follows an asynchronous processing model where:
 * 1. A document is submitted for processing
 * 2. The status is later checked to determine if processing was successful
 */
interface FocusClient {
    /**
     * Consulta dados de um CNPJ através da API do Focus.
     *
     * @param cnpj CNPJ a ser consultado (pode incluir formatação)
     * @return Dados do CNPJ
     * @throws FocusException se o CNPJ não for encontrado ou ocorrer outro erro na consulta
     */
    fun consultarCnpj(cnpj: String): CnpjConsultaResponseDto

    /**
     * Creates and submits an NFSe (Services Invoice) for processing
     *
     * @param reference Unique reference identifier for tracking the invoice
     * @param request Request payload containing all NFSe data
     * @return Response indicating the document has been queued for processing
     * @throws FocusException if the API rejects the request or returns an error
     */
    fun createNfse(
        reference: String,
        request: NfseEnvioRequestDto,
        token: String,
    ): NfseEnvioResponseDto

    /**
     * Retrieves the current status of a previously submitted NFSe
     *
     * @param reference Unique reference identifier for the invoice
     * @return Response containing the current status (processing, authorized, error, etc.)
     * @throws FocusException if the reference is not found or the API returns an error
     */
    fun getNfseStatus(reference: String, token: String): NfseStatusResponseDto

    /**
     * Cancels a previously authorized NFSe
     *
     * @param reference Unique reference identifier for the invoice
     * @param request Request containing cancellation justification
     * @return Response indicating success or failure of the cancellation request
     * @throws FocusException if cancellation fails or the API returns an error
     */
    fun cancelNfse(
        reference: String,
        request: NfseCancelamentoRequestDto,
    ): NfseCancelamentoResponseDto

    /**
     * Sends an email with a copy of the NFSe to specified recipients
     *
     * @param reference Unique reference identifier for the invoice
     * @param request Request containing the email addresses to send to
     * @return Response indicating success or failure of the email sending
     * @throws FocusException if email sending fails or the API returns an error
     */
    fun sendNfseEmail(
        reference: String,
        request: NfseEmailRequestDto,
    ): NfseEmailResponseDto

    fun createCompany(request: FocusCreateCompanyRequestDto): FocusCreateCompanyResponseDto

    /**
     * Atualiza o certificado de uma empresa na Focus.
     *
     * @param companyId ID da empresa na Focus
     * @param request Dados do certificado
     * @return Resposta com os dados do certificado atualizado
     * @throws FocusException se ocorrer algum erro na atualização do certificado
     */
    fun updateCertificate(
        companyId: String,
        request: FocusUpdateCertificateRequestDto,
    ): FocusUpdateCertificateResponseDto
}
