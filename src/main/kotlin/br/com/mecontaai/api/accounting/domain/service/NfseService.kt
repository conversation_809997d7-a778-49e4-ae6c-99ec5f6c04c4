package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.entity.Customer
import br.com.mecontaai.api.accounting.domain.entity.Nfse
import br.com.mecontaai.api.accounting.domain.enum.DocumentType
import br.com.mecontaai.api.accounting.domain.enum.NfseStatus
import br.com.mecontaai.api.accounting.domain.exception.NfseException
import br.com.mecontaai.api.accounting.domain.repository.NfseRepository
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusClient
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusException
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.EnderecoDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseCancelamentoRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEmailRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEnvioRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.PrestadorDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.ServicoDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.TomadorDto
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*

/**
 * Domain service for NFSe operations.
 */
@Service
class NfseService(
    private val nfseRepository: NfseRepository,
    private val focusClient: FocusClient,
) {
    /**
     * Creates a new NFSe for a service provided to a customer.
     *
     * @param companyId ID da empresa emissora
     * @param focusKey Token de autenticação Focus NFe da empresa
     * @param customer The customer (service recipient)
     * @param valorServico The value of the service
     * @param discriminacao Description of the service
     * @param dataEmissao Date of service provision
     * @param itemListaServico Service code according to LC 116/2003
     * @param issRetido Whether ISS tax is retained
     * @param aliquota ISS tax rate
     * @param codigoMunicipioPrestador Municipality code of the service provider
     * @param inscricaoMunicipalPrestador Municipal registration number of the provider
     * @param cnpjPrestador CNPJ of the service provider
     * @return The created NFSe with its status
     */
    fun createNfse(
        companyId: Long,
        token: String,
        customer: Customer,
        valorServico: BigDecimal,
        discriminacao: String,
        dataEmissao: LocalDateTime = LocalDateTime.now(),
        itemListaServico: String,
        issRetido: Boolean = false,
        aliquota: Double? = null,
        codigoMunicipioPrestador: String,
        inscricaoMunicipalPrestador: String,
        cnpjPrestador: String,
        codigoTributarioMunicipio: String? = null,
    ): Nfse {
        // Generate a unique reference
        val reference = UUID.randomUUID().toString()

        // Create request DTO
        val requestDto =
            NfseEnvioRequestDto(
                data_emissao = dataEmissao,
                prestador =
                    PrestadorDto(
                        cnpj = cnpjPrestador,
                        inscricao_municipal = inscricaoMunicipalPrestador,
                        codigo_municipio = codigoMunicipioPrestador,
                    ),
                tomador = createTomadorDto(customer),
                servico =
                    ServicoDto(
                        valor_servicos = valorServico,
                        issRetido = issRetido,
                        item_lista_servico = itemListaServico,
                        discriminacao = discriminacao,
                        codigo_municipio = codigoMunicipioPrestador,
                        aliquota = aliquota,
                        codigo_tributario_municipio = codigoTributarioMunicipio,
                    ),
                natureza_operacao = 1, // Default: Taxation in the municipality
                optante_simples_nacional = true, // Default: Simplified tax regime
            )

        try {
            // Call the Focus API with company-specific token
            val response = focusClient.createNfse(reference, requestDto, token)

            // Create a domain entity from the response
            val nfse =
                Nfse(
                    uuid = UUID.randomUUID(),
                    companyId = companyId,
                    customerId = customer.id ?: 0L,
                    description = discriminacao,
                    amount = valorServico,
                    status = NfseStatus.PROCESSING_AUTHORIZATION.name,
                    externalProcessId = reference,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                )

            // Save to repository
            return nfseRepository.save(nfse)
        } catch (e: FocusException) {
            throw NfseException("Failed to create NFSe: ${e.message}", e)
        }
    }

    /**
     * Gets the current status of an NFSe.
     *
     * @param reference The unique reference of the NFSe
     * @return The NFSe with its current status
     */
    fun getNfseStatus(reference: String): Nfse =
        nfseRepository.findByReference(reference)
            ?: throw NfseException("NFSe with reference $reference not found")

    /**
     * Finds an NFSe by its UUID.
     *
     * @param uuid The NFSe UUID
     * @return The NFSe if found, null otherwise
     */
    fun findByUuid(uuid: UUID): Nfse? = nfseRepository.findByUuid(uuid)

    /**
     * Cancels an NFSe.
     *
     * @param reference The unique reference of the NFSe
     * @param justificativa Justification for the cancellation
     * @param focusKey Token de autenticação Focus NFe da empresa
     * @return The updated NFSe
     */
    fun cancelNfse(
        reference: String,
        justificativa: String,
        focusKey: String,
    ): Nfse {
        val nfse =
            nfseRepository.findByReference(reference)
                ?: throw NfseException("NFSe with reference $reference not found")

        if (nfse.status != "autorizado") {
            throw NfseException("Cannot cancel NFSe with status ${nfse.status}. Only authorized NFSes can be canceled.")
        }

        try {
            val requestDto = NfseCancelamentoRequestDto(justificativa = justificativa)
            val response = focusClient.cancelNfse(reference, requestDto)

            if (response.status == "cancelado") {
                return nfseRepository.updateStatus(reference, "cancelado")
                    ?: throw NfseException("Failed to update NFSe status after cancellation")
            } else {
                val errorMessage = response.erros?.joinToString(", ") { "${it.codigo}: ${it.mensagem}" }
                return nfseRepository.updateStatus(reference, "erro_cancelamento", errorMessage)
                    ?: throw NfseException("Failed to update NFSe status after cancellation error")
            }
        } catch (e: FocusException) {
            throw NfseException("Failed to cancel NFSe: ${e.message}", e)
        }
    }

    /**
     * Sends a copy of the NFSe via email.
     *
     * @param reference The unique reference of the NFSe
     * @param emails List of email addresses to send to
     * @param focusKey Token de autenticação Focus NFe da empresa
     */
    fun sendNfseEmail(
        reference: String,
        emails: List<String>,
    ) {
        val nfse =
            nfseRepository.findByReference(reference)
                ?: throw NfseException("NFSe with reference $reference not found")

        if (nfse.status != "autorizado" && nfse.status != "cancelado") {
            throw NfseException("Cannot send email for NFSe with status ${nfse.status}. Only authorized or canceled NFSes can be sent.")
        }

        try {
            val requestDto = NfseEmailRequestDto(emails = emails)
            focusClient.sendNfseEmail(reference, requestDto)
        } catch (e: FocusException) {
            throw NfseException("Failed to send NFSe email: ${e.message}", e)
        }
    }

    /**
     * Gets all NFSes for a customer.
     *
     * @param customerId The customer ID
     * @return List of NFSes for the customer
     */
    fun getNfsesByCustomer(customerId: Long): List<Nfse> = nfseRepository.findByCustomerId(customerId)

    /**
     * Updates the status of an NFSe.
     *
     * @param reference The unique reference identifier
     * @param status The new status
     * @param mensagemErro Optional error message
     * @return The updated NFSe
     */
    fun updateNfseStatus(
        reference: String,
        status: String,
        mensagemErro: String? = null,
    ): Nfse? = nfseRepository.updateStatus(reference, status, mensagemErro)

    /**
     * Updates the blob UUIDs of an NFSe.
     *
     * @param reference The unique reference identifier
     * @param xmlBlobUuid UUID of the XML blob
     * @param pdfBlobUuid UUID of the PDF blob
     * @return The updated NFSe
     */
    fun updateNfseBlobs(
        reference: String,
        xmlBlobUuid: java.util.UUID? = null,
        pdfBlobUuid: java.util.UUID? = null,
    ): Nfse? = nfseRepository.updateBlobs(reference, xmlBlobUuid, pdfBlobUuid)

    // Helper method to create a TomadorDto from a Customer entity
    private fun createTomadorDto(customer: Customer): TomadorDto {
        val documentField =
            when (customer.documentType) {
                DocumentType.CPF -> TomadorDto(cpf = customer.document)
                DocumentType.CNPJ -> TomadorDto(cnpj = customer.document)
                DocumentType.FOREIGNER -> TomadorDto(cpf = null, cnpj = null) // Para estrangeiros, não usamos CPF ou CNPJ
                // Caso seja adicionado mais tipos no futuro
                else -> throw NfseException("Invalid document type: ${customer.documentType.code}. Must be CPF or CNPJ.")
            }

        // Obter o endereço principal (marcado como primário) ou o primeiro endereço da lista
        val primaryAddress = customer.addresses.firstOrNull { it.isPrimary } ?: customer.addresses.firstOrNull()

        return TomadorDto(
            cnpj = documentField.cnpj,
            cpf = documentField.cpf,
            razao_social = customer.name,
            email = customer.email,
            telefone = customer.phone,
            endereco =
                primaryAddress?.let {
                    EnderecoDto(
                        logradouro = it.street,
                        numero = it.number,
                        complemento = it.complement,
                        bairro = it.neighborhood,
                        codigo_municipio = null, // Address não tem mais cityCode, precisaríamos estender
                        uf = it.state,
                        cep = it.zipCode,
                    )
                },
        )
    }
}
