package br.com.mecontaai.api.accounting.domain.entity

import br.com.mecontaai.api.accounting.infrastructure.database.model.BaseEntity
import java.time.LocalDateTime
import java.util.UUID

/**
 * Representa uma empresa no domínio
 */
class Company(
    override var id: Long? = null,
    override var uuid: UUID = UUID.randomUUID(),
    val name: String,
    val document: String,
    val municipalRegistration: String? = null,
    val stateRegistration: String? = null,
    val simplesNacional: Boolean = false,
    val codigoMunicipio: String? = null,
    val codigoSiafi: String? = null,
    val codigoIbge: String? = null,
    val naturezaJuridica: String?,
    var focusId: String? = null,
    val token: String? = null,
    override var isActive: Boolean = true,
    override var createdAt: LocalDateTime = LocalDateTime.now(),
    override var updatedAt: LocalDateTime = LocalDateTime.now()
) : BaseEntity()