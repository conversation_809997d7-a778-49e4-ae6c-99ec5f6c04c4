package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.entity.UserChat

/**
 * Service interface for ChatUser operations.
 */
interface ChatUserService {
    /**
     * Find user chat relationships by chat ID.
     *
     * @param chatId The chat ID
     * @return List of user chat relationships for the chat
     */
    fun findByChatId(chatId: Long): UserChat?

    /**
     * Find user chat relationship by company ID.
     *
     * @param companyId The company ID
     * @return User chat relationship for the company, or null if not found
     */
    fun findByCompanyId(companyId: Long): UserChat?
} 