package br.com.mecontaai.api.accounting.domain.strategy

import br.com.mecontaai.api.accounting.domain.dto.CompanyValidationResultDto
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto

/**
 * Interface para estratégias de validação baseadas na natureza jurídica.
 */
interface CompanyValidationStrategy {
    /**
     * Valida os dados específicos da natureza jurídica.
     * 
     * @param cnpjData Dados do CNPJ obtidos da Receita Federal
     * @return Resultado da validação ou null se a validação passou
     */
    fun validate(cnpjData: ReceitaWsResponseDto): CompanyValidationResultDto?
}
