package br.com.mecontaai.api.accounting.infrastructure.client.focus

import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.CnpjConsultaResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusCreateCompanyRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusCreateCompanyResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusErrorDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusUpdateCertificateRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusUpdateCertificateResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseCancelamentoRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseCancelamentoResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEmailRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEmailResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEnvioRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseEnvioResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.NfseStatusResponseDto
import br.com.mecontaai.api.accounting.infrastructure.config.FocusConfig
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import feign.Feign
import feign.Headers
import feign.Logger
import feign.Param
import feign.RequestInterceptor
import feign.RequestLine
import feign.Response
import feign.codec.ErrorDecoder
import feign.jackson.JacksonDecoder
import feign.jackson.JacksonEncoder
import feign.okhttp.OkHttpClient
import feign.slf4j.Slf4jLogger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.util.*

/**
 * Feign interface for the Focus NFe API, defining the HTTP endpoints.
 */
@Headers("Content-Type: application/json")
interface FocusHttpApi {
    /**
     * Consulta dados de um CNPJ na API do Focus.
     *
     * @param cnpj CNPJ a ser consultado (apenas números)
     * @return Resposta com os dados do CNPJ
     */
    @RequestLine("GET /v2/cnpjs/{cnpj}")
    fun consultarCnpj(
        @Param("cnpj") cnpj: String,
    ): CnpjConsultaResponseDto

    /**
     * Creates a new NFSe.
     *
     * @param reference Unique reference identifier for tracking the invoice
     * @param request Request payload containing all NFSe data
     * @return Response indicating the document has been queued for processing
     */
    @RequestLine("POST /v2/nfse?ref={reference}")
    fun createNfse(
        @Param("reference") reference: String,
        request: NfseEnvioRequestDto,
    ): NfseEnvioResponseDto

    /**
     * Retrieves the status of an NFSe.
     *
     * @param reference Unique reference identifier for the invoice
     * @return Response containing the current status
     */
    @RequestLine("GET /v2/nfse/{reference}")
    fun getNfseStatus(
        @Param("reference") reference: String,
    ): NfseStatusResponseDto

    /**
     * Cancels an NFSe.
     *
     * @param reference Unique reference identifier for the invoice
     * @param request Request containing cancellation justification
     * @return Response indicating success or failure of the cancellation
     */
    @RequestLine("DELETE /v2/nfse/{reference}")
    fun cancelNfse(
        @Param("reference") reference: String,
        request: NfseCancelamentoRequestDto,
    ): NfseCancelamentoResponseDto

    /**
     * Sends an email with the NFSe.
     *
     * @param reference Unique reference identifier for the invoice
     * @param request Request containing the email addresses
     * @return Response indicating success or failure of the email sending
     */
    @RequestLine("POST /v2/nfse/{reference}/email")
    fun sendNfseEmail(
        @Param("reference") reference: String,
        request: NfseEmailRequestDto,
    ): NfseEmailResponseDto

    /**
     * Cria uma nova empresa na Focus.
     *
     * @param request Dados da empresa a ser criada
     * @return Resposta com os dados da empresa criada
     */
    @RequestLine("POST /v2/empresas")
    fun createCompany(request: FocusCreateCompanyRequestDto): FocusCreateCompanyResponseDto

    /**
     * Atualiza o certificado de uma empresa na Focus.
     *
     * @param companyId ID da empresa na Focus
     * @param request Dados do certificado
     * @return Resposta com os dados do certificado atualizado
     */
    @RequestLine("PUT /v2/empresas/{companyId}")
    fun updateCertificate(
        @Param("companyId") companyId: String,
        request: FocusUpdateCertificateRequestDto,
    ): FocusUpdateCertificateResponseDto
}

/**
 * Implementation of the FocusClient interface using Feign HTTP client.
 */
@Component
class FocusHttpClient(
    private val focusConfig: FocusConfig,
    private val objectMapper: ObjectMapper,
) : FocusClient {
    private val logger = LoggerFactory.getLogger(javaClass)

    companion object {
        /**
         * URL de produção da Focus API.
         */
        private const val FOCUS_PRODUCTION_URL = "https://api.focusnfe.com.br"
    }

    /**
     * Endpoints que SEMPRE devem usar a URL de produção, mesmo em ambiente de homologação.
     *
     * ⚠️ IMPORTANTE: Alguns endpoints da Focus API só funcionam em produção.
     *
     * 📝 COMO ADICIONAR NOVOS ENDPOINTS:
     * 1. Adicione uma nova entrada neste enum com descrição clara
     * 2. Use createProductionOnlyHttpClient() no método correspondente
     * 3. Documente no método que ele sempre usa produção
     *
     * 🔍 ENDPOINTS CONHECIDOS QUE SÓ FUNCIONAM EM PRODUÇÃO:
     * - consultarCnpj: Consulta de CNPJ na Receita Federal
     */
    private enum class ProductionOnlyEndpoint(
        val description: String,
    ) {
        CONSULTAR_CNPJ("Consulta de CNPJ - só funciona em produção"),
        CREATE_COMPANY("Criação de Companhia - só funciona em produção"),
        UPDATE_COMPANY_CERTIFICATE("Upload de Certificado de Companhia - só funciona em produção"),

        // 📋 TODO: Adicione aqui outros endpoints que só funcionam em produção
        // Exemplo: CONSULTAR_CEP("Consulta de CEP - só funciona em produção"),
        // Exemplo: VALIDAR_CERTIFICADO("Validação de certificado - só funciona em produção"),
    }

    private fun createHttpClient(token: String): FocusHttpApi = createHttpClientWithUrl(token, focusConfig.apiUrl)

    /**
     * Creates an HTTP client for the Focus API with the given token and specific URL.
     * Usado para casos especiais como consultarCnpj que sempre deve usar produção.
     */
    private fun createHttpClientWithUrl(
        token: String,
        baseUrl: String,
    ): FocusHttpApi {
        val objectMapper =
            ObjectMapper()
                .registerModule(JavaTimeModule())
                .registerKotlinModule()

        val authInterceptor =
            RequestInterceptor { requestTemplate ->
                // Adiciona autenticação Basic no header usando o token específico
                val encodedAuth = Base64.getEncoder().encodeToString(token.toByteArray())
                requestTemplate.header("Authorization", "Basic $encodedAuth")
            }

        return Feign
            .builder()
            .client(OkHttpClient())
            .encoder(JacksonEncoder(objectMapper))
            .decoder(JacksonDecoder(objectMapper))
            .logger(Slf4jLogger(this.javaClass.canonicalName))
            .logLevel(Logger.Level.FULL)
            .errorDecoder(FocusErrorDecoder(objectMapper))
            .requestInterceptor(authInterceptor)
            .target(FocusHttpApi::class.java, baseUrl)
    }

    /**
     * Creates an HTTP client for endpoints that ALWAYS use production URL.
     *
     * IMPORTANTE: Use este método para endpoints que só funcionam em produção.
     * Adicione novos endpoints no enum ProductionOnlyEndpoint.
     *
     * @param token Token de autenticação
     * @param endpoint Endpoint que sempre usa produção (para documentação)
     * @return Cliente HTTP configurado para produção
     */
    private fun createProductionOnlyHttpClient(
        token: String,
        endpoint: ProductionOnlyEndpoint,
    ): FocusHttpApi {
        logger.debug("🏭 Usando URL de produção para ${endpoint.description}: $FOCUS_PRODUCTION_URL")
        return createHttpClientWithUrl(token, FOCUS_PRODUCTION_URL)
    }

    /**
     * Consulta dados de um CNPJ através da API do Focus.
     *
     * ⚠️ IMPORTANTE: Este endpoint SEMPRE usa a URL de produção, mesmo em homologação,
     * pois a consulta de CNPJ só funciona no ambiente de produção da Focus API.
     *
     * @param cnpj CNPJ a ser consultado (pode incluir formatação)
     * @return Dados do CNPJ
     * @throws FocusException se o CNPJ não for encontrado ou ocorrer outro erro na consulta
     */
    override fun consultarCnpj(cnpj: String): CnpjConsultaResponseDto {
        // Remove caracteres não numéricos do CNPJ
        val cnpjLimpo = cnpj.replace(Regex("[^0-9]"), "")

        return try {
            // SEMPRE usa URL de produção para consultarCnpj, mesmo em homologação
            val response =
                createProductionOnlyHttpClient(
                    focusConfig.tokenPrincipal,
                    ProductionOnlyEndpoint.CONSULTAR_CNPJ,
                ).consultarCnpj(cnpjLimpo)
            val responseJson = objectMapper.writeValueAsString(response)
            logger.info("✅ Focus API - GET /v2/cnpjs/$cnpjLimpo [200] - Response: $responseJson")
            response
        } catch (e: FocusException) {
            val errorJson = e.originalJson?.let { " - Response: $it" } ?: ""
            logger.error("❌ Focus API - GET /v2/cnpjs/$cnpjLimpo [${e.httpStatus}] - Error: ${e.message}$errorJson")
            throw e
        } catch (e: Exception) {
            logger.error("❌ Focus API - GET /v2/cnpjs/$cnpjLimpo [ERROR] - Exception: ${e.message}", e)
            throw FocusException("Erro ao consultar CNPJ: ${e.message}", e)
        }
    }

    override fun createNfse(
        reference: String,
        request: NfseEnvioRequestDto,
        token: String,
    ): NfseEnvioResponseDto {
        val requestJson = objectMapper.writeValueAsString(request)
        logger.info("📤 Focus API - POST /v2/nfse?ref=$reference - Request: $requestJson")

        return try {
            val response = createHttpClient(token).createNfse(reference, request)
            val responseJson = objectMapper.writeValueAsString(response)
            logger.info("✅ Focus API - POST /v2/nfse?ref=$reference [200] - Response: $responseJson")
            response
        } catch (e: FocusException) {
            val errorJson = e.originalJson?.let { " - Response: $it" } ?: ""
            logger.error(
                "❌ Focus API - POST /v2/nfse?ref=$reference [${e.httpStatus}] - Error: ${e.message} - Request: $requestJson$errorJson",
            )
            throw e
        } catch (e: Exception) {
            logger.error("❌ Focus API - POST /v2/nfse?ref=$reference [ERROR] - Exception: ${e.message} - Request: $requestJson", e)
            throw FocusException("Error submitting NFSe: ${e.message}", e)
        }
    }

    override fun getNfseStatus(
        reference: String,
        token: String,
    ): NfseStatusResponseDto {
        logger.info("📋 Focus API - GET /v2/nfse/$reference")

        return try {
            val response = createHttpClient(token).getNfseStatus(reference)
            val responseJson = objectMapper.writeValueAsString(response)
            logger.info("✅ Focus API - GET /v2/nfse/$reference [200] - Response: $responseJson")
            response
        } catch (e: FocusException) {
            val errorJson = e.originalJson?.let { " - Response: $it" } ?: ""
            logger.error("❌ Focus API - GET /v2/nfse/$reference [${e.httpStatus}] - Error: ${e.message}$errorJson")
            throw e
        } catch (e: Exception) {
            logger.error("❌ Focus API - GET /v2/nfse/$reference [ERROR] - Exception: ${e.message}", e)
            throw FocusException("Error retrieving NFSe status: ${e.message}", e)
        }
    }

    override fun cancelNfse(
        reference: String,
        request: NfseCancelamentoRequestDto,
    ): NfseCancelamentoResponseDto {
        val requestJson = objectMapper.writeValueAsString(request)
        logger.info("🗑️ Focus API - DELETE /v2/nfse/$reference - Request: $requestJson")

        return try {
            val response = createHttpClient(focusConfig.tokenPrincipal).cancelNfse(reference, request)
            val responseJson = objectMapper.writeValueAsString(response)
            logger.info("✅ Focus API - DELETE /v2/nfse/$reference [200] - Response: $responseJson")
            response
        } catch (e: FocusException) {
            val errorJson = e.originalJson?.let { " - Response: $it" } ?: ""
            logger.error(
                "❌ Focus API - DELETE /v2/nfse/$reference [${e.httpStatus}] - Error: ${e.message} - Request: $requestJson$errorJson",
            )
            throw e
        } catch (e: Exception) {
            logger.error("❌ Focus API - DELETE /v2/nfse/$reference [ERROR] - Exception: ${e.message} - Request: $requestJson", e)
            throw FocusException("Error canceling NFSe: ${e.message}", e)
        }
    }

    override fun sendNfseEmail(
        reference: String,
        request: NfseEmailRequestDto,
    ): NfseEmailResponseDto {
        val requestJson = objectMapper.writeValueAsString(request)
        logger.info("📧 Focus API - POST /v2/nfse/$reference/email - Request: $requestJson")

        return try {
            val response = createHttpClient(focusConfig.tokenPrincipal).sendNfseEmail(reference, request)
            val responseJson = objectMapper.writeValueAsString(response)
            logger.info("✅ Focus API - POST /v2/nfse/$reference/email [200] - Response: $responseJson")
            response
        } catch (e: FocusException) {
            val errorJson = e.originalJson?.let { " - Response: $it" } ?: ""
            logger.error(
                "❌ Focus API - POST /v2/nfse/$reference/email [${e.httpStatus}] - Error: ${e.message} - Request: $requestJson$errorJson",
            )
            throw e
        } catch (e: Exception) {
            logger.error("❌ Focus API - POST /v2/nfse/$reference/email [ERROR] - Exception: ${e.message} - Request: $requestJson", e)
            throw FocusException("Error sending NFSe email: ${e.message}", e)
        }
    }

    override fun createCompany(request: FocusCreateCompanyRequestDto): FocusCreateCompanyResponseDto {
        val requestJson = objectMapper.writeValueAsString(request)
        logger.info("🏢 Focus API - POST /v2/empresas - Request: $requestJson")

        return try {
            val response =
                createProductionOnlyHttpClient(
                    focusConfig.tokenPrincipal,
                    ProductionOnlyEndpoint.CREATE_COMPANY,
                ).createCompany(request)

            val responseJson = objectMapper.writeValueAsString(response)
            logger.info("✅ Focus API - POST /v2/empresas [200] - Response: $responseJson")
            response
        } catch (e: FocusException) {
            val errorJson = e.originalJson?.let { " - Response: $it" } ?: ""
            logger.error("❌ Focus API - POST /v2/empresas [${e.httpStatus}] - Error: ${e.message} - Request: $requestJson$errorJson")
            throw e
        } catch (e: Exception) {
            logger.error("❌ Focus API - POST /v2/empresas [ERROR] - Exception: ${e.message} - Request: $requestJson", e)
            throw FocusException("Erro ao criar empresa: ${e.message}", e)
        }
    }

    override fun updateCertificate(
        companyId: String,
        request: FocusUpdateCertificateRequestDto,
    ): FocusUpdateCertificateResponseDto {
        val requestJson = objectMapper.writeValueAsString(request)
        logger.info("🔐 Focus API - PUT /v2/empresas/$companyId - Request: $requestJson")

        return try {
            val response =
                createProductionOnlyHttpClient(
                    focusConfig.tokenPrincipal,
                    ProductionOnlyEndpoint.UPDATE_COMPANY_CERTIFICATE,
                ).updateCertificate(companyId, request)
            val responseJson = objectMapper.writeValueAsString(response)
            logger.info("✅ Focus API - PUT /v2/empresas/$companyId [200] - Response: $responseJson")
            response
        } catch (e: FocusException) {
            val errorJson = e.originalJson?.let { " - Response: $it" } ?: ""
            logger.error(
                "❌ Focus API - PUT /v2/empresas/$companyId [${e.httpStatus}] - Error: ${e.message} - Request: $requestJson$errorJson",
            )
            throw e
        } catch (e: Exception) {
            logger.error("❌ Focus API - PUT /v2/empresas/$companyId [ERROR] - Exception: ${e.message} - Request: $requestJson", e)
            throw FocusException("Erro ao atualizar certificado: ${e.message}", e)
        }
    }
}

/**
 * Custom error decoder for the Focus API.
 */
class FocusErrorDecoder(
    private val objectMapper: ObjectMapper,
) : ErrorDecoder {
    override fun decode(
        methodKey: String,
        response: Response,
    ): Exception {
        val statusCode = response.status()

        // If it's not an error, don't transform
        if (statusCode < 400) {
            return feign.codec.ErrorDecoder
                .Default()
                .decode(methodKey, response)
        }

        try {
            val body = response.body()
            val bodyString =
                body?.let {
                    String(it.asInputStream().readBytes(), StandardCharsets.UTF_8)
                } ?: ""

            if (bodyString.isNotBlank()) {
                try {
                    val errorDto = objectMapper.readValue(bodyString, FocusErrorDto::class.java)
                    return FocusException(statusCode, errorDto, bodyString)
                } catch (e: Exception) {
                    // If we can't parse the error as a FocusErrorDto, use the raw body
                    return FocusException(statusCode, bodyString, bodyString)
                }
            } else {
                return FocusException(statusCode, "HTTP error $statusCode without body")
            }
        } catch (e: IOException) {
            return FocusException(statusCode, "Error parsing error response: ${e.message}")
        }
    }
}
