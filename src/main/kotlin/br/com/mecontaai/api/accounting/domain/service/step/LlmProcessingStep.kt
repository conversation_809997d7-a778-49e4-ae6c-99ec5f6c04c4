package br.com.mecontaai.api.accounting.domain.service.step

import br.com.mecontaai.api.accounting.domain.model.ChatMessageEvent
import br.com.mecontaai.api.accounting.domain.model.response.LlmProcessingResult
import br.com.mecontaai.api.accounting.domain.service.LlmContextException
import br.com.mecontaai.api.accounting.domain.service.LlmContextManager
import br.com.mecontaai.api.accounting.domain.service.ResponseExtractionException
import br.com.mecontaai.api.accounting.domain.service.llm.LlmService
import br.com.mecontaai.api.accounting.domain.service.llm.assistants.LlmAssistant
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.dto.LlmResponse
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.logging.JsonLogger
import br.com.mecontaai.api.accounting.infrastructure.logging.infoJson
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Step responsible for processing the message with LLM.
 * This step focuses solely on AI processing and delegates context management
 * and response extraction to specialized components.
 */
@Component
class LlmProcessingStep(
    private val llmService: LlmService,
    private val defaultAssistant: LlmAssistant,
    private val llmContextManager: LlmContextManager,
    private val jsonLogger: JsonLogger,
) {
    private val logger = LoggerFactory.getLogger(LlmProcessingStep::class.java)

    /**
     * Processes the message with LLM and returns agent responses.
     * This is the new method that supports complex responses (buttons, images, etc.).
     *
     * @param chatMessageEvent The chat message event to process
     * @param chat The chat entity for context
     * @return Enhanced LLM processing result with agent responses
     */
    fun execute(
        chatMessageEvent: ChatMessageEvent,
        chat: ChatEntity,
    ): LlmProcessingResult {
        logger.debug("Processing message with LLM: ${chatMessageEvent.externalMessageId}")

        return try {
            // Process the message using LlmService
            val llmResponse = processWithLlm(chatMessageEvent)
            logger.infoJson("Generated LLM response:", llmResponse, jsonLogger)

            // Save context using specialized manager
            llmContextManager.saveResponseContext(llmResponse, chat)

            logger.info("LLM processing completed successfully for message: ${chatMessageEvent.externalMessageId}")
            LlmProcessingResult.success(llmResponse.agentResponses)
        } catch (e: Exception) {
            logger.error("Error processing message with LLM: ${chatMessageEvent.externalMessageId}", e)
            LlmProcessingResult.error(getErrorMessage(e))
        }
    }

    /**
     * Processes the message with LLM service.
     */
    private fun processWithLlm(event: ChatMessageEvent): LlmResponse =
        llmService.processUserMessage(
            assistant = defaultAssistant,
            chatMessageEvent = event,
            useFallbackProvider = false,
            useFallbackModel = false,
        )

    /**
     * Determines appropriate error message based on exception type.
     */
    private fun getErrorMessage(exception: Exception): String =
        when (exception) {
            is ResponseExtractionException -> "Não foi possível processar a resposta do assistente"
            is LlmContextException -> "Resposta gerada, mas houve problema ao salvar o contexto"
            else -> "Desculpe, tive um problema, tente novamente"
        }
}
