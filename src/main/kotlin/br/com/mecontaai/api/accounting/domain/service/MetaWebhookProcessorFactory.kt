package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload
import org.springframework.stereotype.Component

@Component
class MetaWebhookProcessorFactory(
    private val processors: List<MetaWebhookProcessor>,
) {
    fun getProcessor(payload: MetaWebhookPayload): MetaWebhookProcessor? = processors.find { it.canProcess(payload) }
}
