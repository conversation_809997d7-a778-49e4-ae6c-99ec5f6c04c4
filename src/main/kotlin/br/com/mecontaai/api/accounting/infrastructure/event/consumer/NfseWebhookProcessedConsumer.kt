package br.com.mecontaai.api.accounting.infrastructure.event.consumer

import br.com.mecontaai.api.accounting.domain.repository.CompanyRepository
import br.com.mecontaai.api.accounting.domain.repository.NfseRepository
import br.com.mecontaai.api.accounting.domain.service.ChatService
import br.com.mecontaai.api.accounting.domain.service.MessageSenderService
import br.com.mecontaai.api.accounting.domain.service.NfseService
import br.com.mecontaai.api.accounting.infrastructure.event.SqsQueues
import br.com.mecontaai.api.accounting.infrastructure.event.dto.NfseWebhookProcessedDto
import com.fasterxml.jackson.databind.ObjectMapper
import io.awspring.cloud.sqs.annotation.SqsListener
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.util.*

/**
 * Infrastructure consumer for processing NfseWebhookProcessedDto objects from SQS queue.
 * This consumer processes NFSe webhook events that have been successfully processed.
 *
 * Responsibilities:
 * - Consumes messages from SQS queue
 * - Deserializes NfseWebhookProcessedDto
 * - Logs the processed NFSe information
 * - Sends PDF to user via WhatsApp
 */
@Component
class NfseWebhookProcessedConsumer(
    private val objectMapper: ObjectMapper,
    private val nfseService: NfseService,
    private val companyRepository: CompanyRepository,
    private val chatService: ChatService,
    private val messageSenderService: MessageSenderService,
) {
    private val logger = LoggerFactory.getLogger(NfseWebhookProcessedConsumer::class.java)

    @SqsListener(SqsQueues.NFSE_WEBHOOK_PROCESSED)
    fun handleNfseWebhookProcessed(message: String) {
        try {
            val nfseWebhookProcessedDto = objectMapper.readValue(message, NfseWebhookProcessedDto::class.java)
            logger.info("Processing NfseWebhookProcessedDto: NFSe UUID: ${nfseWebhookProcessedDto.nfseUuid}, PDF Blob UUID: ${nfseWebhookProcessedDto.pdfBlobUuid}")

            // Log the processed NFSe information
            logger.info("NFSe webhook processed successfully - NFSe UUID: ${nfseWebhookProcessedDto.nfseUuid}")
            
            if (nfseWebhookProcessedDto.pdfBlobUuid != null) {
                logger.info("PDF blob available for NFSe - NFSe UUID: ${nfseWebhookProcessedDto.nfseUuid}, PDF Blob UUID: ${nfseWebhookProcessedDto.pdfBlobUuid}")
                
                // Send PDF to user
                sendPdfToUser(nfseWebhookProcessedDto.nfseUuid, nfseWebhookProcessedDto.pdfBlobUuid, nfseWebhookProcessedDto.nfsePdfUrl)
            } else {
                logger.info("No PDF blob available for NFSe - NFSe UUID: ${nfseWebhookProcessedDto.nfseUuid}")
            }

            logger.info("NfseWebhookProcessedDto processed successfully: ${nfseWebhookProcessedDto.nfseUuid}")
        } catch (e: Exception) {
            logger.error("Error processing NfseWebhookProcessedDto: ${e.message}", e)
            // throw e // This will cause SQS to retry the message
        }
    }
    
    private fun sendPdfToUser(nfseUuid: UUID, pdfBlobUuid: UUID, nfsePdfUrl: String) {
        try {
            // Buscar NFSe pelo UUID (usando findByReference com o UUID como reference)
            val nfse = nfseService.findByUuid(nfseUuid)
            if (nfse == null) {
                logger.error("NFSe não encontrada - UUID: $nfseUuid")
                return
            }
            
            // Buscar Company pelo ID da NFSe
            val company = companyRepository.findById(nfse.companyId)
            if (company == null) {
                logger.error("Company não encontrada - ID: ${nfse.companyId}")
                return
            }
            
            // Buscar Chat usando o ChatService pelo companyId
            val chat = chatService.findById(nfse.companyId)
            if (chat == null) {
                logger.error("Chat não encontrado para Company - ID: ${nfse.companyId}")
                return
            }
            
            logger.info("Encontrados: NFSe ${nfse.externalProcessId}, Company ${company.name}, Chat ${chat.id}")
            
            // Enviar mensagem para o usuário via WhatsApp
            val message = "📄Sua NFSe foi processada com sucesso!"
            messageSenderService.sendDocumentMessage(
                to = chat.userPhoneNumber,
                documentUrl = nfsePdfUrl,
                caption = message
            )
            
            logger.info("Mensagem enviada com sucesso para o usuário - NFSe: ${nfse.externalProcessId}, Chat: ${chat.id}")
            
        } catch (e: Exception) {
            logger.error("Erro ao enviar mensagem para usuário - NFSe UUID: $nfseUuid", e)
        }
    }
} 