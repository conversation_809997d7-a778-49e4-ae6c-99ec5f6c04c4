package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.entity.UserChat
import br.com.mecontaai.api.accounting.domain.service.ChatUserService
import br.com.mecontaai.api.accounting.infrastructure.database.model.UserChatEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.UserChatEntityRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Implementation of ChatUserService.
 */
@Service
class ChatUserServiceImpl(
    private val userChatEntityRepository: UserChatEntityRepository
) : ChatUserService {
    
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun findByChatId(chatId: Long): UserChat? {
        logger.debug("Buscando relações user-chat para chatId: $chatId")

        val entity: UserChatEntity? = userChatEntityRepository.findByChatIdAndIsActive(chatId, true)
        return entity?.let { mapToUserChat(it) }
    }

    override fun findByCompanyId(companyId: Long): UserChat? {
        logger.info("Buscando relação user-chat para companyId: $companyId")

        val entity: UserChatEntity? = userChatEntityRepository.findByCompanyIdAndIsActive(companyId, true)
        return entity?.let { mapToUserChat(it) }
    }

    /**
     * Mapeia UserChatEntity para UserChat.
     */
    private fun mapToUserChat(entity: UserChatEntity): UserChat {
        return UserChat(
            id = entity.id,
            uuid = entity.uuid,
            userId = entity.user.id!!,
            chatId = entity.chat.id!!,
            companyId = entity.company?.id,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
} 