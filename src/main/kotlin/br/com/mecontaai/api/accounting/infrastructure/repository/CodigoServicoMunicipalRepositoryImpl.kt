package br.com.mecontaai.api.accounting.infrastructure.repository

import br.com.mecontaai.api.accounting.domain.entity.CodigoServicoMunicipal
import br.com.mecontaai.api.accounting.domain.repository.CodigoServicoMunicipalRepository
import br.com.mecontaai.api.accounting.infrastructure.database.model.CodigoServicoMunicipalEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CodigoServicoMunicipalEntityRepository
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Implementação do repositório de códigos de serviço municipais.
 */
@Repository
class CodigoServicoMunicipalRepositoryImpl(
    private val codigoServicoMunicipalEntityRepository: CodigoServicoMunicipalEntityRepository
) : CodigoServicoMunicipalRepository {

    override fun save(codigoServicoMunicipal: CodigoServicoMunicipal): CodigoServicoMunicipal {
        val entity = mapToEntity(codigoServicoMunicipal)
        val savedEntity = codigoServicoMunicipalEntityRepository.save(entity)
        return mapToDomain(savedEntity)
    }

    override fun findById(id: Long): CodigoServicoMunicipal? {
        return codigoServicoMunicipalEntityRepository.findById(id)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByUuid(uuid: String): CodigoServicoMunicipal? {
        return try {
            val uuidObj = UUID.fromString(uuid)
            codigoServicoMunicipalEntityRepository.findByUuid(uuidObj)
                .map { mapToDomain(it) }
                .orElse(null)
        } catch (e: IllegalArgumentException) {
            null
        }
    }

    override fun findByCodigoAndCodigoMunicipio(codigo: String, codigoMunicipio: String): CodigoServicoMunicipal? {
        return codigoServicoMunicipalEntityRepository.findByCodigoAndCodigoMunicipio(codigo, codigoMunicipio)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByCodigoMunicipio(codigoMunicipio: String): List<CodigoServicoMunicipal> {
        return codigoServicoMunicipalEntityRepository.findByCodigoMunicipio(codigoMunicipio)
            .map { mapToDomain(it) }
    }

    override fun findAll(): List<CodigoServicoMunicipal> {
        return codigoServicoMunicipalEntityRepository.findAll()
            .map { mapToDomain(it) }
    }

    /**
     * Converte entidade de domínio para entidade JPA.
     */
    private fun mapToEntity(codigoServicoMunicipal: CodigoServicoMunicipal): CodigoServicoMunicipalEntity {
        val entity = CodigoServicoMunicipalEntity(
            codigo = codigoServicoMunicipal.codigo,
            descricao = codigoServicoMunicipal.descricao,
            codigoMunicipio = codigoServicoMunicipal.codigoMunicipio
        )
        
        entity.id = codigoServicoMunicipal.id
        entity.uuid = codigoServicoMunicipal.uuid
        entity.isActive = codigoServicoMunicipal.isActive
        entity.createdAt = codigoServicoMunicipal.createdAt
        entity.updatedAt = codigoServicoMunicipal.updatedAt
        
        return entity
    }

    /**
     * Converte entidade JPA para entidade de domínio.
     */
    private fun mapToDomain(entity: CodigoServicoMunicipalEntity): CodigoServicoMunicipal {
        return CodigoServicoMunicipal(
            id = entity.id,
            uuid = entity.uuid,
            codigo = entity.codigo,
            descricao = entity.descricao,
            codigoMunicipio = entity.codigoMunicipio,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
} 