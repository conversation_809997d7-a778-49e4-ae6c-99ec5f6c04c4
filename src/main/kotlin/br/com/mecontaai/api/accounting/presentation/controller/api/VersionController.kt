package br.com.mecontaai.api.accounting.presentation.controller.api

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("")
class VersionController {
    @GetMapping("/version")
    fun getVersion(): ResponseEntity<Map<String, String>> {
        val versionInfo = mapOf("version" to "v1.0.31")
        return ResponseEntity.ok(versionInfo)
    }

    @GetMapping
    fun getHealth(): ResponseEntity<Map<String, String>> {
        val versionInfo = mapOf("status" to "UP")
        return ResponseEntity.ok(versionInfo)
    }
}
