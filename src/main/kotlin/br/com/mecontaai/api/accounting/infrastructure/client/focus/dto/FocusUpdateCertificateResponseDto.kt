package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.OffsetDateTime

/**
 * DTO para resposta da atualização do certificado na Focus.
 * Contém apenas os campos relacionados ao certificado.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class FocusUpdateCertificateResponseDto(
    /**
     * Data de validade inicial do certificado.
     */
    @JsonProperty("certificado_valido_de")
    val certificadoValidoDe: OffsetDateTime? = null,

    /**
     * Data de validade final do certificado.
     */
    @JsonProperty("certificado_valido_ate")
    val certificadoValidoAte: OffsetDateTime? = null,

    /**
     * CNPJ do certificado.
     */
    @JsonProperty("certificado_cnpj")
    val certificadoCnpj: String? = null,

    /**
     * Indica se o certificado é específico.
     */
    @JsonProperty("certificado_especifico")
    val certificadoEspecifico: Boolean? = null,

    /**
     * Token de produção.
     */
    @JsonProperty("token_producao")
    val tokenProducao: String? = null,

    /**
     * Token de homologação.
     */
    @JsonProperty("token_homologacao")
    val tokenHomologacao: String? = null
) 