package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.dto.CreateUserDto
import br.com.mecontaai.api.accounting.application.dto.UserDto
import br.com.mecontaai.api.accounting.domain.entity.User
import br.com.mecontaai.api.accounting.domain.exception.DocumentAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.exception.EmailAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.repository.UserRepository
import br.com.mecontaai.api.accounting.domain.service.PasswordEncoderService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/**
 * Use case for creating a new user in the system.
 */
@Component
class CreateUserUseCase(
    private val userRepository: UserRepository,
    private val passwordEncoder: PasswordEncoderService
) {
    /**
     * Create a new user.
     *
     * @param dto Data for creating a user
     * @return The created user
     * @throws EmailAlreadyExistsException if the email is already in use
     * @throws DocumentAlreadyExistsException if the document is already in use
     */
    @Transactional
    fun execute(dto: CreateUserDto): UserDto {
        // Check if email is already in use
        if (userRepository.existsByEmail(dto.email)) {
            throw EmailAlreadyExistsException(dto.email)
        }

        // Check if document is already in use (if provided)
        if (dto.document != null && dto.document.isNotBlank() && userRepository.existsByDocument(dto.document)) {
            throw DocumentAlreadyExistsException(dto.document)
        }

        // Create user entity with encoded password
        val user = User(
            name = dto.name,
            email = dto.email,
            password = passwordEncoder.encode(dto.password),
            document = dto.document
        )

        // Save user
        val savedUser = userRepository.save(user)

        // Return DTO
        return UserDto(
            id = savedUser.id,
            uuid = savedUser.uuid,
            name = savedUser.name,
            email = savedUser.email,
            document = savedUser.document,
            isActive = savedUser.isActive,
            createdAt = savedUser.createdAt,
            updatedAt = savedUser.updatedAt
        )
    }
} 