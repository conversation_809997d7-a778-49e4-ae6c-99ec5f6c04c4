package br.com.mecontaai.api.accounting.presentation.mapper

import br.com.mecontaai.api.accounting.application.dto.CreateCustomerDto
import br.com.mecontaai.api.accounting.application.dto.CustomerDto
import br.com.mecontaai.api.accounting.application.dto.DocumentTypeDto
import br.com.mecontaai.api.accounting.domain.entity.Customer
import br.com.mecontaai.api.accounting.domain.enum.DocumentType
import br.com.mecontaai.api.accounting.presentation.dto.CreateCustomerRequestDto
import br.com.mecontaai.api.accounting.presentation.dto.CustomerFullResponseDto
import org.springframework.stereotype.Component

/**
 * Mapper para converter entre DTOs de apresentação e entidades de domínio de Customer.
 */
@Component
class CustomerMapper {

    /**
     * Converte um DTO de requisição para um DTO de aplicação.
     *
     * @param requestDto O DTO de requisição
     * @return O DTO de aplicação
     */
    fun toCreateCustomerDto(requestDto: CreateCustomerRequestDto): CreateCustomerDto {
        val documentType = DocumentType.fromCode(requestDto.documentType)
            ?: throw IllegalArgumentException("Tipo de documento inválido: ${requestDto.documentType}")
        
        val documentTypeDto = DocumentTypeDto.fromDocumentType(documentType)
        
        return CreateCustomerDto(
            name = requestDto.name,
            document = requestDto.document,
            documentType = documentTypeDto,
            companyId = requestDto.companyId,
            isForeigner = requestDto.isForeigner,
            email = requestDto.email,
            phone = requestDto.phone
        )
    }

    /**
     * Converte um DTO de requisição para uma entidade de domínio.
     *
     * @param requestDto O DTO de requisição
     * @return A entidade de domínio
     */
    fun toCustomerEntity(requestDto: CreateCustomerRequestDto): Customer {
        val documentType = DocumentType.fromCode(requestDto.documentType)
            ?: throw IllegalArgumentException("Tipo de documento inválido: ${requestDto.documentType}")
        
        return Customer(
            name = requestDto.name,
            document = requestDto.document,
            documentType = documentType,
            companyId = requestDto.companyId,
            isForeigner = requestDto.isForeigner,
            email = requestDto.email,
            phone = requestDto.phone,
            addresses = emptyList() // Endereços seriam tratados separadamente
        )
    }

    /**
     * Converte uma entidade de domínio para um DTO de resposta completa.
     *
     * @param customer A entidade de domínio
     * @return O DTO de resposta
     */
    fun toCustomerFullResponseDto(customer: Customer): CustomerFullResponseDto {
        val documentTypeDto = DocumentTypeDto.fromDocumentType(customer.documentType)
        
        return CustomerFullResponseDto(
            id = customer.id,
            uuid = customer.uuid,
            companyId = customer.companyId,
            name = customer.name,
            document = customer.document,
            documentType = documentTypeDto.code, // Mantendo a API com o código string
            documentTypeDescription = documentTypeDto.description, // Adicionando a descrição
            isForeigner = customer.isForeigner,
            email = customer.email,
            phone = customer.phone,
            isActive = customer.isActive,
            createdAt = customer.createdAt,
            updatedAt = customer.updatedAt
        )
    }

    /**
     * Converte um DTO de aplicação para um DTO de resposta completa.
     *
     * @param customerDto O DTO de aplicação
     * @return O DTO de resposta
     */
    fun toCustomerFullResponseDto(customerDto: CustomerDto): CustomerFullResponseDto {
        return CustomerFullResponseDto(
            id = customerDto.id,
            uuid = customerDto.uuid,
            companyId = customerDto.companyId,
            name = customerDto.name,
            document = customerDto.document,
            documentType = customerDto.documentType.code, // Mantendo a API com o código string
            documentTypeDescription = customerDto.documentType.description, // Adicionando a descrição
            isForeigner = customerDto.isForeigner,
            email = customerDto.email,
            phone = customerDto.phone,
            isActive = customerDto.isActive,
            createdAt = customerDto.createdAt,
            updatedAt = customerDto.updatedAt
        )
    }
} 