package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.Certificate
import java.util.UUID

/**
 * Repository interface for Certificate operations.
 */
interface CertificateRepository {

    /**
     * Save a certificate to the database.
     *
     * @param certificate The certificate to save
     * @return The saved certificate with generated ID
     */
    fun save(certificate: Certificate): Certificate

    /**
     * Find a certificate by ID.
     *
     * @param id The certificate ID
     * @return The certificate if found, null otherwise
     */
    fun findById(id: Long): Certificate?

    /**
     * Find a certificate by UUID.
     *
     * @param uuid The certificate UUID
     * @return The certificate if found, null otherwise
     */
    fun findByUuid(uuid: UUID): Certificate?

    /**
     * Find the active certificate for a company.
     *
     * @param companyId The company ID
     * @return The active certificate if found, null otherwise
     */
    fun findActiveByCompanyId(companyId: Long): Certificate?

    /**
     * Find all certificates for a specific company.
     *
     * @param companyId The company ID
     * @return List of certificates belonging to the company
     */
    fun findByCompanyId(companyId: Long): List<Certificate>

    /**
     * Find a certificate by blob UUID.
     *
     * @param blobUuid The blob UUID
     * @return The certificate if found, null otherwise
     */
    fun findByBlobUuid(blobUuid: String): Certificate?

    /**
     * Check if a company has an active certificate.
     *
     * @param companyId The company ID
     * @return true if the company has an active certificate, false otherwise
     */
    fun hasActiveCertificate(companyId: Long): Boolean

    /**
     * Finds all certificates.
     *
     * @return List of all certificates
     */
    fun findAll(): List<Certificate>
} 