package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO para envio de mensagens com documento via WhatsApp.
 */
data class WhatsAppDocumentMessageDto(
    @JsonProperty("messaging_product")
    val messagingProduct: String = "whatsapp",
    @JsonProperty("recipient_type")
    val recipientType: String = "individual",
    val to: String,
    val type: String = "document",
    val document: Document
) {
    data class Document(
        val id: String? = null,
        val link: String? = null,
        val caption: String? = null,
        val filename: String? = null
    ) {
        init {
            require(id != null || link != null) { "É necessário fornecer um id ou link para o documento" }
        }
    }
} 