package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * Response DTO for the email sending endpoint.
 *
 * The Focus API doesn't define a specific structure for the email
 * API response, but we should handle potential errors.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class NfseEmailResponseDto(
    /**
     * Message returned by the API
     */
    val mensagem: String? = null
) {
    companion object {
        /**
         * Creates a simple response for successful email sending
         */
        fun success(): NfseEmailResponseDto {
            return NfseEmailResponseDto("Email sent successfully")
        }
    }
}