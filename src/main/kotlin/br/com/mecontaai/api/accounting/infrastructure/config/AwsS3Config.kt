package br.com.mecontaai.api.accounting.infrastructure.config

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.S3Configuration
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import java.net.URI

/**
 * Configuração do cliente AWS S3
 */
@Configuration
class AwsS3Config(
    @Value("\${aws.region}")
    private val region: String,
    
    @Value("\${aws.access-key-id}")
    private val accessKeyId: String,
    
    @Value("\${aws.secret-access-key}")
    private val secretAccessKey: String,
    
    @Value("\${aws.s3.endpoint:#{null}}")
    private val endpoint: String?,
    
    @Value("\${aws.s3.bucket:mecontaai-blob-storage}")
    private val bucketName: String
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    @Bean
    fun s3Client(): S3Client {
        val credentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey)
        val credentialsProvider = StaticCredentialsProvider.create(credentials)
        
        val builder = S3Client.builder()
            .region(Region.of(region))
            .credentialsProvider(credentialsProvider)
        
        // Para LocalStack, precisamos definir as configurações de endpoint
        if (!endpoint.isNullOrBlank()) {
            logger.info("Configurando S3 com endpoint personalizado: $endpoint")
            builder.endpointOverride(URI.create(endpoint))
            
            // Ao usar LocalStack, precisamos habilitar estas opções
            if (endpoint.contains("localhost") || endpoint.contains("127.0.0.1")) {
                builder.forcePathStyle(true)
            }
        }
        
        return builder.build()
    }
    
    @Bean
    fun s3Presigner(): S3Presigner {
        val credentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey)
        val credentialsProvider = StaticCredentialsProvider.create(credentials)
        
        val builder = S3Presigner.builder()
            .region(Region.of(region))
            .credentialsProvider(credentialsProvider)
        
        // Para LocalStack, precisamos definir as configurações de endpoint
        if (!endpoint.isNullOrBlank()) {
            builder.endpointOverride(URI.create(endpoint))
            
            // Ao usar LocalStack, precisamos habilitar estas opções
            if (endpoint.contains("localhost") || endpoint.contains("127.0.0.1")) {
                // Configurar path style para S3Presigner
                val s3Configuration = S3Configuration.builder()
                    .pathStyleAccessEnabled(true)
                    .build()
                builder.serviceConfiguration(s3Configuration)
            }
        }
        
        return builder.build()
    }
    
    @Bean
    fun s3BucketName(): String {
        return bucketName
    }
} 