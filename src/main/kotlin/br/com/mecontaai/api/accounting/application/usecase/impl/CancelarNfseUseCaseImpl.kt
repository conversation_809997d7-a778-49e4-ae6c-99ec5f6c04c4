package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.dto.NfseDto
import br.com.mecontaai.api.accounting.application.usecase.CancelarNfseUseCase
import br.com.mecontaai.api.accounting.application.usecase.CancelarNfseRequest
import br.com.mecontaai.api.accounting.domain.entity.Nfse
import br.com.mecontaai.api.accounting.domain.repository.CompanyRepository
import br.com.mecontaai.api.accounting.domain.service.NfseService
import org.springframework.stereotype.Component

/**
 * Implementação do caso de uso para cancelar NFS-e.
 */
@Component
class CancelarNfseUseCaseImpl(
    private val nfseService: NfseService,
    private val companyRepository: CompanyRepository
) : CancelarNfseUseCase {

    /**
     * Cancela uma NFS-e autorizada.
     *
     * @param request dados para cancelamento
     * @return dados da NFS-e cancelada
     */
    override fun execute(request: CancelarNfseRequest): NfseDto {
        val company = companyRepository.findById(request.companyId)
            ?: throw IllegalArgumentException("Empresa com ID ${request.companyId} não encontrada")

        if (company.token.isNullOrBlank()) {
            throw IllegalStateException("Empresa não possui token Focus NFe cadastrado")
        }

        val nfse = nfseService.cancelNfse(request.reference, request.justificativa, company.token!!)
        
        return mapToDto(nfse)
    }
    
    private fun mapToDto(nfse: Nfse): NfseDto {
        return NfseDto(
            id = nfse.id,
            uuid = nfse.uuid.toString(),
            companyId = nfse.companyId,
            customerId = nfse.customerId,
            description = nfse.description,
            amount = nfse.amount,
            status = nfse.status,
            externalProcessId = nfse.externalProcessId,
            createdAt = nfse.createdAt,
            updatedAt = nfse.updatedAt
        )
    }
} 