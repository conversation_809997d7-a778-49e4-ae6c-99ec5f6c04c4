package br.com.mecontaai.api.accounting.presentation.dto

import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

/**
 * DTO para resposta de operações com certificado digital.
 */
data class CertificateResponseDto(
    /**
     * ID do certificado
     */
    val id: Long,
    
    /**
     * UUID do certificado
     */
    val uuid: UUID,
    
    /**
     * ID da empresa
     */
    val companyId: Long,
    
    /**
     * Data de expiração do certificado
     */
    val expirationDate: LocalDate,
    
    /**
     * Se o certificado está ativo
     */
    val isActive: Boolean,
    
    /**
     * Se o certificado está válido (não expirado)
     */
    val isValid: Boolean,
    
    /**
     * Se o certificado está próximo do vencimento
     */
    val isExpiringSoon: Boolean,
    
    /**
     * Data de criação
     */
    val createdAt: LocalDateTime,
    
    /**
     * Data de última atualização
     */
    val updatedAt: LocalDateTime
) 