package br.com.mecontaai.api.accounting.domain.service.step

import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.service.MessageManagerService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Step responsible for delivering responses back to the user.
 * This step handles agent responses using the MessageManagerService,
 * which abstracts the messaging platform (WhatsApp, Discord, etc.).
 */
@Component
class ResponseDeliveryStep(
    private val messageManagerService: MessageManagerService,
) {
    private val logger = LoggerFactory.getLogger(ResponseDeliveryStep::class.java)

    /**
     * Delivers agent responses to the user.
     * Uses MessageManagerService to handle different types of responses.
     *
     * @param responses List of agent responses to deliver
     * @param chat The chat entity to send the responses to
     * @return List of external message IDs from the messaging provider
     */
    fun execute(
        responses: List<AgentResponse>,
        chat: ChatEntity,
    ): List<String> {
        logger.info("Delivering ${responses.size} agent responses to chat: ${chat.uuid}")
        if (responses.isEmpty()) {
            logger.warn("No responses to deliver for chat: ${chat.uuid}")
            return emptyList()
        }

        return try {
            val messageIds = messageManagerService.sendAgentResponses(chat, responses)
            logger.info("Delivered ${messageIds.size}/${responses.size} responses to ${chat.userPhoneNumber}")
            messageIds
        } catch (e: Exception) {
            logger.error("Error delivering responses to chat ${chat.uuid}", e)

            // Try to send a fallback error message
            return try {
                val fallbackMessageId = sendFallbackMessage(chat)
                logger.info("Fallback message sent with ID: $fallbackMessageId")
                listOf(fallbackMessageId)
            } catch (fallbackException: Exception) {
                logger.error("Error sending fallback message to chat ${chat.uuid}", fallbackException)
                throw ResponseDeliveryException("Failed to deliver responses and fallback message", e)
            }
        }
    }

    /**
     * Sends a fallback error message when response delivery fails.
     */
    private fun sendFallbackMessage(chat: ChatEntity): String =
        messageManagerService.sendAgentResponse(
            chat = chat,
            agentResponse = AgentResponse.TextResponse(
                content = "Desculpe, ocorreu um erro ao enviar a resposta. Tente novamente."
            )
        )
}

/**
 * Exception thrown when response delivery fails.
 */
class ResponseDeliveryException(
    message: String,
    cause: Throwable? = null,
) : Exception(message, cause)
