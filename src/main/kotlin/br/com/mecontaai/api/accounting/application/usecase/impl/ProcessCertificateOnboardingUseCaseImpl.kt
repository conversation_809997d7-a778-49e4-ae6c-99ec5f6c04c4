package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.usecase.ConvertCertificateToBase64UseCase
import br.com.mecontaai.api.accounting.application.usecase.ProcessCertificateOnboardingUseCase
import br.com.mecontaai.api.accounting.application.usecase.UpdateCertificateInFocusUseCase
import br.com.mecontaai.api.accounting.application.usecase.ValidateCertificateUseCase
import br.com.mecontaai.api.accounting.domain.dto.OnboardingResultDto
import br.com.mecontaai.api.accounting.domain.entity.Certificate
import br.com.mecontaai.api.accounting.domain.enum.OnboardingStatus
import br.com.mecontaai.api.accounting.domain.service.BlobService
import br.com.mecontaai.api.accounting.domain.service.CertificateService
import br.com.mecontaai.api.accounting.domain.dto.BlobMetadata
import br.com.mecontaai.api.accounting.domain.enum.BlobEntityType
import br.com.mecontaai.api.accounting.domain.enum.ContentCategory
import br.com.mecontaai.api.accounting.domain.enum.StorageType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.time.LocalDate
import java.nio.file.Files
import java.nio.file.StandardCopyOption

/**
 * Implementação do use case para processar onboarding de certificado.
 */
@Component
class ProcessCertificateOnboardingUseCaseImpl(
    private val validateCertificateUseCase: ValidateCertificateUseCase,
    private val convertCertificateToBase64UseCase: ConvertCertificateToBase64UseCase,
    private val updateCertificateInFocusUseCase: UpdateCertificateInFocusUseCase,
    private val certificateService: CertificateService,
    private val blobService: BlobService
) : ProcessCertificateOnboardingUseCase {
    
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun execute(empresaId: Long, certificado: MultipartFile, senha: String): OnboardingResultDto {
        logger.info("Iniciando onboarding de certificado para empresa $empresaId")
        
        try {
            // 1. Validações básicas
            if (certificado.isEmpty) {
                return OnboardingResultDto.failure(
                    status = OnboardingStatus.ERRO_VALIDACAO,
                    message = "Arquivo de certificado é obrigatório"
                )
            }
            
            if (senha.isBlank()) {
                return OnboardingResultDto.failure(
                    status = OnboardingStatus.ERRO_VALIDACAO,
                    message = "Senha do certificado é obrigatória"
                )
            }
            
            // Validar tipo de arquivo
            val filename = certificado.originalFilename ?: "certificate"
            if (!filename.endsWith(".pfx") && !filename.endsWith(".p12")) {
                return OnboardingResultDto.failure(
                    status = OnboardingStatus.ERRO_VALIDACAO,
                    message = "Arquivo deve ser um certificado digital (.pfx ou .p12)"
                )
            }
            
            // Criar arquivo temporário
            val tempFile = Files.createTempFile("certificate_", "_${filename}")
            Files.copy(certificado.inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING)
            
            try {
                // 2. Validar certificado (mock por enquanto)
                logger.info("Validando certificado...")
                // val validationResult = validateCertificateUseCase.execute(certificado, senha, cnpjEmpresa)
                // if (!validationResult.isValid) {
                //     return OnboardingResultDto.failure(OnboardingStatus.ERRO_VALIDACAO, validationResult.errorMessage!!)
                // }
                
                // 3. Converter para base64
                logger.info("Convertendo certificado para base64...")
                val certificadoBase64 = convertCertificateToBase64UseCase.execute(tempFile.toFile())
                
                // 4. Criar registro no banco e salvar no BlobService
                logger.info("Criando registro do certificado...")
                val certificate = Certificate(
                    companyId = empresaId,
                    expirationDate = LocalDate.now().plusYears(1), // Mock - deveria vir da validação
                    password = senha
                )
                val certificadoBytes = tempFile.toFile().readBytes()
                val savedCertificate = certificateService.createCertificate(certificate, certificadoBytes, filename)
                
                // 5. Enviar para FocusNFE
                logger.info("Enviando certificado para FocusNFE...")
                try {
                    val focusResponse = updateCertificateInFocusUseCase.execute(empresaId, savedCertificate.blobUuid!!)
                    logger.info("Certificado enviado para FocusNFE com sucesso")
                    
                    // 6. Atualizar data de expiração com dados da Focus
                    focusResponse.certificadoValidoAte?.let { dataExpiracao ->
                        logger.info("Atualizando data de expiração do certificado com dados da Focus: $dataExpiracao")
                        val dataExpiracaoLocalDate = dataExpiracao.toLocalDate()
                        certificateService.updateExpirationDate(savedCertificate.id!!, dataExpiracaoLocalDate)
                        logger.info("Data de expiração atualizada com sucesso para: $dataExpiracaoLocalDate")
                    } ?: run {
                        logger.warn("Focus não retornou data de expiração do certificado")
                    }
                    
                } catch (e: Exception) {
                    logger.error("Erro ao enviar certificado para FocusNFE", e)
                    return OnboardingResultDto.failure(
                        status = OnboardingStatus.ERRO_FOCUS_NFE,
                        message = "Erro ao enviar certificado para FocusNFE: ${e.message}"
                    )
                }
                
                logger.info("Onboarding de certificado concluído com sucesso para empresa $empresaId")
                return OnboardingResultDto.success(
                    status = OnboardingStatus.AGUARDANDO_INSCRICAO_MUNICIPAL,
                    message = "Certificado processado com sucesso. Aguardando inscrição municipal.",
                    certificateId = savedCertificate.id!!
                )
                
            } finally {
                // Limpar arquivo temporário
                Files.deleteIfExists(tempFile)
            }
            
        } catch (e: Exception) {
            logger.error("Erro durante onboarding de certificado para empresa $empresaId", e)
            return OnboardingResultDto.failure(
                status = OnboardingStatus.ERRO_VALIDACAO,
                message = "Erro interno: ${e.message}"
            )
        }
    }
} 