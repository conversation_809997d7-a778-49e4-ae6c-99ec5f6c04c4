package br.com.mecontaai.api.accounting.application.service.callback.handlers

import br.com.mecontaai.api.accounting.application.dto.EmitirNfseRequest
import br.com.mecontaai.api.accounting.application.dto.NfseDto
import br.com.mecontaai.api.accounting.application.usecase.EmitirNfseUseCase
import br.com.mecontaai.api.accounting.domain.model.callback.ButtonAction
import br.com.mecontaai.api.accounting.domain.model.callback.NfseContext
import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.service.callback.ActionType
import br.com.mecontaai.api.accounting.domain.service.callback.ButtonCallbackHandler
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.ToolExecutionResult
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

@Component
class NfseCallbackHandler(
    private val objectMapper: ObjectMapper,
    private val emitirNfseUseCase: EmitirNfseUseCase
) : ButtonCallbackHandler {

    private val logger = LoggerFactory.getLogger(javaClass)

    override fun getActionType(): ActionType = ActionType.EMITIR_NFSE_RESPONSE

    override fun execute(action: ButtonAction, chat: ChatEntity): ToolExecutionResult {
        logger.info("🎯 Executando NfseCallbackHandler")
        logger.info("📋 Action Type: ${action.actionType}")
        logger.info("💬 Chat UUID: ${action.chatUuid}")
        logger.info("🔧 Tool Name: ${action.toolName}")
        logger.info("👆 Clicked Value: ${action.clickedValue}")
        logger.info("📦 Context Data: ${action.contextData}")

        // Recupera o contexto original
        val context = try {
            objectMapper.convertValue(action.contextData, NfseContext::class.java)
        } catch (e: Exception) {
            logger.error("Erro ao converter contexto: ${e.message}")
            // Fallback para contexto simples
            NfseContext(
                companyId = (action.contextData["companyId"] as Number).toLong(),
                customerId = (action.contextData["customerId"] as Number).toLong(),
                customerName = action.contextData["customerName"]?.toString() ?: "",
                customerDocument = action.contextData["customerDocument"]?.toString() ?: "",
                discriminacao = action.contextData["discriminacao"]?.toString() ?: "",
                valorServico = BigDecimal(action.contextData["valorServico"].toString()),
                itemListaServico = action.contextData["itemListaServico"]?.toString() ?: "0107", // Mocked por enquanto
                timestamp = action.contextData["timestamp"]?.toString() ?: "",
                chatUuid = action.chatUuid.toString(),
                userPhoneNumber = chat.userPhoneNumber
            )
        }

        logger.info("🔍 Contexto recuperado:")
        logger.info("   - Company ID: ${context.companyId}")
        logger.info("   - Customer: ${context.customerName} (ID: ${context.customerId})")
        logger.info("   - Documento: ${context.customerDocument}")
        logger.info("   - Valor: ${context.valorServico}")
        logger.info("   - Discriminação: ${context.discriminacao}")
        logger.info("   - Item Lista Serviço: ${context.itemListaServico}")
        logger.info("   - Timestamp: ${context.timestamp}")

        val responseMessage = when (action.clickedValue) {
            "confirm_emission" -> {
                logger.info("✅ Usuário confirmou a emissão da NFSe")
                buildConfirmationResponse(context)
            }
            else -> {
                logger.warn("⚠️ Valor clicado desconhecido: ${action.clickedValue}")
                "Opção não reconhecida. Por favor, tente novamente."
            }
        }

        logger.info("📤 Resposta gerada: $responseMessage")

        return ToolExecutionResult(
            agentResponses = listOf(
                AgentResponse.TextResponse(content = responseMessage)
            )
        )
    }

    private fun buildConfirmationResponse(context: NfseContext): String {
        try {
            logger.info("🚀 Iniciando emissão da NFSe para ${context.customerName}")
            
            val request = EmitirNfseRequest(
                companyId = context.companyId,
                customerId = context.customerId,
                valorServico = context.valorServico,
                discriminacao = context.discriminacao,
                itemListaServico = context.itemListaServico
            )

            val nfse = emitNfse(request, mock = false)
            logger.info("✅ NFSe emitida com sucesso: ${nfse.uuid}")

            return buildString {
                append("✅ *NFSe para ${context.customerName} emitida com sucesso!*\n\n")
                append("Em breve você receberá o PDF por aqui.")
            }
        } catch (e: Exception) {
            logger.error("❌ Erro ao emitir NFSe: ${e.message}", e)
            return buildString {
                append("❌ *Ops! Tivemos um problema ao emitir a NFSe*\n\n")
                append("Não se preocupe, nossa equipe já foi notificada e está trabalhando para resolver.\n")
                append("Em breve entraremos em contato para finalizar a emissão.")
            }
        }
    }

    private fun emitNfse(request: EmitirNfseRequest, mock: Boolean): NfseDto {
        return if (mock) {
            // Mock da resposta para testes
            val now = LocalDateTime.now()
            NfseDto(
                id = 1L, // Mock ID
                uuid = UUID.randomUUID().toString(),
                companyId = request.companyId,
                customerId = request.customerId,
                description = request.discriminacao,
                amount = request.valorServico,
                status = "PENDING",
                externalProcessId = null,
                createdAt = now,
                updatedAt = now,
                xmlBlobUuid = null,
                pdfBlobUuid = null
            )
        } else {
            // Emissão real via caso de uso
            emitirNfseUseCase.execute(request)
        }
    }

    override fun validateContext(contextData: Map<String, Any>): Boolean {
        return contextData.containsKey("companyId") &&
            contextData.containsKey("customerId") &&
            contextData.containsKey("customerName") &&
            contextData.containsKey("customerDocument") &&
            contextData.containsKey("valorServico") &&
            contextData.containsKey("discriminacao") &&
            contextData.containsKey("itemListaServico") &&
            contextData.containsKey("timestamp")
    }
} 