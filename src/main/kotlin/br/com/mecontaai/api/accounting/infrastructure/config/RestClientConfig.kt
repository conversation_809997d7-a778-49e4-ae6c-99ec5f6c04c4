package br.com.mecontaai.api.accounting.infrastructure.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate

/**
 * Configuração para os clientes REST utilizados na aplicação.
 */
@Configuration
class RestClientConfig {
    
    /**
     * Bean para o RestTemplate utilizado em chamadas HTTP.
     * 
     * @return Uma instância de RestTemplate
     */
    @Bean
    fun restTemplate(): RestTemplate {
        return RestTemplate()
    }
} 