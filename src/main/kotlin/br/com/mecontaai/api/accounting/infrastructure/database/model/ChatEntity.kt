package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*
import java.time.Duration
import java.time.LocalDateTime

/**
 * Entity class representing a chat in the database.
 */
@Entity
@Table(name = "chats")
class ChatEntity(
    @Column(name = "user_phone_number", nullable = false)
    val userPhoneNumber: String,

    @Column(name = "conversational_window_started_at")
    var conversationalWindowStartedAt: LocalDateTime? = null,

    @Column(name = "meta_phone_number_id")
    val metaPhoneNumberId: String? = null
) : BaseEntity() {

    /**
     * Verifica se a janela conversacional está aberta (dentro das últimas 24 horas).
     *
     * @return true se a janela estiver aberta, false caso contrário
     */
    fun isConversationalWindowOpen(): Boolean {
        if (conversationalWindowStartedAt == null) {
            return false
        }

        val now = LocalDateTime.now()
        val hours = Duration.between(conversationalWindowStartedAt, now).toHours()
        return hours <= 24
    }

    /**
     * Abre a janela conversacional definindo o momento atual como início.
     */
    fun openConversationWindow() {
        this.conversationalWindowStartedAt = LocalDateTime.now()
    }

    /**
     * Fecha a janela conversacional removendo o momento de início.
     */
    fun closeConversationWindow() {
        this.conversationalWindowStartedAt = null
    }
} 