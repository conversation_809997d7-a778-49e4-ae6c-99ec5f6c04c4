package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.dto.NfseDto
import br.com.mecontaai.api.accounting.application.usecase.ObterStatusNfseUseCase
import br.com.mecontaai.api.accounting.domain.entity.Nfse
import br.com.mecontaai.api.accounting.domain.service.NfseService
import org.springframework.stereotype.Component

/**
 * Implementação do caso de uso para obter status de NFS-e.
 */
@Component
class ObterStatusNfseUseCaseImpl(
    private val nfseService: NfseService
) : ObterStatusNfseUseCase {

    /**
     * Obtém o status atual de uma NFS-e.
     *
     * @param reference referência única da NFS-e
     * @return dados atualizados da NFS-e
     */
    override fun execute(reference: String): NfseDto {
        val nfse = nfseService.getNfseStatus(reference)
        
        return mapToDto(nfse)
    }
    
    private fun mapToDto(nfse: Nfse): NfseDto {
        return NfseDto(
            id = nfse.id,
            uuid = nfse.uuid.toString(),
            companyId = nfse.companyId,
            customerId = nfse.customerId,
            description = nfse.description,
            amount = nfse.amount,
            status = nfse.status,
            externalProcessId = nfse.externalProcessId,
            createdAt = nfse.createdAt,
            updatedAt = nfse.updatedAt,
            xmlBlobUuid = nfse.xmlBlobUuid?.toString(),
            pdfBlobUuid = nfse.pdfBlobUuid?.toString()
        )
    }
} 