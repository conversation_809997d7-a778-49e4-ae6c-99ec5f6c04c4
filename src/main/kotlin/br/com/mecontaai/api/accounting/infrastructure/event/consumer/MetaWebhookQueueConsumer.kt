package br.com.mecontaai.api.accounting.infrastructure.event.consumer

import br.com.mecontaai.api.accounting.domain.service.MetaWebhookProcessorFactory
import br.com.mecontaai.api.accounting.infrastructure.event.SqsQueues
import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload
import com.fasterxml.jackson.databind.ObjectMapper
import io.awspring.cloud.sqs.annotation.SqsListener
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class MetaWebhookQueueConsumer(
    private val objectMapper: ObjectMapper,
    private val metaWebhookProcessorFactory: MetaWebhookProcessorFactory,
) {
    private val logger = LoggerFactory.getLogger(MetaWebhookQueueConsumer::class.java)

    @SqsListener(SqsQueues.WEBHOOK_META_NEW_MESSAGE)
    fun handleMessage(message: String) {
        try {
            val payload = objectMapper.readValue(message, MetaWebhookPayload::class.java)
            logger.info("Processing Meta webhook from queue: ${payload.getEntryId()}")

            // Find the appropriate processor for this webhook type
            val metaWebhookProcessor = metaWebhookProcessorFactory.getProcessor(payload)
            if (metaWebhookProcessor != null) {
                metaWebhookProcessor.process(payload)
            } else {
                logger.warn("No processor found for webhook payload: {}", payload)
            }
        } catch (e: Exception) {
            logger.error("Error processing Meta webhook: ${e.message}", e)
            // throw e // This will cause SQS to retry the message
        }
    }
}
