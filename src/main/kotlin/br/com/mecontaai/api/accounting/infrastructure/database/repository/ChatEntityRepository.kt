package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

/**
 * JPA repository for ChatEntity.
 */
@Repository
interface ChatEntityRepository : JpaRepository<ChatEntity, Long> {
    /**
     * Find a chat by UUID.
     *
     * @param uuid The chat UUID
     * @return The chat if found, wrapped in Optional
     */
    fun findByUuid(uuid: UUID): Optional<ChatEntity>

    /**
     * Find a chat by user phone number.
     *
     * @param userPhoneNumber The user phone number
     * @return The chat if found, wrapped in Optional
     */
    fun findByUserPhoneNumber(userPhoneNumber: String): Optional<ChatEntity>

    /**
     * Check if a chat with the given user phone number exists.
     *
     * @param userPhoneNumber The user phone number to check
     * @return true if a chat with the phone number exists, false otherwise
     */
    fun existsByUserPhoneNumber(userPhoneNumber: String): Bo<PERSON><PERSON>
} 