package br.com.mecontaai.api.accounting.domain.model.response

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(
    use = JsonTypeInfo.Id.DEDUCTION
)
@JsonSubTypes(
    JsonSubTypes.Type(value = AgentResponse.TextResponse::class),
    JsonSubTypes.Type(value = AgentResponse.ButtonResponse::class),
    JsonSubTypes.Type(value = AgentResponse.MediaResponse::class),
    JsonSubTypes.Type(value = AgentResponse.FlowResponse::class),
    JsonSubTypes.Type(value = AgentResponse.ListResponse::class),
    JsonSubTypes.Type(value = AgentResponse.CompositeResponse::class),
    JsonSubTypes.Type(value = AgentResponse.TemplateResponse::class)
)
sealed class AgentResponse {
    /**
     * Simple text response.
     */
    data class TextResponse(
        val content: String,
        var manual: Boolean = true,
    ) : AgentResponse()

    /**
     * Interactive response with buttons.
     */
    data class ButtonResponse(
        val message: String,
        val buttons: List<AgentButton>,
    ) : AgentResponse()

    /**
     * Media response (image, document, audio, video).
     */
    data class MediaResponse(
        val mediaType: MediaType,
        val url: String,
        val caption: String? = null,
        val filename: String? = null,
    ) : AgentResponse()

    /**
     * Flow response for complex interactions.
     */
    data class FlowResponse(
        val flowId: String,
        val flowData: Map<String, Any> = emptyMap(),
        val triggerText: String? = null,
    ) : AgentResponse()

    /**
     * List response for structured data.
     */
    data class ListResponse(
        val title: String,
        val items: List<ListItem>,
        val buttonText: String? = null,
    ) : AgentResponse()

    /**
     * Composite response containing multiple responses.
     */
    data class CompositeResponse(
        val responses: List<AgentResponse>,
    ) : AgentResponse()

    /**
     * Template response for reusable message templates.
     */
    data class TemplateResponse(
        val templateName: String,
        val parameters: Map<String, String> = emptyMap(),
        val languageCode: String = "pt_BR",
    ) : AgentResponse()
}

/**
 * Generic button that can be adapted to any platform.
 */
data class AgentButton(
    val id: String,
    val title: String,
    val type: ButtonType = ButtonType.REPLY,
    val payload: String? = null,
    val url: String? = null,
)

/**
 * List item for structured lists.
 */
data class ListItem(
    val id: String,
    val title: String,
    val description: String? = null,
    val imageUrl: String? = null,
)

/**
 * Text formatting options.
 */
data class TextFormatting(
    val bold: Boolean = false,
    val italic: Boolean = false,
    val monospace: Boolean = false,
)

/**
 * Types of buttons supported.
 */
enum class ButtonType {
    REPLY, // Quick reply button
    URL, // URL button
    CALL, // Phone call button
    COPY, // Copy to clipboard button
}

/**
 * Types of media supported.
 */
enum class MediaType {
    IMAGE,
    DOCUMENT,
    AUDIO,
    VIDEO,
    STICKER,
}
