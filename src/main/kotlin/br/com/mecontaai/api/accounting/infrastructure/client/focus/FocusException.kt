package br.com.mecontaai.api.accounting.infrastructure.client.focus

import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusErrorDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusErrorDetailDto

/**
 * Exception thrown for errors related to the Focus NFe API.
 *
 * This exception includes detailed error information returned by the API,
 * including error codes, messages, and potential corrective actions.
 */
class FocusException : RuntimeException {
    val httpStatus: Int
    val errorCode: String?
    val errorMessage: String?
    val errorDetails: List<FocusErrorDetailDto>?
    val originalJson: String?

    /**
     * Creates an exception with HTTP status and raw error message
     */
    constructor(httpStatus: Int, message: String, originalJson: String? = null) : super(message) {
        this.httpStatus = httpStatus
        this.errorCode = null
        this.errorMessage = message
        this.errorDetails = null
        this.originalJson = originalJson
    }

    /**
     * Creates an exception with HTTP status and structured error information
     */
    constructor(httpStatus: Int, error: FocusErrorDto, originalJson: String? = null) : super(error.mensagem ?: "Unknown Focus API error") {
        this.httpStatus = httpStatus
        this.errorCode = error.codigo
        this.errorMessage = error.mensagem
        this.errorDetails = error.erros
        this.originalJson = originalJson
    }

    /**
     * Creates an exception for network or client configuration issues
     */
    constructor(message: String, cause: Throwable) : super(message, cause) {
        this.httpStatus = 0
        this.errorCode = null
        this.errorMessage = message
        this.errorDetails = null
        this.originalJson = null
    }

    override val message: String
        get() {
            val baseMessage = super.message ?: "Focus API error"
            val detailsMessage = errorDetails?.joinToString(
                prefix = "\nDetails: ",
                separator = ", "
            ) { "${it.codigo ?: ""}: ${it.mensagem ?: ""}" } ?: ""
            
            return "Status: $httpStatus, Code: $errorCode, $baseMessage$detailsMessage"
        }
}