package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * Response DTO for NFSe submission.
 *
 * This response is returned when a new NFSe has been successfully
 * submitted for processing, but the processing itself is asynchronous.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class NfseEnvioResponseDto(
    /**
     * CNPJ of the service provider
     */
    val cnpj_prestador: String,
    
    /**
     * Reference identifier provided in the request
     */
    val ref: String,
    
    /**
     * Current status of the NFSe
     * Expected value at this stage: "processando_autorizacao"
     */
    val status: String
)