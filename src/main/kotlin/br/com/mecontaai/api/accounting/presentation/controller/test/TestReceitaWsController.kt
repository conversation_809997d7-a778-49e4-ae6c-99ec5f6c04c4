package br.com.mecontaai.api.accounting.presentation.controller.test

import br.com.mecontaai.api.accounting.application.service.ReceitaWsService
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * Controller para consulta de CNPJs na Receita WS.
 */
@RestController
@RequestMapping("/api/v1/receitaws")
class TestReceitaWsController(
    private val receitaWsService: ReceitaWsService,
) {
    /**
     * Consulta dados de um CNPJ na Receita WS.
     *
     * @param cnpj CNPJ a ser consultado (com ou sem formatação)
     * @return Dados do CNPJ consultado
     */
    @GetMapping("/cnpj/{cnpj}")
    fun consultarCnpj(
        @PathVariable cnpj: String,
    ): ResponseEntity<ReceitaWsResponseDto> = ResponseEntity.ok(receitaWsService.consultarCnpj(cnpj))
}
