package br.com.mecontaai.api.accounting

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.context.annotation.ComponentScan
import java.util.TimeZone

@SpringBootApplication
@ComponentScan(basePackages = ["br.com.mecontaai.api"])
class ApiApplication

fun main(args: Array<String>) {
	// Configura timezone para São Paulo
	TimeZone.setDefault(TimeZone.getTimeZone("America/Sao_Paulo"))
	
	runApplication<ApiApplication>(*args)
}
