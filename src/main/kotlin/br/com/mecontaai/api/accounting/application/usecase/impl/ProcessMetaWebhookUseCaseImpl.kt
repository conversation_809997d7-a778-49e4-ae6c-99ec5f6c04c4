package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.usecase.ProcessMetaWebhookUseCase
import br.com.mecontaai.api.accounting.infrastructure.event.SqsQueues
import br.com.mecontaai.api.accounting.infrastructure.service.SqsService
import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ProcessMetaWebhookUseCaseImpl(
    private val sqsService: SqsService,
) : ProcessMetaWebhookUseCase {
    companion object {
        private val logger = LoggerFactory.getLogger(ProcessMetaWebhookUseCaseImpl::class.java)
    }

    override fun execute(payload: MetaWebhookPayload) {
        logger.info("Processing Meta webhook for entry: {}", payload.getEntryId())

        // Send all Meta webhooks to the single queue
        sqsService.sendToQueue(
            payload = payload,
            queueName = SqsQueues.WEBHOOK_META_NEW_MESSAGE,
        )
    }
}
