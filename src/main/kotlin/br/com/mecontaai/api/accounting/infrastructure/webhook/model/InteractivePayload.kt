package br.com.mecontaai.api.accounting.infrastructure.webhook.model

import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload

/**
 * Representa um payload de interação unificado extraído do webhook do Meta.
 */
data class InteractivePayload(
    val type: String,                           // Tipo da interação: "button_callback", "quick_reply", etc.
    val payload: String,                        // Dados específicos do tipo
    val metadata: Map<String, Any> = emptyMap() // Metadados adicionais
) {
    companion object {
        /**
         * Extrai um InteractivePayload de uma mensagem do webhook do Meta.
         */
        fun fromMetaWebhook(message: MetaWebhookPayload.Message): InteractivePayload {
            return when {
                // Botão interativo clicado
                message.interactive?.buttonReply != null -> {
                    val buttonId = message.interactive.buttonReply.id ?: ""
                    val externalMessageId = message.context?.id ?: ""

                    // Se temos context.id, é um callback baseado em external message ID
                    if (externalMessageId.isNotEmpty()) {
                        // O buttonId agora é o payload completo (callbackId:clickedValue)
                        // Extraímos o clickedValue (parte após o ":")
                        val clickedValue = if (buttonId.contains(":")) {
                            buttonId.split(":", limit = 2)[1]
                        } else {
                            buttonId
                        }

                        InteractivePayload(
                            type = "button_callback_external",
                            payload = "$externalMessageId:$clickedValue",
                            metadata = mapOf(
                                "button_text" to (message.interactive.buttonReply.title ?: ""),
                                "button_id" to buttonId,
                                "external_message_id" to externalMessageId,
                                "clicked_value" to clickedValue,
                                "from" to (message.from ?: ""),
                                "timestamp" to (message.timestamp ?: "")
                            )
                        )
                    } else if (buttonId.contains(":")) {
                        // Fallback: Se contém ":", é um callback (formato: callbackId:clickedValue)
                        InteractivePayload(
                            type = "button_callback",
                            payload = buttonId,
                            metadata = mapOf(
                                "button_text" to (message.interactive.buttonReply.title ?: ""),
                                "from" to (message.from ?: ""),
                                "timestamp" to (message.timestamp ?: "")
                            )
                        )
                    } else {
                        // Botão simples (quick reply)
                        InteractivePayload(
                            type = "quick_reply",
                            payload = buttonId,
                            metadata = mapOf(
                                "button_text" to (message.interactive.buttonReply.title ?: ""),
                                "from" to (message.from ?: ""),
                                "timestamp" to (message.timestamp ?: "")
                            )
                        )
                    }
                }
                
                // NFM Reply (flows)
                message.interactive?.nfmReply != null -> {
                    InteractivePayload(
                        type = "nfm_reply",
                        payload = message.interactive.nfmReply.responseJson ?: "",
                        metadata = mapOf(
                            "nfm_body" to (message.interactive.nfmReply.body ?: ""),
                            "nfm_name" to (message.interactive.nfmReply.name ?: ""),
                            "from" to (message.from ?: ""),
                            "timestamp" to (message.timestamp ?: "")
                        )
                    )
                }
                
                // Tipo não suportado
                else -> throw IllegalArgumentException("Tipo de interação não suportado: ${message.interactive}")
            }
        }
    }
}
