package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.usecase.AddUserToChatUseCase
import br.com.mecontaai.api.accounting.domain.entity.UserChat
import br.com.mecontaai.api.accounting.domain.repository.UserChatRepository
import br.com.mecontaai.api.accounting.domain.repository.UserRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * Use case implementation for adding a user to a chat.
 */
@Component
class AddUserToChatUseCaseImpl(
    private val userChatRepository: UserChatRepository,
    private val userRepository: UserRepository
) : AddUserToChatUseCase {

    /**
     * Adiciona um usuário a um chat específico.
     * Se já existir uma relação ativa entre o usuário e o chat, retorna o UUID existente.
     * 
     * @param userId ID do usuário
     * @param chatId ID do chat
     * @return UUID da relação criada ou existente
     */
    @Transactional
    override fun execute(userId: Long, chatId: Long): UUID {
        // Verificar se usuário existe
        val user = userRepository.findById(userId)
            ?: throw IllegalArgumentException("User not found with id: $userId")
        
        // Verificar se já existe uma relação ativa
        val existingUserChat = userChatRepository.findActiveByUserIdAndChatId(userId, chatId)
        if (existingUserChat != null) {
            return existingUserChat.uuid
        }
        
        // Criar nova relação
        val userChat = UserChat(
            userId = userId,
            chatId = chatId,
            companyId = null, // Inicialmente sem empresa
            isActive = true
        )
        
        val savedUserChat = userChatRepository.save(userChat)
        return savedUserChat.uuid
    }
} 