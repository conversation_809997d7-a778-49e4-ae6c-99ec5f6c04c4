package br.com.mecontaai.api.accounting.infrastructure.logging

import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.Logger
import org.springframework.stereotype.Component

/**
 * Utility component for JSON logging.
 * Provides methods to safely serialize objects to JSON for logging purposes.
 */
@Component
class JsonLogger(
    private val objectMapper: ObjectMapper,
) {
    /**
     * Converts an object to JSON string safely.
     * If serialization fails, returns a fallback string representation.
     */
    fun toJson(obj: Any?): String {
        if (obj == null) return "null"

        return try {
            objectMapper.writeValueAsString(obj)
        } catch (e: Exception) {
            "Failed to serialize to JSON: $obj"
        }
    }

    /**
     * Converts an object to pretty-printed JSON string safely.
     * If serialization fails, returns a fallback string representation.
     */
    fun toPrettyJson(obj: Any?): String {
        if (obj == null) return "null"

        return try {
            objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj)
        } catch (e: Exception) {
            "Failed to serialize to JSON: $obj"
        }
    }
}

/**
 * Logs an info message with an object serialized as JSON.
 */
fun Logger.infoJson(
    message: String,
    obj: Any?,
    jsonLogger: JsonLogger,
) {
    if (this.isInfoEnabled) {
        this.info("$message ${jsonLogger.toJson(obj)}")
    }
}

/**
 * Logs a debug message with an object serialized as JSON.
 */
fun Logger.debugJson(
    message: String,
    obj: Any?,
    jsonLogger: JsonLogger,
) {
    if (this.isDebugEnabled) {
        this.debug("$message ${jsonLogger.toJson(obj)}")
    }
}

/**
 * Logs a warn message with an object serialized as JSON.
 */
fun Logger.warnJson(
    message: String,
    obj: Any?,
    jsonLogger: JsonLogger,
) {
    if (this.isWarnEnabled) {
        this.warn("$message ${jsonLogger.toJson(obj)}")
    }
}

/**
 * Logs an error message with an object serialized as JSON.
 */
fun Logger.errorJson(
    message: String,
    obj: Any?,
    jsonLogger: JsonLogger,
    throwable: Throwable? = null,
) {
    if (this.isErrorEnabled) {
        if (throwable != null) {
            this.error("$message ${jsonLogger.toJson(obj)}", throwable)
        } else {
            this.error("$message ${jsonLogger.toJson(obj)}")
        }
    }
}

/**
 * Logs a trace message with an object serialized as JSON.
 */
fun Logger.traceJson(
    message: String,
    obj: Any?,
    jsonLogger: JsonLogger,
) {
    if (this.isTraceEnabled) {
        this.trace("$message ${jsonLogger.toJson(obj)}")
    }
}

/**
 * Logs an info message with an object serialized as pretty-printed JSON.
 */
fun Logger.infoPrettyJson(
    message: String,
    obj: Any?,
    jsonLogger: JsonLogger,
) {
    if (this.isInfoEnabled) {
        this.info("$message\n${jsonLogger.toPrettyJson(obj)}")
    }
}

/**
 * Logs a debug message with an object serialized as pretty-printed JSON.
 */
fun Logger.debugPrettyJson(
    message: String,
    obj: Any?,
    jsonLogger: JsonLogger,
) {
    if (this.isDebugEnabled) {
        this.debug("$message\n${jsonLogger.toPrettyJson(obj)}")
    }
}
