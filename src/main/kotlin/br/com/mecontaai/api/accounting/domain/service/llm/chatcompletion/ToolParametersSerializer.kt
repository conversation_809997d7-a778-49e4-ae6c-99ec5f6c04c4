package br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import java.lang.reflect.Field
import java.util.ArrayList

/**
 * Serializador para os parâmetros de ferramentas.
 * Converte uma classe de parâmetros em uma representação JSON para uso com a API OpenAI.
 */
class ToolParametersSerializer : StdSerializer<Class<*>>(Class::class.java) {
    private val objectMapper = ObjectMapper()

    override fun serialize(value: Class<*>, gen: JsonGenerator, provider: SerializerProvider) {
        gen.writeStartObject()
        gen.writeStringField("type", "object")
        
        // Propriedades requeridas
        val requiredFields = mutableListOf<String>()
        
        // Escrever propriedades
        gen.writeObjectFieldStart("properties")
        
        for (field in value.declaredFields) {
            // Verifica se o campo deve ser ignorado
            if (!shouldIgnoreField(field)) {
                serializeField(field, gen)
                
                // Se o campo não tem um valor padrão, considera-o como requerido
                if (!hasDefaultValue(field)) {
                    requiredFields.add(field.name)
                }
            }
        }
        
        gen.writeEndObject()
        
        // Escrever campos requeridos
        if (requiredFields.isNotEmpty()) {
            gen.writeArrayFieldStart("required")
            for (fieldName in requiredFields) {
                gen.writeString(fieldName)
            }
            gen.writeEndArray()
        }
        
        gen.writeEndObject()
    }
    
    private fun shouldIgnoreField(field: Field): Boolean {
        // Verifica se o campo tem alguma anotação com nome simples "JsonIgnore"
        return field.annotations.any { annotation ->
            annotation.annotationClass.simpleName == "JsonIgnore"
        }
    }
    
    private fun serializeField(field: Field, gen: JsonGenerator) {
        gen.writeObjectFieldStart(field.name)
        
        // Determinar o tipo
        when (field.type) {
            String::class.java -> gen.writeStringField("type", "string")
            Int::class.java, java.lang.Integer::class.java -> gen.writeStringField("type", "integer")
            Long::class.java, java.lang.Long::class.java -> gen.writeStringField("type", "integer")
            Double::class.java, java.lang.Double::class.java -> gen.writeStringField("type", "number")
            Float::class.java, java.lang.Float::class.java -> gen.writeStringField("type", "number")
            Boolean::class.java, java.lang.Boolean::class.java -> gen.writeStringField("type", "boolean")
            List::class.java, ArrayList::class.java -> {
                gen.writeStringField("type", "array")
                
                // Tentar determinar o tipo dos itens
                val genericType = field.genericType.toString()
                when {
                    genericType.contains("String") -> {
                        gen.writeObjectFieldStart("items")
                        gen.writeStringField("type", "string")
                        gen.writeEndObject()
                    }
                    genericType.contains("Integer") || genericType.contains("Long") -> {
                        gen.writeObjectFieldStart("items")
                        gen.writeStringField("type", "integer")
                        gen.writeEndObject()
                    }
                    genericType.contains("Double") || genericType.contains("Float") -> {
                        gen.writeObjectFieldStart("items")
                        gen.writeStringField("type", "number")
                        gen.writeEndObject()
                    }
                    genericType.contains("Boolean") -> {
                        gen.writeObjectFieldStart("items")
                        gen.writeStringField("type", "boolean")
                        gen.writeEndObject()
                    }
                    else -> {
                        gen.writeObjectFieldStart("items")
                        gen.writeStringField("type", "object")
                        gen.writeEndObject()
                    }
                }
            }
            JsonNode::class.java -> gen.writeStringField("type", "object")
            else -> {
                if (field.type.isEnum) {
                    gen.writeStringField("type", "string")
                    gen.writeArrayFieldStart("enum")
                    for (enumConstant in field.type.enumConstants) {
                        gen.writeString(enumConstant.toString())
                    }
                    gen.writeEndArray()
                } else {
                    gen.writeStringField("type", "object")
                }
            }
        }
        
        // Adicionar descrição se disponível (usando anotação de descrição comum)
        field.annotations.forEach { annotation ->
            if (annotation.annotationClass.simpleName == "Description") {
                try {
                    val value = annotation.annotationClass.java.getMethod("value").invoke(annotation) as String
                    gen.writeStringField("description", value)
                } catch (e: Exception) {
                    // Ignorar silenciosamente
                }
            }
        }
        
        gen.writeEndObject()
    }
    
    private fun hasDefaultValue(field: Field): Boolean {
        // Verifica se existe a anotação JsonProperty e se o campo não é obrigatório
        field.annotations.forEach { annotation ->
            if (annotation.annotationClass.simpleName == "JsonProperty") {
                try {
                    val required = annotation.annotationClass.java.getMethod("required").invoke(annotation) as Boolean
                    return !required
                } catch (e: Exception) {
                    // Ignorar silenciosamente
                }
            }
        }
        return false
    }
} 