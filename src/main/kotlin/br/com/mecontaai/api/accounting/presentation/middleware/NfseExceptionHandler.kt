package br.com.mecontaai.api.accounting.presentation.middleware

import br.com.mecontaai.api.accounting.domain.exception.NfseException
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusException
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice

/**
 * Exception handler for NFSe-related exceptions.
 */
@RestControllerAdvice
class NfseExceptionHandler {

    /**
     * Handles domain NFSe exceptions.
     *
     * @param ex The exception
     * @return Error response with appropriate status
     */
    @ExceptionHandler(NfseException::class)
    fun handleNfseException(ex: NfseException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            status = HttpStatus.BAD_REQUEST.value(),
            error = "NFSe Error",
            message = ex.message ?: "An error occurred with the NFSe"
        )
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse)
    }

    /**
     * Handles Focus API exceptions.
     *
     * @param ex The exception
     * @return Error response with appropriate status
     */
    @ExceptionHandler(FocusException::class)
    fun handleFocusException(ex: FocusException): ResponseEntity<ErrorResponse> {
        val httpStatus = when (ex.httpStatus) {
            400 -> HttpStatus.BAD_REQUEST
            401, 403 -> HttpStatus.FORBIDDEN
            404 -> HttpStatus.NOT_FOUND
            422 -> HttpStatus.UNPROCESSABLE_ENTITY
            429 -> HttpStatus.TOO_MANY_REQUESTS
            in 500..599 -> HttpStatus.INTERNAL_SERVER_ERROR
            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }
        
        val errorResponse = ErrorResponse(
            status = httpStatus.value(),
            error = "Focus API Error: ${ex.errorCode ?: httpStatus.reasonPhrase}",
            message = ex.message
        )
        
        return ResponseEntity.status(httpStatus).body(errorResponse)
    }

    /**
     * Simple error response DTO.
     */
    data class ErrorResponse(
        val status: Int,
        val error: String,
        val message: String
    )
}