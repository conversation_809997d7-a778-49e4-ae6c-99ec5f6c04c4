package br.com.mecontaai.api.accounting.infrastructure.mapper

import br.com.mecontaai.api.accounting.domain.entity.Address
import br.com.mecontaai.api.accounting.infrastructure.database.model.AddressEntity
import org.springframework.stereotype.Component

/**
 * Mapper para converter entre a entidade de domínio e a entidade JPA de endereço.
 */
@Component
class AddressMapper {

    /**
     * Converte uma entidade JPA para uma entidade de domínio.
     *
     * @param entity Entidade JPA
     * @return Entidade de domínio
     */
    fun toDomain(entity: AddressEntity): Address {
        return Address(
            id = entity.id,
            uuid = entity.uuid,
            entityType = entity.entityType,
            entityId = entity.entityId,
            street = entity.street,
            number = entity.number,
            complement = entity.complement,
            neighborhood = entity.neighborhood,
            city = entity.city,
            state = entity.state,
            zipCode = entity.zipCode,
            isPrimary = entity.isPrimary,
            addressType = entity.addressType ?: throw IllegalStateException("Address type cannot be null"),
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    /**
     * Converte uma entidade de domínio para uma entidade JPA.
     *
     * @param domain Entidade de domínio
     * @return Entidade JPA
     */
    fun toEntity(domain: Address): AddressEntity {
        // Criar a entidade com o construtor
        val entity = AddressEntity(
            entityType = domain.entityType,
            entityId = domain.entityId,
            street = domain.street,
            number = domain.number,
            complement = domain.complement,
            neighborhood = domain.neighborhood,
            city = domain.city,
            state = domain.state,
            zipCode = domain.zipCode,
            isPrimary = domain.isPrimary,
            addressType = domain.addressType
        )
        
        // Atribuir valores de BaseEntity
        entity.id = domain.id
        entity.uuid = domain.uuid
        entity.isActive = domain.isActive
        entity.createdAt = domain.createdAt
        entity.updatedAt = domain.updatedAt
        
        return entity
    }
} 