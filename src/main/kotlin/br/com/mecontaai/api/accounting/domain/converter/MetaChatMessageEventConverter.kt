package br.com.mecontaai.api.accounting.domain.converter

import br.com.mecontaai.api.accounting.domain.model.ChatMessageEvent
import br.com.mecontaai.api.accounting.domain.model.ChatType
import br.com.mecontaai.api.accounting.domain.model.MediaType
import br.com.mecontaai.api.accounting.domain.model.MessageSourceEnum
import br.com.mecontaai.api.accounting.domain.service.ChatService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.stereotype.Component
import java.io.IOException

@Component
class MetaChatMessageEventConverter(
    private val chatService: ChatService
) {
    private val logger = LoggerFactory.getLogger(MetaChatMessageEventConverter::class.java)

    companion object {
        private const val META_MESSAGE_TEXT_TYPE = "text"
        private const val META_MESSAGE_AUDIO_TYPE = "audio"
        private const val META_MESSAGE_IMAGE_TYPE = "image"
        private const val META_MESSAGE_DOCUMENT_TYPE = "document"
        private val IMAGE_MIME_TYPES = setOf(
            "image/jpeg",
            "image/png",
            "image/webp"
        )
    }

    fun convert(payload: MetaWebhookPayload): ChatMessageEvent? {
        try {
            // meta isn't able to work with groups, but will in the near future
            val isGroupMessage = false
            val metaPhoneNumberId = resolveMetaPhoneNumberId(payload)
            val chatType = resolveChatType(metaPhoneNumberId)
            val phoneNumber = getChatId(chatType, payload)

            // Busca ou cria chat no banco de dados
            val chat = getChatFromPhoneNumber(phoneNumber, metaPhoneNumberId)
            val chatUuid = chat.uuid
            val chatId = chat.id

            MDC.put("chatUuid", chatUuid.toString())
            MDC.put("phoneNumber", phoneNumber)

            return ChatMessageEvent(
                chatUuid = chatUuid,
                chatId = chatId,
                source = MessageSourceEnum.META,
                externalMessageId = getFirstMessageId(payload),
                timestamp = getFirstMessageTimestamp(payload),
                receivedTime = System.currentTimeMillis(),
                isMessageToMe = true,
                isMessageFromMe = false,
                isFromBot = false,
                message = resolveMessage(payload),
                messageAuthorName = resolveMessageAuthorName(payload),
                messageAuthorPhoneNumber = phoneNumber,
                isGroupMessage = isGroupMessage,
                isForwardedMessage = resolveIsForwardedMessage(payload),
                media = resolveMedia(payload),
                referral = resolveReferral(payload),
                metaPhoneNumberId = metaPhoneNumberId
            )
        } catch (e: Exception) {
            logger.error("Erro ao converter evento do Meta: ${e.message}", e)
            return null
        }
    }

    private fun resolveChatType(metaPhoneNumberId: String): ChatType {
        // TODO: Implementar a lógica para determinar o tipo de chat baseado no metaPhoneNumberId
        // Por enquanto, retornamos o padrão PF
        return ChatType.PF
    }

    private fun getChatId(chatType: ChatType, payload: MetaWebhookPayload): String {
        val eventChatId = resolveChatId(payload)
        val formattedEventChatId = insertNineInBrazilianPhoneNumberIfNeeded(eventChatId)
        
        if (eventChatId != formattedEventChatId) {
            try {
                // TODO: Implementar o serviço de chat para atualizar o ID
                logger.info("Atualizando chatId de $eventChatId para $formattedEventChatId")
            } catch (e: Exception) {
                logger.error("Erro ao atualizar chatId de $eventChatId para $formattedEventChatId. Usaremos o chatId antigo", e)
                return eventChatId
            }
        }

        return formattedEventChatId
    }

    private fun insertNineInBrazilianPhoneNumberIfNeeded(chatId: String): String {
        if (chatId.startsWith("+55") && chatId.length == 13 && isMobilePhoneNumber(chatId)) {
            return chatId.substring(0, 5) + "9" + chatId.substring(5)
        }
        return chatId
    }

    /**
     * By https://www.teleco.com.br/num.asp numbers with 6, 7, 8 and 9 are mobile phone numbers.
     */
    private fun isMobilePhoneNumber(chatId: String): Boolean {
        return chatId[5] == '6' || chatId[5] == '7' || chatId[5] == '8' || chatId[5] == '9'
    }

    private fun resolveIsForwardedMessage(payload: MetaWebhookPayload): Boolean {
        val message = getFirstMessage(payload) ?: return false
        return message.context?.forwarded ?: false
    }

    private fun resolveMetaPhoneNumberId(payload: MetaWebhookPayload): String {
        return payload.entry.firstOrNull()?.changes?.firstOrNull()?.value?.metadata?.phoneNumberId
            ?: throw IllegalArgumentException("Não foi possível obter o metaPhoneNumberId")
    }

    private fun resolveChatId(payload: MetaWebhookPayload): String {
        val waId = payload.entry.firstOrNull()?.changes?.firstOrNull()?.value?.contacts?.firstOrNull()?.waId
            ?: throw IllegalArgumentException("Provavelmente é a Magie")
        
        return if (waId.startsWith("+")) waId else "+$waId"
    }

    private fun resolveMessageAuthorName(payload: MetaWebhookPayload): String {
        return payload.entry.firstOrNull()?.changes?.firstOrNull()?.value?.contacts?.firstOrNull()?.profile?.name
            ?: "GI"
    }

    private fun resolveMessage(payload: MetaWebhookPayload): String? {
        val firstMessage = getFirstMessage(payload) ?: return null
        
        val messageBuilder = StringBuilder()
        
        // Se a mensagem é uma resposta a outra mensagem
        if (isRepliedMessage(firstMessage)) {
            val context = firstMessage.context
            if (context?.id != null) {
                // TODO: Implementar o serviço para recuperar a mensagem de contexto
                messageBuilder.append("Você disse: \n")
                    .append("Mensagem de contexto (ID: ${context.id})\n")
                    .append("########################\n")
                    .append("E agora eu te falo: ")
            }
        }
        
        // Mensagem atual do usuário
        if (firstMessage.text != null) {
            messageBuilder.append(firstMessage.text.body)
        }
        
        // Legenda da imagem
        val imageCaption = getFirstMessageImageCaption(payload)
        if (imageCaption != null) {
            messageBuilder.append(imageCaption)
        }
        
        return if (messageBuilder.isEmpty()) null else messageBuilder.toString()
    }

    private fun isRepliedMessage(message: MetaWebhookPayload.Message): Boolean {
        return message.context != null
    }

    private fun resolveMedia(payload: MetaWebhookPayload): ChatMessageEvent.Media? {
        val messageType = getFirstMessageType(payload) ?: return null
        
        if (messageType.equals(META_MESSAGE_TEXT_TYPE, ignoreCase = true)) {
            return null
        }
        
        val mediaId = extractMetaMediaId(payload, messageType) ?: return null
        
        try {
            logger.info("Recuperando mídia do Meta com ID [$mediaId] e tipo de mensagem [$messageType]")
            
            // TODO: Implementar o serviço de mídia para obter a mídia
            // Por enquanto, criamos uma mídia fictícia
            val media = ChatMessageEvent.Media(
                type = getMediaType(messageType),
                mimeType = "application/octet-stream",
                fileName = "media_$mediaId",
                caption = getFirstMessageImageCaption(payload)
            )
            
            logger.info("Propriedades da mídia definidas para $messageType: mimeType=${media.mimeType}, fileName=${media.fileName}")
            return media
        } catch (e: IOException) {
            logger.error("Falha ao recuperar mídia do Meta com ID [$mediaId] e tipo de mensagem [$messageType]", e)
            throw RuntimeException("Erro ao processar mídia", e)
        }
    }

    private fun extractMetaMediaId(payload: MetaWebhookPayload, messageType: String): String? {
        return when (messageType.toLowerCase()) {
            META_MESSAGE_IMAGE_TYPE -> getFirstMessageImageId(payload)
            META_MESSAGE_AUDIO_TYPE -> getFirstMessageAudioId(payload)
            META_MESSAGE_DOCUMENT_TYPE -> getFirstMessageDocumentId(payload)
            else -> {
                logger.warn("Tipo de mídia do Meta não suportado [$messageType] para o evento: $payload")
                null
            }
        }
    }

    private fun getMediaType(messageType: String): MediaType {
        return when (messageType.toLowerCase()) {
            META_MESSAGE_DOCUMENT_TYPE -> MediaType.DOCUMENT
            META_MESSAGE_IMAGE_TYPE -> MediaType.IMAGE
            META_MESSAGE_AUDIO_TYPE -> MediaType.AUDIO
            else -> MediaType.TEXT
        }
    }

    private fun resolveReferral(payload: MetaWebhookPayload): ChatMessageEvent.Referral? {
        val firstMessage = getFirstMessage(payload) ?: return null
        val referral = firstMessage.referral ?: return null
        
        return ChatMessageEvent.Referral(
            sourceUrl = referral.sourceUrl,
            sourceId = referral.sourceId,
            sourceType = referral.sourceType,
            body = referral.body,
            headline = referral.headline,
            mediaType = referral.mediaType,
            imageUrl = referral.imageUrl,
            ctwaClid = referral.ctwaClid
        )
    }

    // Métodos auxiliares para acessar os dados do payload
    private fun getFirstMessage(payload: MetaWebhookPayload): MetaWebhookPayload.Message? {
        return payload.entry.firstOrNull()?.changes?.firstOrNull()?.value?.messages?.firstOrNull()
    }

    private fun getFirstMessageId(payload: MetaWebhookPayload): String? {
        return getFirstMessage(payload)?.id
    }

    private fun getFirstMessageTimestamp(payload: MetaWebhookPayload): Long? {
        return getFirstMessage(payload)?.timestamp?.toLongOrNull()
    }

    private fun getFirstMessageType(payload: MetaWebhookPayload): String? {
        val message = getFirstMessage(payload) ?: return null
        
        return when {
            message.text != null -> META_MESSAGE_TEXT_TYPE
            message.image != null -> META_MESSAGE_IMAGE_TYPE
            message.audio != null -> META_MESSAGE_AUDIO_TYPE
            message.document != null -> META_MESSAGE_DOCUMENT_TYPE
            else -> null
        }
    }

    private fun getFirstMessageImageId(payload: MetaWebhookPayload): String? {
        return getFirstMessage(payload)?.image?.id
    }

    private fun getFirstMessageAudioId(payload: MetaWebhookPayload): String? {
        return getFirstMessage(payload)?.audio?.id
    }

    private fun getFirstMessageDocumentId(payload: MetaWebhookPayload): String? {
        return getFirstMessage(payload)?.document?.id
    }

    private fun getFirstMessageImageCaption(payload: MetaWebhookPayload): String? {
        return getFirstMessage(payload)?.image?.caption
    }

    /**
     * Obtém ou cria um chat com base no número de telefone.
     */
    private fun getChatFromPhoneNumber(phoneNumber: String, metaPhoneNumberId: String): ChatEntity {
        return chatService.findOrCreateChat(
            userPhoneNumber = phoneNumber,
            metaPhoneNumberId = metaPhoneNumberId
        )
    }
} 