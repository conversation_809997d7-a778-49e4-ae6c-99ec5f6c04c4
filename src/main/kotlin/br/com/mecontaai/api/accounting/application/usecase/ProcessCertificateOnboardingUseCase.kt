package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.domain.dto.OnboardingResultDto
import org.springframework.web.multipart.MultipartFile

/**
 * Use case para processar o onboarding de um certificado digital.
 */
interface ProcessCertificateOnboardingUseCase {
    /**
     * Executa o processo de onboarding de um certificado digital.
     *
     * @param empresaId ID da empresa
     * @param certificado Arquivo do certificado digital
     * @param senha Senha do certificado
     * @return Resultado do processo de onboarding
     */
    fun execute(empresaId: Long, certificado: MultipartFile, senha: String): OnboardingResultDto
} 