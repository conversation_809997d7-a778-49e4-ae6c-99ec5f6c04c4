package br.com.mecontaai.api.accounting.infrastructure.database.model

import br.com.mecontaai.api.accounting.domain.enum.AddressType
import br.com.mecontaai.api.accounting.domain.enum.EntityType
import jakarta.persistence.*

/**
 * Entity class representing an address in the database.
 */
@Entity
@Table(name = "addresses")
class AddressEntity(
    @Column(name = "entity_type", nullable = false)
    @Enumerated(EnumType.STRING)
    val entityType: EntityType,

    @Column(name = "entity_id", nullable = false)
    val entityId: Long,

    @Column(nullable = false)
    val street: String,

    @Column(nullable = false)
    val number: String,

    @Column
    val complement: String? = null,

    @Column(nullable = false)
    val neighborhood: String,

    @Column(nullable = false)
    val city: String,

    @Column(nullable = false, length = 2)
    val state: String,

    @Column(name = "zip_code", nullable = false)
    val zipCode: String,

    @Column(name = "is_primary")
    val isPrimary: Boolean = false,

    @Column(name = "address_type")
    @Enumerated(EnumType.STRING)
    val addressType: AddressType? = null
) : BaseEntity()