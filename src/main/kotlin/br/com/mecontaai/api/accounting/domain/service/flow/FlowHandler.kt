package br.com.mecontaai.api.accounting.domain.service.flow

import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse

/**
 * Abstract base class for handling specific WhatsApp flows.
 * Each flow (onboarding, company registration, etc.) should have its own handler.
 */
abstract class FlowHandler {
    /**
     * Unique identifier for this flow
     */
    abstract val flowId: String

    /**
     * Abstract method that each FlowHandler must implement to define
     * the mapping between screens and their corresponding handler methods
     */
    abstract fun getScreenHandlers(): Map<String, (FlowRequest) -> FlowResponse>

    /**
     * Handle a flow request and return appropriate response
     */
    fun handle(request: FlowRequest): FlowResponse {
        return when (request.action.lowercase()) {
            "data_exchange" -> handleDataExchange(request)
            else -> errorResponse("Unknown action: ${request.action}")
        }
    }

    /**
     * Generic method that gets the screen and calls the linked method
     */
    private fun handleDataExchange(request: FlowRequest): FlowResponse {
        val screen = request.screen
        val screenHandlers = getScreenHandlers()

        // Validate screen is supported
        if (screen !in screenHandlers.keys) {
            return errorResponse("Unsupported screen: $screen. Supported screens: ${screenHandlers.keys}")
        }

        return try {
            // Get the handler method for this screen and execute it
            val handler = screenHandlers[screen]!!
            handler(request)
        } catch (e: Exception) {
            errorResponse("Error processing screen '$screen': ${e.message}")
        }
    }

    /**
     * Helper method for creating error responses
     */
    protected fun errorResponse(message: String): FlowResponse {
        return FlowResponse(
            screen = "ERROR",
            data = mapOf(
                "type" to "data_exchange",
                "errors" to mapOf("general" to message)
            )
        )
    }
}
