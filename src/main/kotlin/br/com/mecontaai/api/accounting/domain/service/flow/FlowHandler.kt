package br.com.mecontaai.api.accounting.domain.service.flow

import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse

/**
 * Interface for handling specific WhatsApp flows.
 * Each flow (onboarding, company registration, etc.) should have its own handler.
 */
interface FlowHandler {
    /**
     * Unique identifier for this flow
     */
    val flowId: String
    
    /**
     * Handle a flow request and return appropriate response
     */
    fun handle(request: FlowRequest): FlowResponse
}
