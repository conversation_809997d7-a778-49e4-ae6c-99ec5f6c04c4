package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.domain.entity.UserChat
import br.com.mecontaai.api.accounting.domain.repository.UserChatRepository
import br.com.mecontaai.api.accounting.infrastructure.database.model.UserChatEntity
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * JPA implementation of UserChatRepository.
 */
@Repository
class UserChatRepositoryImpl(
    private val userChatEntityRepository: UserChatEntityRepository,
    private val userEntityRepository: UserEntityRepository,
    private val chatEntityRepository: ChatEntityRepository,
    private val companyEntityRepository: CompanyEntityRepository
) : UserChatRepository {

    override fun save(userChat: UserChat): UserChat {
        val userEntity = userEntityRepository.findById(userChat.userId)
            .orElseThrow { IllegalArgumentException("User not found with id: ${userChat.userId}") }
        
        val chatEntity = chatEntityRepository.findById(userChat.chatId)
            .orElseThrow { IllegalArgumentException("Chat not found with id: ${userChat.chatId}") }
        
        val companyEntity = userChat.companyId?.let { companyId ->
            companyEntityRepository.findById(companyId)
                .orElseThrow { IllegalArgumentException("Company not found with id: $companyId") }
        }
        
        val entity = UserChatEntity(
            user = userEntity,
            chat = chatEntity,
            company = companyEntity
        ).apply {
            if (userChat.id != null) {
                this.id = userChat.id
            }
            this.uuid = userChat.uuid
            this.isActive = userChat.isActive
            this.createdAt = userChat.createdAt
            this.updatedAt = userChat.updatedAt
        }
        
        val savedEntity = userChatEntityRepository.save(entity)
        return mapToUserChat(savedEntity)
    }

    override fun findByUuid(uuid: UUID): UserChat? {
        return userChatEntityRepository.findByUuid(uuid)
            .map { mapToUserChat(it) }
            .orElse(null)
    }

    override fun findActiveByUserIdAndChatId(userId: Long, chatId: Long): UserChat? {
        return userChatEntityRepository.findByUserIdAndChatIdAndIsActive(userId, chatId, true)
            ?.let { mapToUserChat(it) }
    }

    override fun existsActiveByUserIdAndChatId(userId: Long, chatId: Long): Boolean {
        return userChatEntityRepository.existsByUserIdAndChatIdAndIsActive(userId, chatId, true)
    }

    override fun update(userChat: UserChat): UserChat {
        return save(userChat)
    }

    private fun mapToUserChat(entity: UserChatEntity): UserChat {
        return UserChat(
            id = entity.id,
            uuid = entity.uuid,
            userId = entity.user.id!!,
            chatId = entity.chat.id!!,
            companyId = entity.company?.id,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
} 