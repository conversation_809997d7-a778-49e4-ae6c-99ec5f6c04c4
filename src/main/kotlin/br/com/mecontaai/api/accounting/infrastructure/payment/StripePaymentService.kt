package br.com.mecontaai.api.accounting.infrastructure.payment

import br.com.mecontaai.api.accounting.domain.service.payment.PaymentLinkResult
import br.com.mecontaai.api.accounting.domain.service.payment.PaymentService
import br.com.mecontaai.api.accounting.domain.service.payment.PaymentStatus
import br.com.mecontaai.api.accounting.domain.service.payment.PaymentStatusEnum
import com.stripe.Stripe
import com.stripe.model.PaymentIntent
import com.stripe.model.checkout.Session
import com.stripe.param.checkout.SessionCreateParams
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.math.BigDecimal
import jakarta.annotation.PostConstruct

/**
 * Implementação do PaymentService usando Stripe.
 */
@Service
class StripePaymentService(
    @Value("\${stripe.api.secret-key}")
    private val secretKey: String,
    
    @Value("\${stripe.urls.success}")
    private val defaultSuccessUrl: String,
    
    @Value("\${stripe.urls.cancel}")
    private val defaultCancelUrl: String
) : PaymentService {
    
    private val logger = LoggerFactory.getLogger(javaClass)
    
    @PostConstruct
    fun init() {
        if (secretKey.isNotBlank()) {
            Stripe.apiKey = secretKey
            logger.info("✅ Stripe configurado com sucesso")
        } else {
            logger.warn("⚠️ Stripe secret key não configurada")
        }
    }
    
    override fun createSubscriptionPaymentLink(
        planId: String,
        planName: String,
        amount: BigDecimal,
        currency: String,
        customerEmail: String,
        customerName: String?,
        successUrl: String?,
        cancelUrl: String?
    ): PaymentLinkResult {
        logger.info("💳 Criando link de pagamento de assinatura - Plano: $planName, Valor: $amount $currency")
        
        return try {
            if (secretKey.isBlank()) {
                return PaymentLinkResult(
                    paymentUrl = "",
                    paymentId = "",
                    success = false,
                    errorMessage = "Stripe não configurado"
                )
            }
            
            val sessionParams = SessionCreateParams.builder()
                .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
                .setSuccessUrl(successUrl ?: defaultSuccessUrl)
                .setCancelUrl(cancelUrl ?: defaultCancelUrl)
                .setCustomerEmail(customerEmail)
                .addLineItem(
                    SessionCreateParams.LineItem.builder()
                        .setPriceData(
                            SessionCreateParams.LineItem.PriceData.builder()
                                .setCurrency(currency.lowercase())
                                .setProductData(
                                    SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                        .setName(planName)
                                        .setDescription("Assinatura do plano $planName")
                                        .build()
                                )
                                .setUnitAmount((amount * BigDecimal(100)).toLong()) // Stripe usa centavos
                                .setRecurring(
                                    SessionCreateParams.LineItem.PriceData.Recurring.builder()
                                        .setInterval(SessionCreateParams.LineItem.PriceData.Recurring.Interval.MONTH)
                                        .build()
                                )
                                .build()
                        )
                        .setQuantity(1L)
                        .build()
                )
                .putMetadata("plan_id", planId)
                .putMetadata("customer_name", customerName ?: "")
                .build()
            
            val session = Session.create(sessionParams)
            
            logger.info("✅ Link de pagamento de assinatura criado: ${session.id}")
            
            PaymentLinkResult(
                paymentUrl = session.url,
                paymentId = session.id,
                expiresAt = null // Stripe sessions não têm expiração fixa
            )
            
        } catch (e: Exception) {
            logger.error("❌ Erro ao criar link de pagamento de assinatura", e)
            PaymentLinkResult(
                paymentUrl = "",
                paymentId = "",
                success = false,
                errorMessage = "Erro ao criar link de pagamento: ${e.message}"
            )
        }
    }
    
    override fun createOneTimePaymentLink(
        description: String,
        amount: BigDecimal,
        currency: String,
        customerEmail: String,
        customerName: String?,
        successUrl: String?,
        cancelUrl: String?
    ): PaymentLinkResult {
        logger.info("💳 Criando link de pagamento único - Descrição: $description, Valor: $amount $currency")
        
        return try {
            if (secretKey.isBlank()) {
                return PaymentLinkResult(
                    paymentUrl = "",
                    paymentId = "",
                    success = false,
                    errorMessage = "Stripe não configurado"
                )
            }
            
            val sessionParams = SessionCreateParams.builder()
                .setMode(SessionCreateParams.Mode.PAYMENT)
                .setSuccessUrl(successUrl ?: defaultSuccessUrl)
                .setCancelUrl(cancelUrl ?: defaultCancelUrl)
                .setCustomerEmail(customerEmail)
                .addLineItem(
                    SessionCreateParams.LineItem.builder()
                        .setPriceData(
                            SessionCreateParams.LineItem.PriceData.builder()
                                .setCurrency(currency.lowercase())
                                .setProductData(
                                    SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                        .setName(description)
                                        .build()
                                )
                                .setUnitAmount((amount * BigDecimal(100)).toLong()) // Stripe usa centavos
                                .build()
                        )
                        .setQuantity(1L)
                        .build()
                )
                .putMetadata("description", description)
                .putMetadata("customer_name", customerName ?: "")
                .build()
            
            val session = Session.create(sessionParams)
            
            logger.info("✅ Link de pagamento único criado: ${session.id}")
            
            PaymentLinkResult(
                paymentUrl = session.url,
                paymentId = session.id
            )
            
        } catch (e: Exception) {
            logger.error("❌ Erro ao criar link de pagamento único", e)
            PaymentLinkResult(
                paymentUrl = "",
                paymentId = "",
                success = false,
                errorMessage = "Erro ao criar link de pagamento: ${e.message}"
            )
        }
    }
    
    override fun getPaymentStatus(paymentId: String): PaymentStatus {
        logger.info("🔍 Verificando status do pagamento: $paymentId")
        
        return try {
            if (secretKey.isBlank()) {
                return PaymentStatus(
                    paymentId = paymentId,
                    status = PaymentStatusEnum.FAILED,
                    errorMessage = "Stripe não configurado"
                )
            }
            
            val session = Session.retrieve(paymentId)
            
            val status = when (session.paymentStatus) {
                "paid" -> PaymentStatusEnum.SUCCEEDED
                "unpaid" -> PaymentStatusEnum.PENDING
                "no_payment_required" -> PaymentStatusEnum.SUCCEEDED
                else -> PaymentStatusEnum.PENDING
            }
            
            PaymentStatus(
                paymentId = paymentId,
                status = status,
                amount = session.amountTotal?.let { BigDecimal(it).divide(BigDecimal(100)) },
                currency = session.currency?.uppercase()
            )
            
        } catch (e: Exception) {
            logger.error("❌ Erro ao verificar status do pagamento: $paymentId", e)
            PaymentStatus(
                paymentId = paymentId,
                status = PaymentStatusEnum.FAILED,
                errorMessage = "Erro ao verificar status: ${e.message}"
            )
        }
    }
}
