package br.com.mecontaai.api.accounting.domain.service.flow

import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse

interface FlowActionHandler {
    /**
     * Nome da ação que este handler processa
     */
    val actionName: String

    /**
     * Processa a requisição do flow
     */
    fun handle(request: FlowRequest): FlowResponse
}
