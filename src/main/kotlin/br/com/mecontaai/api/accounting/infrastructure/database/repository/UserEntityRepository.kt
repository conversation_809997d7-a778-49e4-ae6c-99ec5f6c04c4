package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.UserEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Spring Data JPA repository for UserEntity.
 */
@Repository
interface UserEntityRepository : JpaRepository<UserEntity, Long> {
    /**
     * Find a user by UUID.
     *
     * @param uuid the UUID to search for
     * @return the user entity if found, null otherwise
     */
    fun findByUuid(uuid: UUID): UserEntity?
    
    /**
     * Find a user by email.
     *
     * @param email the email to search for
     * @return the user entity if found, null otherwise
     */
    fun findByEmail(email: String): UserEntity?
    
    /**
     * Check if a user with the given email exists.
     *
     * @param email the email to check
     * @return true if a user with the email exists, false otherwise
     */
    fun existsByEmail(email: String): Boolean
    
    /**
     * Check if a user with the given document exists.
     *
     * @param document the document to check
     * @return true if a user with the document exists, false otherwise
     */
    fun existsByDocument(document: String): <PERSON><PERSON><PERSON>
} 