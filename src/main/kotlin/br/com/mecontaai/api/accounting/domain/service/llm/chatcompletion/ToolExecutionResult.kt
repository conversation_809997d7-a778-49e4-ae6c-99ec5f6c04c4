package br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion

import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse

/**
 * Resultado da execução de uma ferramenta.
 * Isso é esperado para ser retornado pelos Executores de ferramentas.
 * Espera-se que manualResponse ou content não seja nulo.
 *
 * @property manualResponse Resposta manual opcional da ferramenta
 * @property content Resposta opcional a ser enviada de volta ao LLM
 * @property systemInstruction Instrução opcional de sistema
 */
data class ToolExecutionResult(
    val agentResponses: List<AgentResponse>? = null,
    val content: Any? = null,
    val systemInstruction: String? = null,
)
