package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.entity.Customer
import java.util.UUID

/**
 * Service interface for Customer operations.
 */
interface CustomerService {
    /**
     * Creates a new customer.
     *
     * @param customer The customer to create
     * @return The created customer
     */
    fun createCustomer(customer: Customer): Customer
    
    /**
     * Finds a customer by ID.
     *
     * @param id The customer ID
     * @return The customer if found, null otherwise
     */
    fun findById(id: Long): Customer?
    
    /**
     * Finds a customer by UUID.
     *
     * @param uuid The customer UUID
     * @return The customer if found, null otherwise
     */
    fun findByUuid(uuid: UUID): Customer?
    
    /**
     * Finds a customer by document and company ID.
     *
     * @param document The document number
     * @param companyId The company ID
     * @return The customer if found, null otherwise
     */
    fun findByDocumentAndCompanyId(document: String, companyId: Long): Customer?
    
    /**
     * Finds all customers for a specific company.
     *
     * @param companyId The company ID
     * @return List of customers belonging to the company
     */
    fun findByCompanyId(companyId: Long): List<Customer>

    /**
     * Finds customers by company ID and customer name.
     */
    fun findByCompanyIdAndCustomerName(companyId: Long, customerName: String): List<Customer>
} 