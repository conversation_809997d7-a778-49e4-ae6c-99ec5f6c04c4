package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO para envio de mensagens com imagem via WhatsApp.
 */
data class WhatsAppImageMessageDto(
    @JsonProperty("messaging_product")
    val messagingProduct: String = "whatsapp",
    @JsonProperty("recipient_type")
    val recipientType: String = "individual",
    val to: String,
    val type: String = "image",
    val image: Image
) {
    data class Image(
        val id: String? = null,
        val link: String? = null,
        val caption: String? = null
    ) {
        init {
            require(id != null || link != null) { "É necessário fornecer um id ou link para a imagem" }
        }
    }
} 