package br.com.mecontaai.api.accounting.infrastructure.database.model

import br.com.mecontaai.api.accounting.domain.enum.ChatMessageStatus
import jakarta.persistence.*
import java.time.LocalDateTime
import java.util.UUID

/**
 * Entity class representing a chat message in the database.
 */
@Entity
@Table(name = "chat_messages")
class ChatMessageEntity(
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chat_id", nullable = false)
    val chat: ChatEntity,

    @Column(name = "external_message_id")
    val externalMessageId: String? = null,

    @Column(name = "meta_phone_number_id")
    val metaPhoneNumberId: String? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    val status: ChatMessageStatus = ChatMessageStatus.RECEIVED,

    @Column(name = "is_message_to_me", nullable = false)
    val isMessageToMe: Boolean = false,

    @Column(name = "is_message_from_me", nullable = false)
    val isMessageFromMe: Boolean = false,

    @Column(name = "is_group_message", nullable = false)
    val isGroupMessage: Boolean = false,

    @Column(name = "is_forwarded_message", nullable = false)
    val isForwardedMessage: Boolean = false,

    @Column(name = "is_from_bot", nullable = false)
    val isFromBot: Boolean = false,

    @Column(name = "message")
    val message: String? = null,

    @Column(name = "message_author_name")
    val messageAuthorName: String? = null,

    @Column(name = "message_author_phone_number")
    val messageAuthorPhoneNumber: String? = null,

    @Column(name = "media_mimetype")
    val mediaMimeType: String? = null,

    @Column(name = "media_download_url")
    val mediaDownloadUrl: String? = null,

    @Column(name = "media_filename")
    val mediaFilename: String? = null,

    @Column(name = "media_caption")
    val mediaCaption: String? = null,

    @Column(name = "sent_at")
    val sentAt: LocalDateTime? = null,

    @Column(name = "delivery_at")
    val deliveryAt: LocalDateTime? = null,

    @Column(name = "read_at")
    val readAt: LocalDateTime? = null,

    @Column(name = "originating_message_uuid")
    val originatingMessageUuid: UUID? = null
) : BaseEntity()