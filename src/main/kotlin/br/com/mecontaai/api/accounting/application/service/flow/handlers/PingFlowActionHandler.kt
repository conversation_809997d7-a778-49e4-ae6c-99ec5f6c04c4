package br.com.mecontaai.api.accounting.application.service.flow.handlers

import br.com.mecontaai.api.accounting.domain.service.flow.FlowActionHandler
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import org.springframework.stereotype.Component

@Component
class PingFlowActionHandler : FlowActionHandler {
    override val actionName = "ping"

    override fun handle(request: FlowRequest): FlowResponse {
        // Para ping, a Meta espera exatamente: {"data": {"status": "active"}}
        return FlowResponse(
            screen = null, // Meta não espera screen para ping
            data = mapOf(
                "status" to "active"
            )
        )
    }
}
