package br.com.mecontaai.api.accounting.infrastructure.meta.dto

/**
 * DTO para representar uma mensagem de botão interativo no formato da API do WhatsApp.
 *
 * @param to Número do destinatário.
 * @param text Texto da mensagem.
 * @param buttons Lista de botões no formato esperado da API do WhatsApp.
 */
data class WhatsAppInteractiveMessageDto(
    val to: String,
    val text: String,
    val buttons: List<WhatsAppButtonDto>
)