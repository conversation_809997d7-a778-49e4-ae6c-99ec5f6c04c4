package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.usecase.EnviarEmailNfseRequest
import br.com.mecontaai.api.accounting.application.usecase.EnviarEmailNfseUseCase
import br.com.mecontaai.api.accounting.domain.repository.CompanyRepository
import br.com.mecontaai.api.accounting.domain.service.NfseService
import org.springframework.stereotype.Component

/**
 * Implementação do caso de uso para enviar NFS-e por email.
 */
@Component
class EnviarEmailNfseUseCaseImpl(
    private val nfseService: NfseService,
    private val companyRepository: CompanyRepository,
) : EnviarEmailNfseUseCase {
    /**
     * Envia uma NFS-e por email.
     *
     * @param request dados para envio por email
     */
    override fun execute(request: EnviarEmailNfseRequest) {
        val company =
            companyRepository.findById(request.companyId)
                ?: throw IllegalArgumentException("Empresa com ID ${request.companyId} não encontrada")

        if (company.token.isNullOrBlank()) {
            throw IllegalStateException("Empresa não possui token Focus NFe cadastrado")
        }

        // TODO: undestand when to use company's token
        nfseService.sendNfseEmail(request.reference, request.emails)
//        nfseService.sendNfseEmail(request.reference, request.emails, company.focusKey!!)
    }
}
