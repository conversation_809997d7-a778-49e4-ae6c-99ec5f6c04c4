package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table

@Entity
@Table(name = "context_chat_messages")
class ContextChatMessage(
    @Column(name = "chat_id", nullable = false)
    val chatId: Long,

    @Column(name = "role", nullable = false)
    val role: String, // "user", "assistant", "system"

    @Column(name = "content", nullable = false)
    val content: String,

    @Column(name = "external_message_id")
    val externalMessageId: String? = null
) : BaseEntity()