package br.com.mecontaai.api.accounting.infrastructure.database.model

import br.com.mecontaai.api.accounting.domain.enum.NfseStatus
import jakarta.persistence.*
import java.math.BigDecimal

/**
 * Entidade JPA representando uma NFSe (Nota Fiscal de Serviço Eletrônica) no banco de dados.
 */
@Entity
@Table(name = "nfses")
class NfseEntity(
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id", nullable = false)
    val company: CompanyEntity,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    val customer: CustomerEntity,

    @Column(nullable = false, columnDefinition = "TEXT")
    val description: String,

    @Column(nullable = false, precision = 10, scale = 2)
    val amount: BigDecimal,

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 30)
    val status: NfseStatus = NfseStatus.PROCESSING_AUTHORIZATION,

    @Column(name = "external_process_id")
    val externalProcessId: String? = null,

    @Column(name = "xml_blob_uuid")
    val xmlBlobUuid: java.util.UUID? = null,

    @Column(name = "pdf_blob_uuid")
    val pdfBlobUuid: java.util.UUID? = null
) : BaseEntity()