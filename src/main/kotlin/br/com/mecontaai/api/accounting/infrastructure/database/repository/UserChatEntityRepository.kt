package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.UserChatEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

/**
 * JPA repository for UserChatEntity.
 */
@Repository
interface UserChatEntityRepository : JpaRepository<UserChatEntity, Long> {
    /**
     * Find a user-chat relationship by UUID.
     *
     * @param uuid The relationship UUID
     * @return The relationship if found, wrapped in Optional
     */
    fun findByUuid(uuid: UUID): Optional<UserChatEntity>

    /**
     * Find all chats for a user.
     *
     * @param userId The user ID
     * @return List of user-chat relationships
     */
    fun findByUserId(userId: Long): List<UserChatEntity>

    /**
     * Find all users for a chat.
     *
     * @param chatId The chat ID
     * @return List of user-chat relationships
     */
    fun findByChatId(chatId: Long): List<UserChatEntity>

    /**
     * Check if a relationship exists between a user and a chat.
     *
     * @param userId The user ID
     * @param chatId The chat ID
     * @return true if the relationship exists, false otherwise
     */
    fun existsByUserIdAndChatId(userId: Long, chatId: Long): Boolean

    /**
     * Find a user-chat relationship by user ID, chat ID and active status.
     *
     * @param userId The user ID
     * @param chatId The chat ID
     * @param isActive The active status
     * @return The relationship if found, null otherwise
     */
    fun findByUserIdAndChatIdAndIsActive(userId: Long, chatId: Long, isActive: Boolean): UserChatEntity?

    /**
     * Check if an active relationship exists between a user and a chat.
     *
     * @param userId The user ID
     * @param chatId The chat ID
     * @param isActive The active status
     * @return true if the relationship exists, false otherwise
     */
    fun existsByUserIdAndChatIdAndIsActive(userId: Long, chatId: Long, isActive: Boolean): Boolean

    /**
     * Find active user-chat relationships by chat ID.
     *
     * @param chatId The chat ID
     * @return List of active user-chat relationships
     */
    fun findByChatIdAndIsActive(chatId: Long, isActive: Boolean): UserChatEntity?

    /**
     * Find active user-chat relationship by company ID.
     *
     * @param companyId The company ID
     * @param isActive The active status
     * @return The relationship if found, null otherwise
     */
    fun findByCompanyIdAndIsActive(companyId: Long, isActive: Boolean): UserChatEntity?
} 