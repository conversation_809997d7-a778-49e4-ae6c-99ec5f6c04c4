package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.dto.CreateCustomerDto
import br.com.mecontaai.api.accounting.application.dto.CustomerDto
import br.com.mecontaai.api.accounting.domain.entity.Customer
import br.com.mecontaai.api.accounting.domain.enum.DocumentType
import br.com.mecontaai.api.accounting.domain.exception.CustomerAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.service.CustomerService
import br.com.mecontaai.api.accounting.domain.repository.CustomerRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/**
 * Caso de uso para criação de um cliente.
 */
@Component
class CreateCustomerUseCase(
    private val customerService: CustomerService,
    private val customerRepository: CustomerRepository
) {
    
    /**
     * Cria um novo cliente.
     *
     * @param dto DTO com os dados do cliente a ser criado
     * @return DTO com os dados do cliente criado
     * @throws CustomerAlreadyExistsException se já existir um cliente com o mesmo documento para a mesma empresa
     */
    @Transactional
    fun execute(dto: CreateCustomerDto): CustomerDto {
        // Verifica se já existe um cliente com este documento para esta empresa
        val existingCustomer = customerRepository.findByDocumentAndCompanyId(dto.document, dto.companyId)
        if (existingCustomer != null && dto.document != "") {
            throw CustomerAlreadyExistsException(dto.document, dto.companyId)
        }
        
        // Obtém o DocumentType a partir do código
        val documentType = DocumentType.fromCode(dto.documentType.code)
            ?: throw IllegalArgumentException("Tipo de documento inválido: ${dto.documentType.code}")
        
        // Cria a entidade de cliente
        val customer = Customer(
            name = dto.name,
            document = dto.document,
            documentType = documentType,
            companyId = dto.companyId,
            isForeigner = dto.isForeigner,
            email = dto.email,
            phone = dto.phone
        )
        
        // Usa o serviço para criar o cliente (que já tem validações)
        val savedCustomer = customerService.createCustomer(customer)
        
        // Retorna o DTO
        return CustomerDto(
            id = savedCustomer.id,
            uuid = savedCustomer.uuid,
            companyId = savedCustomer.companyId,
            name = savedCustomer.name,
            document = savedCustomer.document,
            documentType = dto.documentType, // Mantém o mesmo DocumentTypeDto que veio na requisição
            isForeigner = savedCustomer.isForeigner,
            email = savedCustomer.email,
            phone = savedCustomer.phone,
            isActive = savedCustomer.isActive,
            createdAt = savedCustomer.createdAt,
            updatedAt = savedCustomer.updatedAt,
            address = null // Endereço seria tratado separadamente
        )
    }
} 