package br.com.mecontaai.api.accounting.infrastructure.repository

import br.com.mecontaai.api.accounting.domain.entity.Nfse
import br.com.mecontaai.api.accounting.domain.repository.NfseRepository
import br.com.mecontaai.api.accounting.infrastructure.database.model.NfseEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.CompanyEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.CustomerEntity
import br.com.mecontaai.api.accounting.domain.enum.NfseStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import jakarta.persistence.EntityManager

/**
 * JPA Repository interface for NfseEntity operations.
 */
interface NfseJpaRepository : JpaRepository<NfseEntity, Long> {
    fun findByExternalProcessId(externalProcessId: String): NfseEntity?
    fun findByUuid(uuid: java.util.UUID): NfseEntity?
    fun findByCompanyId(companyId: Long): List<NfseEntity>
    fun findByCustomerId(customerId: Long): List<NfseEntity>
}

/**
 * Implementation of the NfseRepository interface using JPA for persistence.
 * Maps between domain entities and JPA entities.
 */
@Repository
class NfseRepositoryImpl(
    private val nfseJpaRepository: NfseJpaRepository,
    private val entityManager: EntityManager
) : NfseRepository {

    override fun save(nfse: Nfse): Nfse {
        val entity = if (nfse.id != null) {
            // Atualização - buscar entidade existente
            nfseJpaRepository.findById(nfse.id).orElse(null)?.let { existing ->
                NfseEntity(
                    company = existing.company,
                    customer = existing.customer,
                    description = nfse.description,
                    amount = nfse.amount,
                    status = NfseStatus.valueOf(nfse.status),
                    externalProcessId = nfse.externalProcessId,
                    xmlBlobUuid = nfse.xmlBlobUuid,
                    pdfBlobUuid = nfse.pdfBlobUuid
                ).apply {
                    id = existing.id
                    uuid = existing.uuid
                    isActive = existing.isActive
                    createdAt = existing.createdAt
                }
            } ?: throw IllegalArgumentException("NFSe with ID ${nfse.id} not found for update")
        } else {
            // Criação - usar JPA Reference para evitar buscar entidades completas
            NfseEntity(
                company = entityManager.getReference(CompanyEntity::class.java, nfse.companyId),
                customer = entityManager.getReference(CustomerEntity::class.java, nfse.customerId),
                description = nfse.description,
                amount = nfse.amount,
                status = NfseStatus.valueOf(nfse.status),
                externalProcessId = nfse.externalProcessId,
                xmlBlobUuid = nfse.xmlBlobUuid,
                pdfBlobUuid = nfse.pdfBlobUuid
            ).apply {
                uuid = nfse.uuid
            }
        }
        
        val savedEntity = nfseJpaRepository.save(entity)
        return mapToDomain(savedEntity)
    }

    override fun findByReference(reference: String): Nfse? {
        return nfseJpaRepository.findByExternalProcessId(reference)?.let { 
            mapToDomain(it) 
        }
    }

    override fun findByUuid(uuid: java.util.UUID): Nfse? {
        return nfseJpaRepository.findByUuid(uuid)?.let { 
            mapToDomain(it) 
        }
    }

    override fun findByCustomerId(customerId: Long): List<Nfse> {
        return nfseJpaRepository.findByCustomerId(customerId).map { 
            mapToDomain(it) 
        }
    }

    override fun updateStatus(reference: String, status: String, mensagemErro: String?): Nfse? {
        val entity = nfseJpaRepository.findByExternalProcessId(reference) ?: return null
        
        val updatedEntity = NfseEntity(
            company = entity.company,
            customer = entity.customer,
            description = entity.description,
            amount = entity.amount,
            status = NfseStatus.valueOf(status),
            externalProcessId = entity.externalProcessId,
            xmlBlobUuid = entity.xmlBlobUuid,
            pdfBlobUuid = entity.pdfBlobUuid
        ).apply {
            id = entity.id
            uuid = entity.uuid
            isActive = entity.isActive
            createdAt = entity.createdAt
        }
        
        val savedEntity = nfseJpaRepository.save(updatedEntity)
        return mapToDomain(savedEntity)
    }

    override fun updateBlobs(reference: String, xmlBlobUuid: java.util.UUID?, pdfBlobUuid: java.util.UUID?): Nfse? {
        val entity = nfseJpaRepository.findByExternalProcessId(reference) ?: return null
        
        val updatedEntity = NfseEntity(
            company = entity.company,
            customer = entity.customer,
            description = entity.description,
            amount = entity.amount,
            status = entity.status,
            externalProcessId = entity.externalProcessId,
            xmlBlobUuid = xmlBlobUuid ?: entity.xmlBlobUuid,
            pdfBlobUuid = pdfBlobUuid ?: entity.pdfBlobUuid
        ).apply {
            id = entity.id
            uuid = entity.uuid
            isActive = entity.isActive
            createdAt = entity.createdAt
        }
        
        val savedEntity = nfseJpaRepository.save(updatedEntity)
        return mapToDomain(savedEntity)
    }

    // Helper method para mapear entidade JPA para domínio
    private fun mapToDomain(entity: NfseEntity): Nfse {
        return Nfse(
            id = entity.id,
            uuid = entity.uuid,
            companyId = entity.company.id!!,
            customerId = entity.customer.id!!,
            description = entity.description,
            amount = entity.amount,
            status = entity.status.name,
            externalProcessId = entity.externalProcessId,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            xmlBlobUuid = entity.xmlBlobUuid,
            pdfBlobUuid = entity.pdfBlobUuid
        )
    }
}