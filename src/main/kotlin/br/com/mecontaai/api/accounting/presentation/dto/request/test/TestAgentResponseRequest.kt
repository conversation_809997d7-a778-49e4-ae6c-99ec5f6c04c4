package br.com.mecontaai.api.accounting.presentation.dto.request.test

import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty

/**
 * Request DTOs for testing MessageManagerService.sendAgentResponse method.
 */
sealed class TestAgentResponseRequest {

    /**
     * Base request with common chat information.
     */
    abstract class BaseRequest {
        @field:NotBlank(message = "User phone number is required")
        abstract val userPhoneNumber: String

        @field:NotBlank(message = "Meta phone number ID is required")
        abstract val metaPhoneNumberId: String
    }

    /**
     * Request for testing TextResponse.
     */
    data class TextRequest(
        @field:NotBlank(message = "User phone number is required")
        override val userPhoneNumber: String,

        @field:NotBlank(message = "Meta phone number ID is required")
        override val metaPhoneNumberId: String,

        @field:NotBlank(message = "Content is required")
        val content: String,

        val manual: Boolean? = true
    ) : BaseRequest()

    /**
     * Request for testing ButtonResponse.
     */
    data class ButtonRequest(
        @field:NotBlank(message = "User phone number is required")
        override val userPhoneNumber: String,

        @field:NotBlank(message = "Meta phone number ID is required")
        override val metaPhoneNumberId: String,

        @field:NotBlank(message = "Message is required")
        val message: String,

        @field:NotEmpty(message = "At least one button is required")
        @field:Valid
        val buttons: List<ButtonDto>
    ) : BaseRequest()

    /**
     * Request for testing MediaResponse.
     */
    data class MediaRequest(
        @field:NotBlank(message = "User phone number is required")
        override val userPhoneNumber: String,

        @field:NotBlank(message = "Meta phone number ID is required")
        override val metaPhoneNumberId: String,

        @field:NotBlank(message = "Media type is required")
        val mediaType: String, // IMAGE, DOCUMENT, AUDIO, VIDEO, STICKER

        @field:NotBlank(message = "URL is required")
        val url: String,

        val caption: String? = null,
        val filename: String? = null
    ) : BaseRequest()

    /**
     * Request for testing CompositeResponse.
     */
    data class CompositeRequest(
        @field:NotBlank(message = "User phone number is required")
        override val userPhoneNumber: String,

        @field:NotBlank(message = "Meta phone number ID is required")
        override val metaPhoneNumberId: String,

        @field:Valid
        val textResponses: List<TextDto>? = null,

        @field:Valid
        val buttonResponses: List<ButtonResponseDto>? = null
    ) : BaseRequest()

    /**
     * Request for testing multiple AgentResponses in sequence.
     */
    data class MultipleRequest(
        @field:NotBlank(message = "User phone number is required")
        override val userPhoneNumber: String,

        @field:NotBlank(message = "Meta phone number ID is required")
        override val metaPhoneNumberId: String,

        @field:NotEmpty(message = "At least one response is required")
        @field:Valid
        val responses: List<GenericResponseDto>
    ) : BaseRequest()

    /**
     * DTO for button information.
     */
    data class ButtonDto(
        @field:NotBlank(message = "Button ID is required")
        val id: String,

        @field:NotBlank(message = "Button title is required")
        val title: String,

        val type: String? = "REPLY", // REPLY, URL, CALL, COPY
        val payload: String? = null,
        val url: String? = null
    )

    /**
     * DTO for text response information.
     */
    data class TextDto(
        @field:NotBlank(message = "Content is required")
        val content: String,

        val manual: Boolean? = true
    )

    /**
     * DTO for button response information.
     */
    data class ButtonResponseDto(
        @field:NotBlank(message = "Message is required")
        val message: String,

        @field:NotEmpty(message = "At least one button is required")
        @field:Valid
        val buttons: List<ButtonDto>
    )

    /**
     * Generic response DTO for multiple response testing.
     */
    data class GenericResponseDto(
        @field:NotBlank(message = "Response type is required")
        val type: String, // text, button, media

        // For text responses
        val content: String? = null,

        // For button responses
        @field:Valid
        val buttons: List<ButtonDto>? = null,

        // For media responses
        val mediaType: String? = null, // IMAGE, DOCUMENT, AUDIO, VIDEO, STICKER
        val url: String? = null,
        val caption: String? = null,
        val filename: String? = null
    )
}
