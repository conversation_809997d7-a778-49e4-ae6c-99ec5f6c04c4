package br.com.mecontaai.api.accounting.presentation.dto.request.test

import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty

/**
 * Request DTO for testing MessageManagerService.sendAgentResponses method.
 */
data class TestSendAgentResponsesRequest(
    @field:NotBlank(message = "User phone number is required")
    val userPhoneNumber: String,

    @field:NotBlank(message = "Meta phone number ID is required")
    val metaPhoneNumberId: String,

    @field:NotEmpty(message = "At least one agent response is required")
    @field:Valid
    val agentResponses: List<AgentResponse>
)
