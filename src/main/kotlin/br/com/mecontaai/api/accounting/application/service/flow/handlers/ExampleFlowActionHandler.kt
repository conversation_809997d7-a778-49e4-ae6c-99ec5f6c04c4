package br.com.mecontaai.api.accounting.application.service.flow.handlers

import br.com.mecontaai.api.accounting.domain.service.flow.FlowActionHandler
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Exemplo de handler para ações de Flow.
 * Cada ação específica deve ter seu próprio handler.
 */
@Component
class ExampleFlowActionHandler : FlowActionHandler {
    
    private val logger = LoggerFactory.getLogger(javaClass)
    
    override val actionName: String = "example_action"
    
    override fun handle(request: FlowRequest): FlowResponse {
        logger.info("🔄 Processando ação: ${request.action}")
        logger.info("📱 Screen: ${request.screen}")
        logger.info("📦 Data: ${request.data}")
        logger.info("🎫 Flow Token: ${request.flowToken}")
        
        // Aqui você implementaria a lógica específica da ação
        return when (request.action.lowercase()) {
            "example_action" -> {
                // Lógica para ação de exemplo
                FlowResponse(
                    screen = "SUCCESS",
                    data = mapOf(
                        "message" to "Ação executada com sucesso!",
                        "timestamp" to System.currentTimeMillis()
                    )
                )
            }
            else -> {
                // Ação não reconhecida
                FlowResponse(
                    screen = "ERROR",
                    data = mapOf(
                        "error" to "Ação não reconhecida: ${request.action}"
                    )
                )
            }
        }
    }
}
