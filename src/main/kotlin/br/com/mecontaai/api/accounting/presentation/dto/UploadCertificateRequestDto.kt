package br.com.mecontaai.api.accounting.presentation.dto

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * DTO para a requisição de upload de certificado digital.
 */
data class UploadCertificateRequestDto(
    /**
     * ID da empresa à qual o certificado pertence
     */
    @field:NotNull(message = "O ID da empresa é obrigatório")
    val companyId: Long,
    
    /**
     * Senha do certificado digital
     */
    @field:NotBlank(message = "A senha do certificado é obrigatória")
    val password: String
) 