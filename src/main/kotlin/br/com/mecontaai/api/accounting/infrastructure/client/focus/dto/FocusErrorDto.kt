package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * Represents a detailed error returned by the Focus API.
 *
 * Focus API errors may include both a high-level error message
 * and a list of more specific error details.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class FocusErrorDto(
    /**
     * Error code identifying the type of error
     */
    val codigo: String? = null,
    
    /**
     * Human-readable error message
     */
    val mensagem: String? = null,
    
    /**
     * List of detailed error information (may be null)
     */
    val erros: List<FocusErrorDetailDto>? = null
)

/**
 * Represents a detailed error item returned by the Focus API.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class FocusErrorDetailDto(
    /**
     * Error code specific to this error
     */
    val codigo: String? = null,
    
    /**
     * Detailed error message
     */
    val mensagem: String? = null,
    
    /**
     * Suggested correction to fix the error
     */
    val correcao: String? = null
)