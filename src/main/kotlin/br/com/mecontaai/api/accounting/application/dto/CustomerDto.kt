package br.com.mecontaai.api.accounting.application.dto

import java.time.LocalDateTime
import java.util.UUID

/**
 * Application DTO for Customer data.
 */
data class CustomerDto(
    /**
     * Unique identifier
     */
    val id: Long?,

    /**
     * UUID for external references
     */
    val uuid: UUID,

    /**
     * Company ID that this customer belongs to
     */
    val companyId: Long,

    /**
     * Customer name
     */
    val name: String,

    /**
     * Document (CPF or CNPJ)
     */
    val document: String,

    /**
     * Document type (CPF or CNPJ)
     */
    val documentType: DocumentTypeDto,

    /**
     * Flag indicating if the customer is a foreigner
     */
    val isForeigner: Boolean = false,

    /**
     * Email address
     */
    val email: String?,

    /**
     * Phone number
     */
    val phone: String?,

    /**
     * Whether the customer is active
     */
    val isActive: Boolean,

    /**
     * Creation timestamp
     */
    val createdAt: LocalDateTime,

    /**
     * Last update timestamp
     */
    val updatedAt: LocalDateTime,

    /**
     * Address information
     */
    val address: AddressDto? = null
)

/**
 * Application DTO for Address data.
 */
data class AddressDto(
    /**
     * Street address
     */
    val street: String,
    
    /**
     * Street number
     */
    val number: String?,
    
    /**
     * Additional address information
     */
    val complement: String?,
    
    /**
     * Neighborhood
     */
    val neighborhood: String?,
    
    /**
     * City
     */
    val city: String?,
    
    /**
     * Municipality code (IBGE)
     */
    val cityCode: String?,
    
    /**
     * State abbreviation
     */
    val state: String?,
    
    /**
     * ZIP/Postal code
     */
    val zipCode: String?
)