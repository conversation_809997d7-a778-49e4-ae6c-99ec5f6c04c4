package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.usecase.AddCompanyToChatUseCase
import br.com.mecontaai.api.accounting.domain.entity.UserChat
import br.com.mecontaai.api.accounting.domain.repository.UserChatRepository
import br.com.mecontaai.api.accounting.domain.repository.UserRepository
import br.com.mecontaai.api.accounting.domain.repository.CompanyRepository
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.util.UUID

/**
 * Use case implementation for adding a company to a user chat.
 */
@Component
class AddCompanyToChatUseCaseImpl(
    private val userChatRepository: UserChatRepository,
    private val userRepository: UserRepository,
    private val companyRepository: CompanyRepository
) : AddCompanyToChatUseCase {

    /**
     * Associa uma empresa a um chat específico de um usuário.
     * Se já existir uma relação ativa, desativa ela e cria uma nova com a nova empresa.
     * 
     * @param userId ID do usuário
     * @param chatId ID do chat
     * @param companyId ID da empresa
     * @return UUID da relação atualizada
     */
    override fun execute(userId: Long, chatId: Long, companyId: Long): UUID {
        // Verificar se usuário existe
        val user = userRepository.findById(userId)
            ?: throw IllegalArgumentException("User not found with id: $userId")
        
        // Verificar se empresa existe
        val company = companyRepository.findById(companyId)
            ?: throw IllegalArgumentException("Company not found with id: $companyId")
        
        // Verificar se existe uma relação ativa
        var existingUserChat = userChatRepository.findActiveByUserIdAndChatId(userId, chatId)
        
        if (existingUserChat != null) {
            if (existingUserChat.companyId == companyId) {
                // Já está associado à empresa correta
                return existingUserChat.uuid
            }

            if (existingUserChat.companyId == null) {
                existingUserChat.companyId = companyId
                return userChatRepository.update(existingUserChat).uuid
            }
            
            // Desativar relação atual
            val deactivatedUserChat = UserChat(
                id = existingUserChat.id,
                uuid = existingUserChat.uuid,
                userId = existingUserChat.userId,
                chatId = existingUserChat.chatId,
                companyId = existingUserChat.companyId,
                isActive = false,
                createdAt = existingUserChat.createdAt,
                updatedAt = LocalDateTime.now()
            )
            userChatRepository.update(deactivatedUserChat)
        }
        
        // Criar nova relação ativa com a empresa
        val newUserChat = UserChat(
            userId = userId,
            chatId = chatId,
            companyId = companyId,
            isActive = true
        )
        
        val savedUserChat = userChatRepository.save(newUserChat)
        return savedUserChat.uuid
    }
} 