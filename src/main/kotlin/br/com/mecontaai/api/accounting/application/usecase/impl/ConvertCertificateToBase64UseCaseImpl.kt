package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.usecase.ConvertCertificateToBase64UseCase
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.io.File
import java.util.Base64

/**
 * Implementação do use case para conversão de certificado para base64.
 */
@Component
class ConvertCertificateToBase64UseCaseImpl : ConvertCertificateToBase64UseCase {
    
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun execute(certificado: File): String {
        logger.info("Convertendo certificado ${certificado.name} para base64")
        
        require(certificado.exists()) { "Arquivo de certificado não encontrado: ${certificado.path}" }
        require(certificado.isFile) { "Caminho não é um arquivo: ${certificado.path}" }
        require(certificado.canRead()) { "Não é possível ler o arquivo: ${certificado.path}" }
        
        return try {
            val bytes = certificado.readBytes()
            val base64 = Base64.getEncoder().encodeToString(bytes)
            logger.info("Certificado convertido com sucesso. Tamanho original: ${bytes.size} bytes, Base64: ${base64.length} chars")
            base64
        } catch (e: Exception) {
            logger.error("Erro ao converter certificado para base64", e)
            throw IllegalArgumentException("Erro ao converter certificado: ${e.message}", e)
        }
    }
} 