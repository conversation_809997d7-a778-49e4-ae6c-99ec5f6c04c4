package br.com.mecontaai.api.accounting.presentation.dto

import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime
import java.util.UUID

/**
 * DTO for user creation requests.
 */
data class CreateUserRequestDto(
    /**
     * User's full name.
     */
    @field:NotBlank(message = "Name is required")
    @field:Size(min = 3, max = 255, message = "Name must be between 3 and 255 characters")
    val name: String,
    
    /**
     * User's email.
     */
    @field:NotBlank(message = "Email is required")
    @field:Email(message = "Invalid email format")
    @field:Size(max = 255, message = "Email must be less than 255 characters")
    val email: String,
    
    /**
     * User's password.
     */
    @field:NotBlank(message = "Password is required")
    @field:Size(min = 6, message = "Password must be at least 6 characters")
    val password: String,
    
    /**
     * User's document (CPF).
     */
    @field:Size(max = 20, message = "Document must be less than 20 characters")
    val document: String? = null
)

/**
 * DTO for user responses.
 */
data class UserResponseDto(
    /**
     * User's ID.
     */
    val id: Long?,
    
    /**
     * User's UUID.
     */
    val uuid: UUID,
    
    /**
     * User's full name.
     */
    val name: String,
    
    /**
     * User's email.
     */
    val email: String,
    
    /**
     * User's document (CPF).
     */
    val document: String?,
    
    /**
     * Whether the user is active.
     */
    val isActive: Boolean,
    
    /**
     * When the user was created.
     */
    val createdAt: LocalDateTime,
    
    /**
     * When the user was last updated.
     */
    val updatedAt: LocalDateTime
) 