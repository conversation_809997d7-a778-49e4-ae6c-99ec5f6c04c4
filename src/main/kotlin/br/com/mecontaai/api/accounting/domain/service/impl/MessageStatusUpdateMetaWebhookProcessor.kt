package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.enum.MetaWebhookType
import br.com.mecontaai.api.accounting.domain.service.MetaWebhookProcessor
import br.com.mecontaai.api.accounting.infrastructure.service.SqsService
import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class MessageStatusUpdateMetaWebhookProcessor(
    private val sqsService: SqsService,
) : MetaWebhookProcessor {
    private val logger = LoggerFactory.getLogger(MessageStatusUpdateMetaWebhookProcessor::class.java)

    override val webhookType: MetaWebhookType = MetaWebhookType.MESSAGE_STATUS_UPDATE

    override fun canProcess(payload: MetaWebhookPayload): Boolean =
        payload.entry.any { entry ->
            entry.changes.any { change ->
                change.field == "messages" && change.value.statuses?.isNotEmpty() == true
            }
        }

    override fun process(payload: MetaWebhookPayload) {
        logger.info("Processing status webhook for entry: {}", payload.getEntryId())
        payload.entry.forEach { entry ->
            entry.changes.forEach { change ->
                change.value.statuses?.forEach { status ->
                    logger.info(
                        "Message status update - ID: {}, Status: {}, Timestamp: {}",
                        status.id,
                        status.status,
                        status.timestamp,
                    )
                }
            }
        }
    }
}
