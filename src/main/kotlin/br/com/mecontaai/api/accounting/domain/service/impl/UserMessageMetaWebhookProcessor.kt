package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.converter.MetaChatMessageEventConverter
import br.com.mecontaai.api.accounting.domain.enum.MetaWebhookType
import br.com.mecontaai.api.accounting.domain.service.MetaWebhookProcessor
import br.com.mecontaai.api.accounting.infrastructure.event.SqsQueues
import br.com.mecontaai.api.accounting.infrastructure.service.SqsService
import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class UserMessageMetaWebhookProcessor(
    private val metaChatMessageEventConverter: MetaChatMessageEventConverter,
    private val sqsService: SqsService,
) : MetaWebhookProcessor {
    private val logger = LoggerFactory.getLogger(UserMessageMetaWebhookProcessor::class.java)

    override val webhookType: MetaWebhookType = MetaWebhookType.USER_MESSAGE

    override fun canProcess(payload: MetaWebhookPayload): Boolean =
        payload.entry.any { entry ->
            entry.changes.any { change ->
                change.field == "messages" &&
                    change.value.messages?.isNotEmpty() == true &&
                    // Only process regular messages, not interactive button clicks
                    change.value.messages?.none { message ->
                        message.type == "interactive" && message.interactive?.buttonReply != null
                    } == true
            }
        }

    override fun process(payload: MetaWebhookPayload) {
        logger.info("Processing user message webhook for entry: {}", payload.getEntryId())

        try {
            // Convert the Meta webhook payload to domain object (ChatMessageEvent)
            val chatMessageEvent = metaChatMessageEventConverter.convert(payload)
                ?: throw IllegalStateException("Failed to convert webhook payload to ChatMessageEvent")

            logger.info("Converted Meta webhook to ChatMessageEvent: {}", chatMessageEvent.externalMessageId)

            // Publish the domain event to the domain-specific queue
            // This decouples Meta from the business logic - any messaging provider can publish ChatMessageEvent
            sqsService.sendToQueue(
                payload = chatMessageEvent,
                queueName = SqsQueues.CHAT_MESSAGE_RECEIVED,
            )

            logger.info("ChatMessageEvent published to domain queue for entry: {}", payload.getEntryId())
        } catch (e: Exception) {
            logger.error("Error processing user message for entry: ${payload.getEntryId()}", e)
            throw e
        }
    }
}
