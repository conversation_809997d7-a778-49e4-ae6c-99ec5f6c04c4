package br.com.mecontaai.api.accounting.infrastructure.service

import br.com.mecontaai.api.accounting.domain.dto.CompanyQualificationDto
import br.com.mecontaai.api.accounting.domain.service.CompanyQualificationService
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CompanyCnaeServicoEntityRepository
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CompanyEntityRepository
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CnaeEntityRepository
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CodigoServicoMunicipalEntityRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.UUID

/**
 * Implementação do CompanyQualificationService.
 * Busca qualificações de empresas através dos repositórios de infraestrutura.
 */
@Service
class CompanyQualificationServiceImpl(
    private val companyCnaeServicoEntityRepository: CompanyCnaeServicoEntityRepository,
    private val companyEntityRepository: CompanyEntityRepository,
    private val cnaeEntityRepository: CnaeEntityRepository,
    private val codigoServicoMunicipalEntityRepository: CodigoServicoMunicipalEntityRepository
) : CompanyQualificationService {
    
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun findByCompanyId(companyId: Long): List<CompanyQualificationDto> {
        logger.debug("Buscando qualificações para empresa ID: $companyId")
        
        val qualifications = companyCnaeServicoEntityRepository.findByCompanyId(companyId)
        
        return qualifications.map { entity ->
            val company = companyEntityRepository.findById(entity.company.id!!).orElseThrow()
            val cnae = cnaeEntityRepository.findById(entity.cnae.id!!).orElseThrow()
            val codigoServico = codigoServicoMunicipalEntityRepository.findById(entity.codigoServico.id!!).orElseThrow()

            CompanyQualificationDto(
                id = entity.id!!,
                uuid = entity.uuid,
                companyId = company.id!!,
                
                // Dados do CNAE
                cnaeId = cnae.id!!,
                cnaeCode = cnae.code,
                cnaeDescription = cnae.description,
                cnaeSimplesNacionalAnexo = cnae.simplesNacionalAnnexo,
                
                // Dados do Código de Serviço Municipal
                codigoServicoId = codigoServico.id!!,
                codigoServico = codigoServico.codigo,
                codigoServicoDescricao = codigoServico.descricao,
                codigoMunicipio = codigoServico.codigoMunicipio,
                
                // Flags de configuração
                isPrimary = entity.isPrimary,
                isActive = entity.isActive,
                
                // Timestamps
                createdAt = entity.createdAt.toString(),
                updatedAt = entity.updatedAt.toString()
            )
        }
    }
} 