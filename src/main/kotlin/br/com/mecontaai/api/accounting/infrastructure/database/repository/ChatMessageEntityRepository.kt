package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatMessageEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional

/**
 * JPA repository for ChatMessageEntity.
 */
@Repository
interface ChatMessageEntityRepository : JpaRepository<ChatMessageEntity, Long> {
    /**
     * Find a message by external message ID.
     *
     * @param externalMessageId The external message ID
     * @return The message if found, wrapped in Optional
     */
    fun findByExternalMessageId(externalMessageId: String): Optional<ChatMessageEntity>
} 