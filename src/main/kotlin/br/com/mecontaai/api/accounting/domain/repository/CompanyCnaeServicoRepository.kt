package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.CompanyCnaeServico

/**
 * Repositório para entidades CompanyCnaeServico.
 */
interface CompanyCnaeServicoRepository {
    fun save(companyCnaeServico: CompanyCnaeServico): CompanyCnaeServico
    fun findById(id: Long): CompanyCnaeServico?
    fun findByUuid(uuid: String): CompanyCnaeServico?
    fun findByCompanyId(companyId: Long): List<CompanyCnaeServico>
    fun findPrimaryByCompanyId(companyId: Long): CompanyCnaeServico?
    fun findByCnaeId(cnaeId: Long): List<CompanyCnaeServico>
    fun findByCodigoServicoId(codigoServicoId: Long): List<CompanyCnaeServico>
} 