package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.CertificateEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

/**
 * JPA repository for CertificateEntity.
 */
@Repository
interface CertificateEntityRepository : JpaRepository<CertificateEntity, Long> {
    /**
     * Find a certificate by UUID.
     *
     * @param uuid The certificate UUID
     * @return The certificate if found, wrapped in Optional
     */
    fun findByUuid(uuid: UUID): Optional<CertificateEntity>

    /**
     * Find the active certificate for a company.
     *
     * @param companyId The company ID
     * @return The active certificate if found, wrapped in Optional
     */
    fun findByCompanyIdAndIsActiveTrue(companyId: Long): Optional<CertificateEntity>

    /**
     * Find all certificates for a company.
     *
     * @param companyId The company ID
     * @return List of certificates for the company
     */
    fun findByCompanyId(companyId: Long): List<CertificateEntity>

    /**
     * Find a certificate by blob UUID.
     *
     * @param blobUuid The blob UUID
     * @return The certificate if found, wrapped in Optional
     */
    fun findByBlobUuid(blobUuid: String): Optional<CertificateEntity>

    /**
     * Check if a company has an active certificate.
     *
     * @param companyId The company ID
     * @return true if the company has an active certificate, false otherwise
     */
    fun existsByCompanyIdAndIsActiveTrue(companyId: Long): Boolean
} 