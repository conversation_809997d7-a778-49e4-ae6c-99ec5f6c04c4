package br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.constants

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

/**
 * Enumeração das razões de conclusão de um chat completion.
 */
enum class ChatCompletionsFinishReason(private val reason: String) {
    STOP("stop"),
    LENGTH("length"),
    TOOLS_CALLS("tool_calls"),
    CONTENT_FILTER("content_filter");

    @JsonValue
    fun getReason(): String {
        return reason
    }

    companion object {
        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun fromReason(reason: String): ChatCompletionsFinishReason {
            for (finishReason in values()) {
                if (finishReason.reason.equals(reason, ignoreCase = true)) {
                    return finishReason
                }
            }
            throw IllegalArgumentException("Valor de razão inválido: $reason")
        }
    }
} 