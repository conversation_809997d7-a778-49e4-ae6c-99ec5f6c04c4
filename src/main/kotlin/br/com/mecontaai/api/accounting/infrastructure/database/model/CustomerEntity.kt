package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*

/**
 * Entity class representing a customer in the database.
 */
@Entity
@Table(name = "customers")
class CustomerEntity(
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id", nullable = false)
    val company: CompanyEntity,

    @Column(nullable = false)
    val name: String,

    @Column(nullable = false)
    val document: String,

    @Column(name = "document_type", nullable = false)
    val documentType: String,

    @Column(name = "is_foreigner", nullable = false)
    val isForeigner: Boolean = false,

    @Column
    val email: String? = null,

    @Column
    val phone: String? = null
) : BaseEntity()