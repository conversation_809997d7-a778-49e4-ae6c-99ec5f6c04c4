package br.com.mecontaai.api.accounting.presentation.dto

import java.time.LocalDateTime
import java.util.UUID

/**
 * DTO para a resposta completa de informações de cliente.
 */
data class CustomerFullResponseDto(
    /**
     * ID do cliente
     */
    val id: Long?,
    
    /**
     * UUID do cliente para referências externas
     */
    val uuid: UUID,
    
    /**
     * ID da empresa à qual o cliente pertence
     */
    val companyId: Long,
    
    /**
     * Nome do cliente
     */
    val name: String,
    
    /**
     * CPF ou CNPJ do cliente
     */
    val document: String,
    
    /**
     * Código do tipo de documento (CPF ou CNPJ)
     */
    val documentType: String,
    
    /**
     * Descrição do tipo de documento
     */
    val documentTypeDescription: String,
    
    /**
     * Flag indicando se o cliente é estrangeiro
     */
    val isForeigner: Boolean,
    
    /**
     * Email do cliente
     */
    val email: String?,
    
    /**
     * Telefone do cliente
     */
    val phone: String?,
    
    /**
     * Flag indicando se o cliente está ativo
     */
    val isActive: Boolean,
    
    /**
     * Data de criação do cliente
     */
    val createdAt: LocalDateTime,
    
    /**
     * Data da última atualização do cliente
     */
    val updatedAt: LocalDateTime
) 