package br.com.mecontaai.api.accounting.infrastructure.config

import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

/**
 * Configuration properties for the Focus NFe API.
 */
@Configuration
@ConfigurationProperties(prefix = "focus.api")
class FocusConfig {
    private val logger = LoggerFactory.getLogger(javaClass)

    /**
     * The base URL of the Focus API.
     * Production: https://api.focusnfe.com.br
     * Sandbox/Testing: https://homologacao.focusnfe.com.br
     *
     * This value MUST be configured in application.yaml under focus.api.url
     */
    lateinit var url: String

    /**
     * The principal token for Focus API authentication.
     * This token is used for API authentication and authorization.
     *
     * This value MUST be configured in application.yaml under focus.api.token_principal
     */
    lateinit var tokenPrincipal: String

    /**
     * Getter for API URL, ensuring it doesn't end with a slash
     * to avoid double slashes in URLs.
     */
    val apiUrl: String
        get() = url.trimEnd('/')

    /**
     * Logs the configured values after the configuration is loaded.
     */
    @PostConstruct
    fun logConfiguration() {
        logger.info("🔧 FocusConfig inicializado:")
        logger.info("📍 URL configurada: $url")
        logger.info("🌐 API URL (processada): $apiUrl")

        // Log do token (mascarado por segurança)
        val maskedToken =
            if (::tokenPrincipal.isInitialized && tokenPrincipal.isNotBlank()) {
                "${tokenPrincipal.take(8)}${"*".repeat(maxOf(0, tokenPrincipal.length - 12))}${tokenPrincipal.takeLast(4)}"
            } else {
                "NÃO CONFIGURADO"
            }
        logger.info("🔑 Token Principal: $maskedToken")

        // Validação adicional
        var hasErrors = false

        if (url.isBlank()) {
            logger.error("❌ ERRO: focus.api.url não está configurada!")
            hasErrors = true
        } else if (!url.startsWith("http")) {
            logger.warn("⚠️ AVISO: URL não parece ser válida (não começa com http): $url")
        }

        if (!::tokenPrincipal.isInitialized || tokenPrincipal.isBlank()) {
            logger.error("❌ ERRO: focus.api.token_principal não está configurado!")
            hasErrors = true
        }

        if (!hasErrors) {
            logger.info("✅ Configuração Focus API válida")
        } else {
            logger.error("❌ Configuração Focus API incompleta - verifique application.yaml")
        }
    }
}
