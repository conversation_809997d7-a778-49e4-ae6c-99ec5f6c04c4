package br.com.mecontaai.api.accounting.presentation.dto

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern
import java.time.LocalDateTime
import java.util.UUID

/**
 * DTO para requisição de criação de empresa.
 */
data class CreateCompanyRequestDto(
    /**
     * C<PERSON>J da empresa (apenas números).
     */
    @field:NotBlank(message = "CNPJ é obrigatório")
    @field:Pattern(regexp = "^\\d{14}$", message = "CNPJ deve conter 14 dígitos numéricos")
    val document: String
)

/**
 * DTO para resposta de empresa.
 */
data class CompanyResponseDto(
    /**
     * ID da empresa.
     */
    val id: Long?,
    
    /**
     * UUID da empresa.
     */
    val uuid: UUID,
    
    /**
     * Nome da empresa.
     */
    val name: String,
    
    /**
     * CNPJ da empresa.
     */
    val document: String,
    
    /**
     * Inscrição municipal.
     */
    val municipalRegistration: String?,
    
    /**
     * Inscrição estadual.
     */
    val stateRegistration: String?,
    
    /**
     * Indica se a empresa é optante pelo Simples Nacional.
     */
    val simplesNacional: Boolean,
    
    /**
     * Código do município.
     */
    val codigoMunicipio: String?,
    
    /**
     * Código SIAFI.
     */
    val codigoSiafi: String?,
    
    /**
     * Código IBGE.
     */
    val codigoIbge: String?,
    
    /**
     * ID da empresa na Focus.
     */
    val focusId: String?,
    
    /**
     * Indica se a empresa está ativa.
     */
    val isActive: Boolean,
    
    /**
     * Data de criação.
     */
    val createdAt: LocalDateTime,
    
    /**
     * Data de atualização.
     */
    val updatedAt: LocalDateTime
) 