package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp

import com.fasterxml.jackson.databind.ObjectMapper
import feign.Response
import feign.codec.ErrorDecoder
import org.slf4j.LoggerFactory
import java.nio.charset.StandardCharsets

/**
 * Decodificador de erros personalizado para a API do WhatsApp.
 */
class WhatsAppErrorDecoder(
    private val objectMapper: ObjectMapper
) : ErrorDecoder {

    private val logger = LoggerFactory.getLogger(WhatsAppErrorDecoder::class.java)

    override fun decode(methodKey: String, response: Response): Exception {
        val status = response.status()
        val responseBody: String? = try {
            response.body()?.asInputStream()?.bufferedReader(StandardCharsets.UTF_8)?.use { it.readText() }
        } catch (e: Exception) {
            logger.error("Erro ao ler o corpo da resposta da API do WhatsApp", e)
            null
        }

        if (status < 400) {
            logger.warn("Resposta inesperada com status: $status retornada para o método: $methodKey")
            return ErrorDecoder.Default().decode(methodKey, response)
        }

        return if (responseBody.isNullOrBlank()) {
            logger.error("Resposta de erro sem corpo recebida da API do WhatsApp: Status: $status")
            WhatsAppException(
                httpStatus = status,
                message = "Erro sem corpo específico retornado pela API do WhatsApp"
            )
        } else {
            logger.error("Resposta de erro recebida da API do WhatsApp: Status: $status, Corpo: $responseBody")
            parseErrorResponse(status, responseBody)
        }
    }

    /**
     * Faz o parsing do corpo de erro retornado pela API e cria uma `WhatsAppException`.
     */
    private fun parseErrorResponse(httpStatus: Int, responseBody: String): WhatsAppException {
        return try {
            val errorResponse = objectMapper.readValue(responseBody, WhatsAppErrorResponse::class.java)
            val errorMessage = errorResponse.error?.message ?: "Erro desconhecido"
            val errorCode = errorResponse.error?.code?.toString()
            val fbTraceId = errorResponse.error?.fbtraceId

            logger.error("Detalhes do erro da API do WhatsApp: Código de erro: $errorCode, fbtraceId: $fbTraceId")

            WhatsAppException(
                httpStatus = httpStatus,
                errorCode = errorCode,
                message = errorMessage,
                responseBody = responseBody
            )
        } catch (e: Exception) {
            logger.error("Erro ao processar o corpo de erro da API do WhatsApp: $responseBody", e)
            WhatsAppException(
                httpStatus = httpStatus,
                message = "Erro ao processar resposta de erro da API do WhatsApp",
                cause = e,
                responseBody = responseBody
            )
        }
    }
}

/**
 * Classe para deserialização da resposta de erro da API do WhatsApp.
 */
data class WhatsAppErrorResponse(
    val error: Error?
) {
    data class Error(
        val message: String,
        val type: String,
        val code: Int,
        val fbtraceId: String
    )
}