package br.com.mecontaai.api.accounting.application.service.flow.handlers

import br.com.mecontaai.api.accounting.application.dto.CreateUserDto
import br.com.mecontaai.api.accounting.application.service.flow.FlowSessionStore
import br.com.mecontaai.api.accounting.application.usecase.impl.CreateUserUseCase
import br.com.mecontaai.api.accounting.domain.service.flow.FlowHandler
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.util.regex.Pattern

/**
 * Handler for onboarding flow - user account creation
 */
@Component
class OnboardingV1CreateJafAccountFlowHandler(
    private val createUserUseCase: CreateUserUseCase,
    private val sessionStore: FlowSessionStore
) : FlowHandler() {
    private val logger = LoggerFactory.getLogger(javaClass)
    override val flowId = "onboarding.v1.create_jaf_account"

    // Email validation pattern
    private val emailPattern = Pattern.compile(
        "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    )
    
    override fun getScreenHandlers(): Map<String, (FlowRequest) -> FlowResponse> {
        return mapOf(
            "SIGN_UP" to ::handleSignUpScreen,
            "TERMS_AND_CONDITIONS" to ::handleTermsScreen,
            "SIGN_UP_SUCCESS" to ::handleSuccessScreen
        )
    }


    private fun handleSignUpScreen(request: FlowRequest): FlowResponse {
        logger.info("🎯 OnboardingFlowHandler processing screen: ${request.screen}")
        logger.info("📱 Screen: ${request.screen}")
        logger.info("🎫 Flow Token: ${request.flowToken}")

        val data = request.data ?: return errorResponse("No data provided")
        return handleSignUp(data, request.flowToken)
    }

    private fun handleTermsScreen(request: FlowRequest): FlowResponse {
        // Handle terms and conditions acceptance
        logger.info("📋 Processing terms acceptance")

        val data = request.data ?: return errorResponse("No data provided")
        val accepted = data["accepted"]?.let {
            when (it) {
                is Boolean -> it
                is String -> it.lowercase() in listOf("true", "1", "yes", "on")
                is Number -> it.toInt() != 0
                else -> false
            }
        } ?: false

        return if (accepted) {
            // Store terms acceptance in session
            request.flowToken?.let { token ->
                sessionStore.put(token, "terms_accepted", true)
                sessionStore.put(token, "terms_accepted_at", System.currentTimeMillis())
            }

            FlowResponse(
                screen = "SIGN_UP",
                data = mapOf(
                    "message" to "Termos aceitos com sucesso!",
                    "terms_accepted" to true
                )
            )
        } else {
            FlowResponse(
                screen = "TERMS_AND_CONDITIONS",
                data = mapOf("errors" to listOf("É necessário aceitar os termos e condições"))
            )
        }
    }

    private fun handleSuccessScreen(request: FlowRequest): FlowResponse {
        // Success screen typically doesn't need processing, just display
        logger.info("✅ Displaying success screen")

        return FlowResponse(
            screen = "SIGN_UP_SUCCESS",
            data = mapOf("message" to "Conta criada com sucesso!")
        )
    }
    
    private fun handleSignUp(data: Map<String, Any>, flowToken: String?): FlowResponse {
        logger.info("👤 Processing sign up with data: ${data.keys}")
        
        // Validate input data
        val validationErrors = validateSignUpData(data)
        if (validationErrors.isNotEmpty()) {
            logger.warn("Validation errors: $validationErrors")
            return FlowResponse(
                screen = "SIGN_UP", // Stay on same screen
                data = mapOf(
                    "errors" to validationErrors
                )
            )
        }
        
        return try {
            // Extract validated data
            val fullName = data["full_name"].toString()
            val email = data["email"].toString()
            val cpf = data["cpf"].toString()
            val password = data["password"].toString()
            
            // Create user
            val userDto = CreateUserDto(
                name = fullName,
                email = email,
                password = password,
                document = cpf
            )
            
            val createdUser = createUserUseCase.execute(userDto)
            logger.info("✅ User created successfully: ${createdUser.email}")
            
            // Store user info in session for later use
            flowToken?.let { token ->
                createdUser.id?.let { userId -> sessionStore.put(token, "user_id", userId) }
                sessionStore.put(token, "user_name", createdUser.name)
                sessionStore.put(token, "user_email", createdUser.email)
            }
            
            // Redirect to success screen
            FlowResponse(
                screen = "SIGN_UP_SUCCESS",
                data = mapOf(
                    "message" to "Conta criada com sucesso!",
                    "user_name" to createdUser.name,
                    "user_email" to createdUser.email
                )
            )
        } catch (e: Exception) {
            logger.error("Error creating user", e)
            
            // Return to same screen with error
            FlowResponse(
                screen = "SIGN_UP",
                data = mapOf(
                    "errors" to listOf(
                        when {
                            e.message?.contains("email", ignoreCase = true) == true -> 
                                "Este email já está em uso"
                            e.message?.contains("document", ignoreCase = true) == true -> 
                                "Este CPF já está cadastrado"
                            else -> "Erro ao criar conta. Tente novamente."
                        }
                    )
                )
            )
        }
    }
    
    private fun validateSignUpData(data: Map<String, Any>): List<String> {
        val errors = mutableListOf<String>()
        
        // Validate full_name
        val fullName = data["full_name"]?.toString()?.trim()
        if (fullName.isNullOrBlank()) {
            errors.add("Nome completo é obrigatório")
        } else if (fullName.length < 2) {
            errors.add("Nome deve ter pelo menos 2 caracteres")
        }
        
        // Validate email
        val email = data["email"]?.toString()?.trim()
        if (email.isNullOrBlank()) {
            errors.add("Email é obrigatório")
        } else if (!emailPattern.matcher(email).matches()) {
            errors.add("Email inválido")
        }
        
        // Validate CPF
        val cpf = data["cpf"]?.toString()?.replace(Regex("[^0-9]"), "")
        if (cpf.isNullOrBlank()) {
            errors.add("CPF é obrigatório")
        } else if (cpf.length != 11) {
            errors.add("CPF deve ter 11 dígitos")
        } else if (!isValidCpf(cpf)) {
            errors.add("CPF inválido")
        }
        
        // Validate password
        val password = data["password"]?.toString()
        if (password.isNullOrBlank()) {
            errors.add("Senha é obrigatória")
        } else if (password.length < 6) {
            errors.add("Senha deve ter pelo menos 6 caracteres")
        }
        
        // Validate password confirmation
        val confirmPassword = data["confirm_password"]?.toString()
        if (password != confirmPassword) {
            errors.add("Senhas não conferem")
        }
        
        // Validate terms agreement
        val termsAgreement = data["terms_agreement"]
        if (termsAgreement != true) {
            errors.add("Você deve concordar com os termos")
        }
        
        return errors
    }
    
    /**
     * Basic CPF validation algorithm
     */
    private fun isValidCpf(cpf: String): Boolean {
        if (cpf.length != 11) return false
        
        // Check if all digits are the same
        if (cpf.all { it == cpf[0] }) return false
        
        // Calculate first verification digit
        var sum = 0
        for (i in 0..8) {
            sum += cpf[i].digitToInt() * (10 - i)
        }
        var remainder = sum % 11
        val firstDigit = if (remainder < 2) 0 else 11 - remainder
        
        if (cpf[9].digitToInt() != firstDigit) return false
        
        // Calculate second verification digit
        sum = 0
        for (i in 0..9) {
            sum += cpf[i].digitToInt() * (11 - i)
        }
        remainder = sum % 11
        val secondDigit = if (remainder < 2) 0 else 11 - remainder
        
        return cpf[10].digitToInt() == secondDigit
    }
}
