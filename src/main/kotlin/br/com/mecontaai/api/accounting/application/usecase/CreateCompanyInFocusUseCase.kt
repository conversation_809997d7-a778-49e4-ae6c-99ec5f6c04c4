package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.domain.entity.Address
import br.com.mecontaai.api.accounting.domain.entity.Chat
import br.com.mecontaai.api.accounting.domain.entity.Company
import br.com.mecontaai.api.accounting.domain.entity.User
import br.com.mecontaai.api.accounting.domain.enum.EntityType
import br.com.mecontaai.api.accounting.domain.service.AddressService
import br.com.mecontaai.api.accounting.domain.service.ChatService
import br.com.mecontaai.api.accounting.domain.service.ChatUserService
import br.com.mecontaai.api.accounting.domain.service.CompanyService
import br.com.mecontaai.api.accounting.domain.service.UserService
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusClient
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusCreateCompanyRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusCreateCompanyResponseDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CreateCompanyInFocusUseCase(
    private val focusClient: FocusClient,
    private val companyService: CompanyService,
    private val addressService: AddressService,
    private val chatUserService: ChatUserService,
    private val userService: UserService,
    private val chatService: ChatService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    @Transactional
    fun execute(companyId: Long): String {
        logger.info("Iniciando criação de empresa na Focus. companyId={}", companyId)

        logger.info("Buscando empresa no banco de dados")
        val company =
            companyService.findById(companyId)
                ?: throw IllegalArgumentException("Empresa não encontrada com ID: $companyId")
        logger.info("Empresa encontrada. name={}, document={}", company.name, company.document)

        logger.info("Buscando endereço principal da empresa")
        val address =
            addressService.findByEntityTipeAndEntityId(EntityType.COMPANY, companyId)
                ?: throw IllegalArgumentException("Endereço principal não encontrado para a empresa: $companyId")
        logger.info("Endereço encontrado. street={}, number={}, city={}", address.street, address.number, address.city)

        logger.info("Buscando UserChat da empresa")
        val userChat =
            chatUserService.findByCompanyId(companyId)
                ?: throw IllegalArgumentException("Usuário não encontrado para a empresa: $companyId")
        logger.info("UserChat encontrado. userId={}, companyId={}", userChat.userId, userChat.chatId)

        logger.info("Buscando usuário")
        val user =
            userService.findById(userChat.userId)
                ?: throw IllegalArgumentException("Usuário não encontrado com ID: ${userChat.userId}")
        logger.info("Usuário encontrado. name={}, email={}", user.name, user.email)

        logger.info("Buscando chat")
        val chat =
            chatService.findById(userChat.chatId)
                ?: throw IllegalArgumentException("Chat não encontrado com ID: ${userChat.chatId}")
        logger.info("Chat encontrado. userPhoneNumber={}", chat.userPhoneNumber)

        logger.info("Mapeando dados para o DTO da Focus")
        val request = mapCompanyToFocusRequest(company, address, user, chat)
        logger.info("DTO da Focus criado. cnpj={}, nome={}", request.cnpj, request.nome)

        logger.info("Enviando requisição para criar empresa na Focus. Request: {}", request)
        val response = createCompanyInFocus(request)
        logger.info("Empresa criada na Focus com sucesso. focusId={}. Response: {}", response.id, response)

        logger.info("Atualizando ID da Focus na empresa")
        companyService.addFocusId(companyId, response.id.toString())
        logger.info("ID da Focus atualizado com sucesso. companyId={}, focusId={}", companyId, response.id)

        logger.info("Atualizando chave da Focus na empresa")
        companyService.addFocusKey(companyId, response.token_producao, response.token_homologacao)
        logger.info("Chave da Focus atualizada com sucesso. companyId={}", companyId)

        return response.id.toString()
    }

    private fun createCompanyInFocus(request: FocusCreateCompanyRequestDto): FocusCreateCompanyResponseDto =
        try {
            logger.info("Tentando criar empresa na Focus. cnpj={}", request.cnpj)
            focusClient.createCompany(request)
        } catch (e: Exception) {
            logger.error("Erro ao criar empresa na Focus. cnpj={}, erro={}", request.cnpj, e.message, e)
            throw RuntimeException("Erro ao criar empresa na Focus: ${e.message}", e)
        }

    private fun mapCompanyToFocusRequest(
        company: Company,
        address: Address,
        user: User,
        chat: Chat,
    ): FocusCreateCompanyRequestDto {
        logger.info("Iniciando mapeamento para DTO da Focus")
        logger.info("Dados da empresa: name={}, document={}", company.name, company.document)
        logger.info("Dados do endereço: street={}, number={}, city={}", address.street, address.number, address.city)
        logger.info("Dados do usuário: email={}", user.email)
        logger.info("Dados do chat: phone={}", chat.userPhoneNumber)

        val numeroLong =
            try {
                address.number.toLong()
            } catch (e: NumberFormatException) {
                logger.warn("Número do endereço não é um número válido: {}. Usando 0 como padrão.", address.number)
                0L
            }

        val complemento =
            if (address.complement.isNullOrBlank()) {
                logger.info("Complemento não informado, usando 'sem complemento'")
                "sem complemento"
            } else {
                address.complement!!
            }

        return FocusCreateCompanyRequestDto(
            cnpj = company.document.replace(Regex("[^0-9]"), ""),
            nome = company.name,
            nome_fantasia = company.name,
            logradouro = address.street,
            numero = numeroLong,
            complemento = complemento,
            bairro = address.neighborhood,
//            codigo_municipio = address.cityCode,
            municipio = address.city,
            uf = address.state,
            cep = address.zipCode.replace(Regex("[^0-9]"), ""),
            telefone = chat.userPhoneNumber,
            email = user.email,
            regime_tributario = 1, // 1 = Simples Nacional, 2 = Lucro Presumido, 3 = Lucro Real
//            cnae = company.cnae,
            inscricao_municipal = company.municipalRegistration,
//            inscricao_estadual = company.stateRegistration,
//            inscricao_suframa = company.suframaRegistration,
//            optante_simples_nacional = company.simplesNacional,
            discrimina_impostos = true,
            enviar_email_destinatario = false,
//            enviar_email_homologacao = false,
//            habilita_nfce = true,
//            habilita_nfe = true,
            habilita_nfse = true,
//            habilita_cte = false,
//            habilita_mdfe = false,
//            habilita_manifestacao = true,
//            certificado_valido_de = company.certificate?.validFrom?.toString(),
//            certificado_valido_ate = company.certificate?.validUntil?.toString(),
//            certificado_cnpj = company.certificate?.document,
//            certificado_especifico = false,
//            orientacao_danfe = "portrait",
//            recibo_danfe = true,
//            exibe_sempre_ipi_danfe = false,
//            exibe_issqn_danfe = false,
//            exibe_impostos_adicionais_danfe = false,
//            exibe_fatura_danfe = false,
//            exibe_unidade_tributaria_danfe = false,
//            exibe_desconto_itens = false,
//            exibe_sempre_volumes_danfe = false,
//            exibe_composicao_carga_mdfe = false,
//            habilita_csrt_nfe = true,
//            nfe_sincrono = false,
//            nfe_sincrono_homologacao = false,
//            mdfe_sincrono = false,
//            mdfe_sincrono_homologacao = false
        ).also {
            logger.info("DTO da Focus mapeado com sucesso")
        }
    }
}
