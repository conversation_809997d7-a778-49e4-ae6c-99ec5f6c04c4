package br.com.mecontaai.api.accounting.presentation.dto.webhook

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class Message(
    val timestamp: String? = null,
    val from: String? = null,
    val id: String? = null,
    val context: Context? = null,
    val text: Text? = null,
    val type: String? = null,
    val document: Document? = null,
    val image: Image? = null,
    val audio: Audio? = null,
    val sticker: Sticker? = null,
    val button: Button? = null,
    val referral: Referral? = null,
    val contacts: List<SharedContact>? = null,
    val interactive: Interactive? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Text(
    val body: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Document(
    val filename: String? = null,
    @JsonProperty("mime_type")
    val mimeType: String? = null,
    val sha256: String? = null,
    val id: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Image(
    val caption: String? = null,
    @JsonProperty("mime_type")
    val mimeType: String? = null,
    val sha256: String? = null,
    val id: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Audio(
    val voice: Boolean = false,
    @JsonProperty("mime_type")
    val mimeType: String? = null,
    val sha256: String? = null,
    val id: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Sticker(
    val animated: Boolean = false,
    @JsonProperty("mime_type")
    val mimeType: String? = null,
    val sha256: String? = null,
    val id: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Button(
    val payload: String? = null,
    val text: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Context(
    val id: String? = null,
    val forwarded: Boolean = false
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Referral(
    @JsonProperty("source_url")
    val sourceUrl: String? = null,
    @JsonProperty("source_id")
    val sourceId: String? = null,
    @JsonProperty("source_type")
    val sourceType: String? = null,
    val body: String? = null,
    val headline: String? = null,
    @JsonProperty("media_type")
    val mediaType: String? = null,
    @JsonProperty("image_url")
    val imageUrl: String? = null,
    @JsonProperty("ctwa_clid")
    val ctwaClid: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SharedContact(
    val name: SharedContactName? = null,
    val phones: List<SharedContactPhone>? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SharedContactName(
    @JsonProperty("first_name")
    val firstName: String? = null,
    @JsonProperty("formatted_name")
    val formattedName: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SharedContactPhone(
    val phone: String? = null,
    @JsonProperty("wa_id")
    val waId: String? = null,
    val type: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Interactive(
    val type: String? = null,
    @JsonProperty("button_reply")
    val buttonReply: ButtonReply? = null,
    @JsonProperty("nfm_reply")
    val nfmReply: NfmReply? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ButtonReply(
    val id: String? = null,
    val title: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class NfmReply(
    @JsonProperty("response_json")
    val responseJson: String? = null,
    val body: String? = null,
    val name: String? = null
) 