package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.entity.Address
import br.com.mecontaai.api.accounting.domain.enum.EntityType
import br.com.mecontaai.api.accounting.domain.repository.AddressRepository
import br.com.mecontaai.api.accounting.domain.service.AddressService
import org.springframework.stereotype.Service

@Service
class AddressServiceImpl(
    private val addressRepository: AddressRepository
) : AddressService {
    
    override fun findByEntityTipeAndEntityId(entityType: EntityType, entityId: Long): Address? {
        return addressRepository.findPrimaryByEntityTypeAndEntityId(entityType, entityId)
    }
}