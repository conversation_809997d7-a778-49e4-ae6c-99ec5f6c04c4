package br.com.mecontaai.api.accounting.infrastructure.database.model

import br.com.mecontaai.api.accounting.domain.enum.ContentCategory
import br.com.mecontaai.api.accounting.domain.enum.BlobEntityType
import br.com.mecontaai.api.accounting.domain.enum.StorageType
import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * Entidade que representa um blob armazenado
 */
@Entity
@Table(name = "blobs")
class BlobEntity : BaseEntity() {
    @Column(name = "content_type", nullable = false)
    var contentType: String = ""
    
    @Enumerated(EnumType.STRING)
    @Column(name = "content_category", nullable = false)
    var contentCategory: ContentCategory = ContentCategory.OTHER
    
    @Column(name = "filename")
    var filename: String? = null
    
    @Column(name = "size", nullable = false)
    var size: Long = 0
    
    @Enumerated(EnumType.STRING)
    @Column(name = "entity_type")
    var entityType: BlobEntityType? = null
    
    @Column(name = "entity_id")
    var entityId: String? = null
    
    @Enumerated(EnumType.STRING)
    @Column(name = "storage_type", nullable = false)
    var storageType: StorageType = StorageType.DATABASE
    
    @Column(name = "storage_path", nullable = false)
    var storagePath: String = ""
    
    @Column(name = "last_accessed_at")
    var lastAccessedAt: LocalDateTime? = null
    
    @Column(name = "metadata")
    var metadata: String? = null
    
    /**
     * Define valores iniciais para a entidade
     */
    fun initialize(
        contentType: String,
        contentCategory: ContentCategory,
        filename: String?,
        size: Long,
        entityType: BlobEntityType?,
        entityId: String?,
        storageType: StorageType,
        storagePath: String
    ) {
        this.contentType = contentType
        this.contentCategory = contentCategory
        this.filename = filename
        this.size = size
        this.entityType = entityType
        this.entityId = entityId
        this.storageType = storageType
        this.storagePath = storagePath
    }
} 