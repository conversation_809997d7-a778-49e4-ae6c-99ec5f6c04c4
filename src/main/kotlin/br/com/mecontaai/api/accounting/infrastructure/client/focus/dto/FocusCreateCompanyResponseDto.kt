package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class FocusCreateCompanyResponseDto(
    val id: Long,
    val nome: String,
    val nome_fantasia: String,
    val inscricao_estadual: String?,
    val inscricao_municipal: String?,
    val bairro: String?,
    val cargo_responsavel: String?,
    val cep: String?,
    val cnpj: String?,
    val cpf: String?,
    val codigo_municipio: String?,
    val codigo_pais: String?,
    val codigo_uf: String?,
    val complemento: String?,
    val cpf_cnpj_contabilidade: String?,
    val cpf_responsavel: String?,
    val discrimina_impostos: Boolean?,
    val email: String?,
    val enviar_email_destinatario: Boolean?,
    val enviar_email_homologacao: Boolean?,
    val habilita_nfce: Boolean?,
    val habilita_nfe: Boolean?,
    val habilita_nfse: Boolean?,
    val habilita_nfsen_producao: Boolean?,
    val habilita_nfsen_homologacao: Boolean?,
    val habilita_cte: Boolean?,
    val habilita_mdfe: Boolean?,
    val habilita_manifestacao: Boolean?,
    val habilita_manifestacao_homologacao: Boolean?,
    val habilita_manifestacao_cte: Boolean?,
    val habilita_manifestacao_cte_homologacao: Boolean?,
    val logradouro: String?,
    val municipio: String?,
    val nome_responsavel: String?,
    val numero: String?,
    val pais: String?,
    val regime_tributario: String?,
    val telefone: String?,
    val uf: String?,
    val habilita_contingencia_offline_nfce: Boolean?,
    val habilita_contingencia_epec_nfce: Boolean?,
    val reaproveita_numero_nfce_contingencia: Boolean?,
    val mostrar_danfse_badge: Boolean?,
    val csc_nfce_producao: String?,
    val id_token_nfce_producao: String?,
    val csc_nfce_homologacao: String?,
    val id_token_nfce_homologacao: String?,
    val proximo_numero_nfe_producao: Long?,
    val proximo_numero_nfe_homologacao: Long?,
    val serie_nfe_producao: String?,
    val serie_nfe_homologacao: String?,
    val proximo_numero_nfse_producao: Long?,
    val proximo_numero_nfse_homologacao: Long?,
    val proximo_numero_nfsen_producao: Long?,
    val proximo_numero_nfsen_homologacao: Long?,
    val serie_nfse_producao: String?,
    val serie_nfse_homologacao: String?,
    val serie_nfsen_producao: String?,
    val serie_nfsen_homologacao: String?,
    val proximo_numero_nfce_producao: Long?,
    val proximo_numero_nfce_homologacao: Long?,
    val serie_nfce_producao: String?,
    val serie_nfce_homologacao: String?,
    val proximo_numero_cte_producao: Long?,
    val proximo_numero_cte_homologacao: Long?,
    val serie_cte_producao: String?,
    val serie_cte_homologacao: String?,
    val proximo_numero_cte_os_producao: Long?,
    val proximo_numero_cte_os_homologacao: Long?,
    val serie_cte_os_producao: String?,
    val serie_cte_os_homologacao: String?,
    val proximo_numero_mdfe_producao: Long?,
    val proximo_numero_mdfe_homologacao: Long?,
    val serie_mdfe_producao: String?,
    val serie_mdfe_homologacao: String?,
    val certificado_valido_ate: String?,
    val certificado_valido_de: String?,
    val certificado_cnpj: String?,
    val certificado_especifico: Boolean?,
    val data_ultima_emissao: String?,
    val caminho_logo: String?,
    val login_responsavel: String?,
    val senha_responsavel_preenchida: Boolean?,
    val orientacao_danfe: String?,
    val recibo_danfe: Boolean?,
    val exibe_sempre_ipi_danfe: Boolean?,
    val exibe_issqn_danfe: Boolean?,
    val exibe_impostos_adicionais_danfe: Boolean?,
    val exibe_fatura_danfe: Boolean?,
    val exibe_unidade_tributaria_danfe: Boolean?,
    val exibe_desconto_itens: Boolean?,
    val exibe_sempre_volumes_danfe: Boolean?,
    val exibe_composicao_carga_mdfe: Boolean?,
    val data_inicio_recebimento_nfe: String?,
    val data_inicio_recebimento_cte: String?,
    val habilita_csrt_nfe: Boolean?,
    val nfe_sincrono: Boolean?,
    val nfe_sincrono_homologacao: Boolean?,
    val mdfe_sincrono: Boolean?,
    val mdfe_sincrono_homologacao: Boolean?,
    val smtp_endereco: String?,
    val smtp_dominio: String?,
    val smtp_autenticacao: String?,
    val smtp_porta: Int?,
    val smtp_login: String?,
    val smtp_remetente: String?,
    val smtp_responder_para: String?,
    val smtp_modo_verificacao_openssl: String?,
    val smtp_habilita_starttlls: Boolean?,
    val smtp_ssl: Boolean?,
    val smtp_tls: Boolean?,
    val token_producao: String,
    val token_homologacao: String,
    val client_app_id: String?
) 