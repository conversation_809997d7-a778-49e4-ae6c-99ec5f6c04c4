package br.com.mecontaai.api.accounting.infrastructure.crypto

import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.io.File
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.interfaces.RSAPrivateKey
import java.security.spec.MGF1ParameterSpec
import java.security.spec.PKCS8EncodedKeySpec
import java.util.*
import javax.crypto.Cipher
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.OAEPParameterSpec
import javax.crypto.spec.PSource
import javax.crypto.spec.SecretKeySpec

/**
 * Serviço responsável pela criptografia/descriptografia de WhatsApp Flows.
 *
 * Responsabilidades:
 * - Gerenciar chaves RSA privadas
 * - Descriptografar chaves AES usando RSA
 * - Descriptografar/criptografar dados de flow usando AES-GCM
 * - Conversão entre JSON e objetos de flow
 */
@Service
class WhatsAppFlowCryptoService(
    private val objectMapper: ObjectMapper,
    @Value("\${whatsapp.flows.private-key-path:keys/private_unencrypted.pem}")
    private val privateKeyPath: String,
) {
    private val logger = LoggerFactory.getLogger(WhatsAppFlowCryptoService::class.java)

    // Carrega a chave privada RSA (lazy loading)
    private val privateKey: PrivateKey by lazy {
        val key = loadPrivateKeyFromPEM(privateKeyPath)
        testPrivateKey(key)
        key
    }

    /**
     * Descriptografa uma chave AES usando RSA.
     */
    fun decryptAESKey(encryptedAesKeyBase64: String): ByteArray {
        logger.debug("Descriptografando chave AES...")

        try {
            val encryptedBytes = Base64.getDecoder().decode(encryptedAesKeyBase64)

            val cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding")
            val oaepSpec =
                OAEPParameterSpec(
                    "SHA-256",
                    "MGF1",
                    MGF1ParameterSpec.SHA256,
                    PSource.PSpecified.DEFAULT,
                )

            cipher.init(Cipher.DECRYPT_MODE, privateKey as RSAPrivateKey, oaepSpec)
            val aesKeyBytes = cipher.doFinal(encryptedBytes)

            logger.debug("Chave AES descriptografada com sucesso. Tamanho: ${aesKeyBytes.size} bytes")
            return aesKeyBytes
        } catch (ex: Exception) {
            logger.error("Erro ao descriptografar chave AES", ex)
            throw CryptoException("Falha na descriptografia da chave AES", ex)
        }
    }

    /**
     * Descriptografa dados de flow usando AES-GCM.
     */
    fun decryptFlowData(
        encryptedFlowDataBase64: String,
        ivBase64: String,
        aesKeyBytes: ByteArray,
    ): FlowRequest {
        logger.debug("Descriptografando dados do flow...")

        try {
            val cipherDataBytes = Base64.getDecoder().decode(encryptedFlowDataBase64)
            val ivBytes = Base64.getDecoder().decode(ivBase64)

            val secretKey = SecretKeySpec(aesKeyBytes, "AES")
            val gcmSpec = GCMParameterSpec(128, ivBytes)
            val cipherAES = Cipher.getInstance("AES/GCM/NoPadding")
            cipherAES.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec)

            val decryptedBytes = cipherAES.doFinal(cipherDataBytes)
            val jsonStr = String(decryptedBytes, Charsets.UTF_8)

            logger.debug("Dados do flow descriptografados com sucesso")
            return objectMapper.readValue(jsonStr, FlowRequest::class.java)
        } catch (ex: Exception) {
            logger.error("Erro ao descriptografar dados do flow", ex)
            throw CryptoException("Falha na descriptografia dos dados do flow", ex)
        }
    }

    /**
     * Criptografa resposta de flow usando AES-GCM.
     */
    fun encryptFlowResponse(
        flowResponse: FlowResponse,
        aesKeyBytes: ByteArray,
        ivBase64: String,
    ): String {
        logger.debug("Criptografando resposta do flow...")

        try {
            val jsonStr = objectMapper.writeValueAsString(flowResponse)
            val plaintextBytes = jsonStr.toByteArray(Charsets.UTF_8)

            val ivBytes = Base64.getDecoder().decode(ivBase64)
            val flippedIv = flipIv(ivBytes)

            val secretKey = SecretKeySpec(aesKeyBytes, "AES")
            val gcmSpec = GCMParameterSpec(128, flippedIv)
            val cipherAES = Cipher.getInstance("AES/GCM/NoPadding")
            cipherAES.init(Cipher.ENCRYPT_MODE, secretKey, gcmSpec)

            val ciphertextBytes = cipherAES.doFinal(plaintextBytes)
            val result = Base64.getEncoder().encodeToString(ciphertextBytes)

            logger.debug("Resposta do flow criptografada com sucesso")
            return result
        } catch (ex: Exception) {
            logger.error("Erro ao criptografar resposta do flow", ex)
            throw CryptoException("Falha na criptografia da resposta do flow", ex)
        }
    }

    /**
     * Inverte o IV conforme especificação do WhatsApp.
     */
    private fun flipIv(iv: ByteArray): ByteArray {
        val result = ByteArray(iv.size)
        for (i in iv.indices) {
            result[i] = (iv[i].toInt() xor 0xFF).toByte()
        }
        return result
    }

    /**
     * Carrega uma chave privada RSA de um arquivo PEM.
     */
    private fun loadPrivateKeyFromPEM(keyPath: String): PrivateKey {
        logger.info("Carregando chave privada RSA de: $keyPath")

        return try {
            val keyFile = File(keyPath)
            val keyContent =
                if (keyFile.exists()) {
                    keyFile.readText()
                } else {
                    loadFromResources(keyPath) ?: run {
                        logger.warn("Chave privada não encontrada, gerando chave dummy para desenvolvimento")
                        return generateDummyPrivateKey()
                    }
                }

            val cleanedKeyContent =
                keyContent
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                    .replace("-----END RSA PRIVATE KEY-----", "")
                    .replace(Regex("\\s"), "")

            val keyBytes = Base64.getDecoder().decode(cleanedKeyContent)
            val keySpec = PKCS8EncodedKeySpec(keyBytes)
            val keyFactory = KeyFactory.getInstance("RSA")
            val privateKey = keyFactory.generatePrivate(keySpec)
            return privateKey
        } catch (ex: Exception) {
            logger.error("Falha ao carregar chave privada", ex)
            logger.warn("Gerando chave dummy para desenvolvimento")
            generateDummyPrivateKey()
        }
    }

    private fun loadFromResources(keyPath: String): String? {
        val resourcePath = if (keyPath.startsWith("/")) keyPath else "/$keyPath"
        return this::class.java
            .getResourceAsStream(resourcePath)
            ?.bufferedReader()
            ?.use { it.readText() }
            ?: this::class.java.classLoader
                .getResourceAsStream(keyPath.removePrefix("/"))
                ?.bufferedReader()
                ?.use { it.readText() }
    }

    private fun testPrivateKey(key: PrivateKey) {
        logger.debug("Testando chave privada...")
        try {
            val cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding")
            cipher.init(Cipher.DECRYPT_MODE, key)
            logger.debug("Chave privada validada com sucesso")
        } catch (ex: Exception) {
            logger.warn("Falha na validação da chave privada: ${ex.message}")
        }
    }

    private fun generateDummyPrivateKey(): PrivateKey {
        logger.warn("⚠️ GERANDO CHAVE DUMMY - NÃO USE EM PRODUÇÃO!")

        val keyPairGenerator = java.security.KeyPairGenerator.getInstance("RSA")
        keyPairGenerator.initialize(2048)
        val keyPair = keyPairGenerator.generateKeyPair()

        logger.info("Chave dummy gerada (2048 bits)")
        return keyPair.private
    }
}

/**
 * Exceção específica para erros de criptografia.
 */
class CryptoException(
    message: String,
    cause: Throwable? = null,
) : RuntimeException(message, cause)
