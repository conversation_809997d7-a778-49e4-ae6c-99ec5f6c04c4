package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.CompanyEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

/**
 * JPA repository for CompanyEntity.
 */
@Repository
interface CompanyEntityRepository : JpaRepository<CompanyEntity, Long> {
    /**
     * Find a company by UUID.
     *
     * @param uuid The company UUID
     * @return The company if found, wrapped in Optional
     */
    fun findByUuid(uuid: UUID): Optional<CompanyEntity>

    /**
     * Find a company by document (CNPJ).
     *
     * @param document The company document (CNPJ)
     * @return The company if found, wrapped in Optional
     */
    fun findByDocument(document: String): Optional<CompanyEntity>

    /**
     * Check if a company with the given document (CNPJ) exists.
     *
     * @param document The document (CNPJ) to check
     * @return true if a company with the document exists, false otherwise
     */
    fun existsByDocument(document: String): <PERSON><PERSON><PERSON>
} 