package br.com.mecontaai.api.accounting.presentation.controller.api.nfse

import br.com.mecontaai.api.accounting.application.dto.EmitirNfseRequest
import br.com.mecontaai.api.accounting.application.usecase.EmitirNfseUseCase
import br.com.mecontaai.api.accounting.application.usecase.ObterStatusNfseUseCase
import br.com.mecontaai.api.accounting.application.usecase.CancelarNfseUseCase
import br.com.mecontaai.api.accounting.application.usecase.CancelarNfseRequest
import br.com.mecontaai.api.accounting.application.usecase.EnviarEmailNfseUseCase
import br.com.mecontaai.api.accounting.application.usecase.EnviarEmailNfseRequest
import br.com.mecontaai.api.accounting.application.usecase.ListarNfsesClienteUseCase
import br.com.mecontaai.api.accounting.presentation.dto.NfseRequestDto
import br.com.mecontaai.api.accounting.presentation.dto.NfseResponseDto
import br.com.mecontaai.api.accounting.presentation.dto.NfseCancelRequestDto
import br.com.mecontaai.api.accounting.presentation.dto.NfseEmailRequestDto
import br.com.mecontaai.api.accounting.presentation.mapper.NfseMapper
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import jakarta.validation.Valid

/**
 * REST controller for NFSe operations.
 */
@RestController
@RequestMapping("/api/v1/nfse")
class NfseController(
    private val emitirNfseUseCase: EmitirNfseUseCase,
    private val obterStatusNfseUseCase: ObterStatusNfseUseCase,
    private val cancelarNfseUseCase: CancelarNfseUseCase,
    private val enviarEmailNfseUseCase: EnviarEmailNfseUseCase,
    private val listarNfsesClienteUseCase: ListarNfsesClienteUseCase,
    private val nfseMapper: NfseMapper
) {

    /**
     * Creates a new NFSe.
     *
     * @param requestDto NFSe request data
     * @return NFSe response data
     */
    @PostMapping
    fun createNfse(@Valid @RequestBody requestDto: NfseRequestDto): ResponseEntity<NfseResponseDto> {
        // Map presentation DTO to EmitirNfseRequest (apenas IDs e dados básicos)
        val emitirNfseRequest = EmitirNfseRequest(
            companyId = requestDto.companyId,
            customerId = requestDto.customerId,
            discriminacao = requestDto.discriminacao,
            itemListaServico = requestDto.itemListaServico,
            valorServico = requestDto.valorServico
        )
        
        // Execute the use case (Use Case busca dados da empresa e cliente)
        val nfseDto = emitirNfseUseCase.execute(emitirNfseRequest)
        
        // Map response back to presentation DTO
        val responseDto = nfseMapper.toResponseDto(nfseDto)
        
        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto)
    }

    /**
     * Gets the status of an NFSe.
     *
     * @param reference The unique reference of the NFSe
     * @return NFSe response data
     */
    @GetMapping("/{reference}")
    fun getNfseStatus(@PathVariable reference: String): ResponseEntity<NfseResponseDto> {
        val nfseDto = obterStatusNfseUseCase.execute(reference)
        val responseDto = nfseMapper.toResponseDto(nfseDto)
        return ResponseEntity.ok(responseDto)
    }

    /**
     * Cancels an NFSe.
     *
     * @param reference The unique reference of the NFSe
     * @param requestDto Cancellation request data
     * @return NFSe response data
     */
    @DeleteMapping("/{reference}")
    fun cancelNfse(
        @PathVariable reference: String,
        @Valid @RequestBody requestDto: NfseCancelRequestDto
    ): ResponseEntity<NfseResponseDto> {
        val cancelRequest = CancelarNfseRequest(
            companyId = requestDto.companyId,
            reference = reference,
            justificativa = requestDto.justificativa
        )
        
        val nfseDto = cancelarNfseUseCase.execute(cancelRequest)
        val responseDto = nfseMapper.toResponseDto(nfseDto)
        
        return ResponseEntity.ok(responseDto)
    }

    /**
     * Sends an NFSe via email.
     *
     * @param reference The unique reference of the NFSe
     * @param requestDto Email request data
     * @return Empty response with OK status
     */
    @PostMapping("/{reference}/email")
    fun sendNfseEmail(
        @PathVariable reference: String,
        @Valid @RequestBody requestDto: NfseEmailRequestDto
    ): ResponseEntity<Void> {
        val emailRequest = EnviarEmailNfseRequest(
            companyId = requestDto.companyId,
            reference = reference,
            emails = requestDto.emails
        )
        
        enviarEmailNfseUseCase.execute(emailRequest)
        
        return ResponseEntity.ok().build()
    }

    /**
     * Gets all NFSes for a customer.
     *
     * @param customerId The customer ID
     * @return List of NFSe response data
     */
    @GetMapping("/customer/{customerId}")
    fun getNfsesByCustomer(@PathVariable customerId: String): ResponseEntity<List<NfseResponseDto>> {
        val customerIdLong = customerId.toLong()
        val nfseDtos = listarNfsesClienteUseCase.execute(customerIdLong)
        val responseDtos = nfseDtos.map { nfseMapper.toResponseDto(it) }
        return ResponseEntity.ok(responseDtos)
    }
}