package br.com.mecontaai.api.accounting.presentation.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Presentation DTO for NFSe response.
 * Mapeamento 1:1 com NfseDto - sem campos desnecessários.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class NfseResponseDto(
    /**
     * ID único da NFSe
     */
    val id: Long?,
    
    /**
     * UUID da NFSe
     */
    val uuid: String,
    
    /**
     * ID da empresa
     */
    val companyId: Long,
    
    /**
     * ID do cliente
     */
    val customerId: Long,
    
    /**
     * Descrição do serviço
     */
    val description: String,
    
    /**
     * Valor do serviço
     */
    val amount: BigDecimal,
    
    /**
     * Status atual da NFSe
     */
    val status: String,
    
    /**
     * ID do processo externo (Focus NFe)
     */
    val externalProcessId: String?,
    
    /**
     * Data de criação
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val createdAt: LocalDateTime,
    
    /**
     * Data de atualização
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val updatedAt: LocalDateTime
)

