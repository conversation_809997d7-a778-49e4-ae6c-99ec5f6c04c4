package br.com.mecontaai.api.accounting.application.dto

/**
 * DTO para a criação de cliente.
 */
data class CreateCustomerDto(
    /**
     * Nome do cliente
     */
    val name: String,

    /**
     * CPF ou CNPJ do cliente
     */
    val document: String,

    /**
     * Tip<PERSON> de documento (CPF ou CNPJ)
     */
    val documentType: DocumentTypeDto,

    /**
     * ID da empresa à qual o cliente pertence
     */
    val companyId: Long,

    /**
     * Flag indicando se o cliente é estrangeiro
     */
    val isForeigner: Boolean = false,

    /**
     * Email do cliente
     */
    val email: String? = null,

    /**
     * Telefone do cliente
     */
    val phone: String? = null
) 