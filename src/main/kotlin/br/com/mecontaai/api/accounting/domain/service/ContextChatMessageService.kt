package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.infrastructure.database.model.ContextChatMessage
import br.com.mecontaai.api.accounting.infrastructure.database.repository.ContextChatMessageRepository
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class ContextChatMessageService(
    private val contextChatMessageRepository: ContextChatMessageRepository
) {

    fun save(contextChatMessage: ContextChatMessage): ContextChatMessage {
        return contextChatMessageRepository.save(contextChatMessage)
    }

    fun getContextMessages(chatId: Long, userContextSize: Int): List<ContextChatMessage> {
        val allMessages = contextChatMessageRepository.findByChatIdOrderByCreatedAtDesc(chatId, PageRequest.of(0, 100))
        
        // Encontra o índice da N-ésima última mensagem do usuário
        val userMessages = allMessages.filter { it.role == "user" }
        if (userMessages.isEmpty()) return emptyList()
        
        val targetUserMessageIndex = minOf(userContextSize - 1, userMessages.size - 1)
        val targetUserMessage = userMessages[targetUserMessageIndex]
        
        // Encontra o índice dessa mensagem na lista completa
        val targetIndex = allMessages.indexOfFirst { it.uuid == targetUserMessage.uuid }
        if (targetIndex == -1) return emptyList()
        
        // Retorna todas as mensagens a partir da N-ésima última mensagem do usuário
        return allMessages.take(targetIndex + 1)
    }

    fun getLastMessages(chatId: Long, limit: Int): List<ContextChatMessage> {
        // Busca as mensagens em ordem decrescente (mais recentes primeiro)
        val messages = contextChatMessageRepository.findByChatIdOrderByCreatedAtDesc(
            chatId, 
            PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdAt"))
        )
        
        // Inverte a ordem para retornar em ordem cronológica (mais antigas primeiro)
        return messages.reversed()
    }
} 