package br.com.mecontaai.api.accounting.presentation.controller.test

import br.com.mecontaai.api.accounting.domain.dto.BlobMetadata
import br.com.mecontaai.api.accounting.domain.enum.BlobEntityType
import br.com.mecontaai.api.accounting.domain.enum.ContentCategory
import br.com.mecontaai.api.accounting.domain.enum.StorageType
import br.com.mecontaai.api.accounting.domain.service.BlobService
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.util.*

@RestController
@RequestMapping("/api/test/blobs")
class TestBlobController(
    private val blobService: BlobService,
) {
    /**
     * Endpoint para fazer upload de uma imagem
     */
    @PostMapping("/upload")
    fun uploadImage(
        @RequestParam("file") file: MultipartFile,
        @RequestParam("entityType", required = false) entityType: String?,
        @RequestParam("entityId", required = false) entityId: String?,
    ): ResponseEntity<Map<String, Any>> {
        // Verificar se o arquivo não está vazio
        if (file.isEmpty) {
            return ResponseEntity
                .badRequest()
                .body(mapOf("error" to "O arquivo está vazio") as Map<String, Any>)
        }

        // Criar metadata para o arquivo
        val metadata =
            BlobMetadata(
                contentType = file.contentType ?: "application/octet-stream",
                contentCategory = ContentCategory.IMAGE,
                filename = file.originalFilename ?: "unnamed.bin",
                size = file.size,
                entityType = entityType?.let { BlobEntityType.valueOf(it) },
                entityId = entityId,
                storageType = StorageType.S3,
            )

        // Salvar o arquivo
        val uuid = blobService.save(file.bytes, metadata)

        // Gerar URL pública
        val url = blobService.generatePublicUrl(uuid, 60) // URL válida por 60 minutos

        return ResponseEntity.ok(
            mapOf(
                "uuid" to uuid.toString(),
                "url" to (url ?: ""),
                "filename" to (file.originalFilename ?: "unnamed.bin"),
                "size" to file.size,
                "contentType" to (file.contentType ?: "application/octet-stream"),
            ) as Map<String, Any>,
        )
    }

    /**
     * Endpoint para buscar um arquivo pelo UUID
     */
    @GetMapping("/{uuid}")
    fun getFile(
        @PathVariable uuid: UUID,
    ): ResponseEntity<ByteArrayResource> {
        // Buscar o blob
        val blob = blobService.get(uuid) ?: return ResponseEntity.notFound().build()

        // Criar resource com os bytes do arquivo
        val resource = ByteArrayResource(blob.content)

        // Retornar o arquivo
        return ResponseEntity
            .ok()
            .contentType(MediaType.parseMediaType(blob.metadata.contentType))
            .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"${blob.metadata.filename}\"")
            .body(resource)
    }

    /**
     * Endpoint para obter a URL pública de um arquivo
     */
    @GetMapping("/{uuid}/url")
    fun getFileUrl(
        @PathVariable uuid: UUID,
        @RequestParam("minutes", defaultValue = "30") minutes: Int,
    ): ResponseEntity<Map<String, String>> {
        // Verificar se o blob existe
        if (!blobService.exists(uuid)) {
            return ResponseEntity.notFound().build()
        }

        // Gerar URL pública
        val url =
            blobService.generatePublicUrl(uuid, minutes)
                ?: return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()

        return ResponseEntity.ok(mapOf("url" to url))
    }

    /**
     * Endpoint para deletar um arquivo
     */
    @DeleteMapping("/{uuid}")
    fun deleteFile(
        @PathVariable uuid: UUID,
    ): ResponseEntity<Map<String, Boolean>> {
        val deleted = blobService.delete(uuid)
        return ResponseEntity.ok(mapOf("deleted" to deleted))
    }
}
