package br.com.mecontaai.api.accounting.presentation.dto.webhook

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class MetaWebhookPayload(
    @field:JsonProperty("object")
    val objectType: String,
    @field:JsonProperty("entry")
    val entry: List<Entry>,
) {
    fun getEntryId(): String? = entry.firstOrNull()?.id

    // Entry object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Entry(
        val id: String,
        val changes: List<Change>,
    )

    // Change object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Change(
        val field: String,
        val value: Value,
    )

    // Value object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Value(
        @JsonProperty("messaging_product")
        val messagingProduct: String? = null,
        @JsonProperty("display_phone_number")
        val displayPhoneNumber: String? = null,
        @JsonProperty("message_template_id")
        val messageTemplateId: String? = null,
        @JsonProperty("message_template_name")
        val messageTemplateName: String? = null,
        @JsonProperty("message_template_language")
        val messageTemplateLanguage: String? = null,
        val reason: String? = null,
        @JsonProperty("disable_info")
        val disableInfo: DisableInfo? = null,
        @JsonProperty("other_info")
        val otherInfo: OtherInfo? = null,
        @JsonProperty("previous_category")
        val previousCategory: String? = null,
        @JsonProperty("new_category")
        val newCategory: String? = null,
        @JsonProperty("previous_quality_score")
        val previousQualityScore: String? = null,
        @JsonProperty("new_quality_score")
        val newQualityScore: String? = null,
        @JsonProperty("event")
        val event: String? = null,
        @JsonProperty("current_limit")
        val currentLimit: String? = null,
        val metadata: Metadata? = null,
        val contacts: List<Contact>? = null,
        val messages: List<Message>? = null,
        val statuses: List<Status>? = null,
    )

    // DisableInfo object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class DisableInfo(
        @JsonProperty("disable_date")
        val disableDate: String? = null,
    )

    // OtherInfo object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class OtherInfo(
        @JsonProperty("title")
        val title: String? = null,
        @JsonProperty("description")
        val description: String? = null,
    )

    // Metadata object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Metadata(
        @JsonProperty("display_phone_number")
        val displayPhoneNumber: String? = null,
        @JsonProperty("phone_number_id")
        val phoneNumberId: String? = null,
    )

    // Contact object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Contact(
        val profile: Profile? = null,
        @JsonProperty("wa_id")
        val waId: String? = null,
    )

    // Profile object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Profile(
        val name: String? = null,
    )

    // Message object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Message(
        val timestamp: String? = null,
        val from: String? = null,
        val id: String? = null,
        val context: Context? = null,
        val text: Text? = null,
        val type: String? = null,
        val document: Document? = null,
        val image: Image? = null,
        val audio: Audio? = null,
        val sticker: Sticker? = null,
        val button: Button? = null,
        val referral: Referral? = null,
        val contacts: List<SharedContact>? = null,
        val interactive: Interactive? = null,
    )

    // Text object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Text(
        val body: String? = null,
    )

    // Document object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Document(
        val filename: String? = null,
        @JsonProperty("mime_type")
        val mimeType: String? = null,
        val sha256: String? = null,
        val id: String? = null,
    )

    // Image object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Image(
        val caption: String? = null,
        @JsonProperty("mime_type")
        val mimeType: String? = null,
        val sha256: String? = null,
        val id: String? = null,
    )

    // Audio object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Audio(
        val voice: Boolean = false,
        @JsonProperty("mime_type")
        val mimeType: String? = null,
        val sha256: String? = null,
        val id: String? = null,
    )

    // Sticker object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Sticker(
        val animated: Boolean = false,
        @JsonProperty("mime_type")
        val mimeType: String? = null,
        val sha256: String? = null,
        val id: String? = null,
    )

    // Button object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Button(
        val payload: String? = null,
        val text: String? = null,
    )

    // Context object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Context(
        val id: String? = null,
        val forwarded: Boolean = false,
    )

    // Referral object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Referral(
        @JsonProperty("source_url")
        val sourceUrl: String? = null,
        @JsonProperty("source_id")
        val sourceId: String? = null,
        @JsonProperty("source_type")
        val sourceType: String? = null,
        val body: String? = null,
        val headline: String? = null,
        @JsonProperty("media_type")
        val mediaType: String? = null,
        @JsonProperty("image_url")
        val imageUrl: String? = null,
        @JsonProperty("ctwa_clid")
        val ctwaClid: String? = null,
    )

    // SharedContact object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class SharedContact(
        val name: SharedContactName? = null,
        val phones: List<SharedContactPhone>? = null,
    )

    // SharedContactName object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class SharedContactName(
        @JsonProperty("first_name")
        val firstName: String? = null,
        @JsonProperty("formatted_name")
        val formattedName: String? = null,
    )

    // SharedContactPhone object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class SharedContactPhone(
        val phone: String? = null,
        @JsonProperty("wa_id")
        val waId: String? = null,
        val type: String? = null,
    )

    // Interactive object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Interactive(
        val type: String? = null,
        @JsonProperty("button_reply")
        val buttonReply: ButtonReply? = null,
        @JsonProperty("nfm_reply")
        val nfmReply: NfmReply? = null,
    )

    // ButtonReply object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ButtonReply(
        val id: String? = null,
        val title: String? = null,
    )

    // NfmReply object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class NfmReply(
        @JsonProperty("response_json")
        val responseJson: String? = null,
        val body: String? = null,
        val name: String? = null,
    )

    // Status object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Status(
        val id: String? = null,
        val status: String? = null,
        val timestamp: String? = null,
        @JsonProperty("recipient_id")
        val recipientId: String? = null,
        val conversation: Conversation? = null,
    )

    // Conversation object
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Conversation(
        val id: String? = null,
    )
}
