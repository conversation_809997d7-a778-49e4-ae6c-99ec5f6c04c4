package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*

/**
 * Entity class representing the relationship between users, chats and companies.
 * A user can use the same chat for different companies, but only one can be active at a time.
 * Company can be null initially until user selects which company to work with.
 * The uniqueness is enforced by a partial index on (user_id, chat_id) WHERE is_active = true.
 */
@Entity
@Table(name = "users_chats")
class UserChatEntity(
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    val user: UserEntity,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chat_id", nullable = false)
    val chat: ChatEntity,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id", nullable = true)
    val company: CompanyEntity? = null
) : BaseEntity()