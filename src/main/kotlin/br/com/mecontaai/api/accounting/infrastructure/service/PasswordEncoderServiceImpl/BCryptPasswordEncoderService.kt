package br.com.mecontaai.api.accounting.infrastructure.service.PasswordEncoderServiceImpl

import br.com.mecontaai.api.accounting.domain.service.PasswordEncoderService
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.stereotype.Service

/**
 * Implementation of PasswordEncoderService using BCrypt algorithm.
 */
@Service
class BCryptPasswordEncoderService(
    private val bCryptPasswordEncoder: BCryptPasswordEncoder
) : PasswordEncoderService {
    
    /**
     * Encode a raw password using BCrypt.
     *
     * @param rawPassword the raw password to encode
     * @return the encoded password
     */
    override fun encode(rawPassword: String): String {
        return bCryptPasswordEncoder.encode(rawPassword)
    }
    
    /**
     * Check if a raw password matches an encoded password.
     *
     * @param rawPassword the raw password to check
     * @param encodedPassword the encoded password to check against
     * @return true if the passwords match, false otherwise
     */
    override fun matches(rawPassword: String, encodedPassword: String): <PERSON><PERSON><PERSON> {
        return bCryptPasswordEncoder.matches(rawPassword, encodedPassword)
    }
} 