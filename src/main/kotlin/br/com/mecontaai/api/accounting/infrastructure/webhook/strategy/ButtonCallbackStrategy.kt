package br.com.mecontaai.api.accounting.infrastructure.webhook.strategy

import br.com.mecontaai.api.accounting.domain.service.callback.ButtonCallbackService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.webhook.model.InteractivePayload
import br.com.mecontaai.api.accounting.infrastructure.webhook.model.ProcessingResult
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Estratégia para processar cliques em botões que possuem callbacks registrados.
 * Utiliza o ButtonCallbackService para executar a ação apropriada baseada no callback.
 */
@Component
class ButtonCallbackStrategy(
    private val buttonCallbackService: ButtonCallbackService
) : InteractionStrategy {
    
    private val logger = LoggerFactory.getLogger(javaClass)
    
    override fun getInteractionType(): String = "button_callback"
    
    override fun canHandle(interactionType: String): Boolean = 
        interactionType == "button_callback"
    
    override fun process(interaction: InteractivePayload, chat: ChatEntity): ProcessingResult {
        logger.info("🔘 Processando button callback")
        logger.info("📦 Payload: ${interaction.payload}")
        logger.info("💬 Chat: ${chat.uuid}")
        logger.info("📋 Metadata: ${interaction.metadata}")
        
        return try {
            // Parse do payload (formato: callbackId:clickedValue)
            val (callbackId, clickedValue) = parseCallbackPayload(interaction.payload)
            
            logger.info("🆔 Callback ID: $callbackId")
            logger.info("👆 Clicked Value: $clickedValue")
            
            // Executa o callback
            val result = buttonCallbackService.executeCallback(callbackId, clickedValue, chat)
            
            if (result != null) {
                logger.info("✅ Callback executado com sucesso")
                val responses = result.agentResponses ?: emptyList()
                ProcessingResult.success(responses)
            } else {
                logger.warn("⚠️ Callback não encontrado ou expirado: $callbackId")
                ProcessingResult.fallback("Esta ação não está mais disponível.")
            }
            
        } catch (e: Exception) {
            logger.error("❌ Erro ao processar button callback", e)
            ProcessingResult.error("Erro ao processar clique do botão", e)
        }
    }
    
    /**
     * Faz o parse do payload do callback.
     * Formato esperado: "callbackId:clickedValue"
     */
    private fun parseCallbackPayload(payload: String): Pair<String, String> {
        val parts = payload.split(":", limit = 2)
        
        if (parts.size != 2) {
            throw IllegalArgumentException("Formato de payload inválido. Esperado: 'callbackId:clickedValue', recebido: '$payload'")
        }
        
        return Pair(parts[0], parts[1])
    }
}
