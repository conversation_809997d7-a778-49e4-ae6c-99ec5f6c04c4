package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.presentation.dto.flows.EncryptedFlowRequest

/**
 * Use case para processar WhatsApp Flows criptografados.
 */
interface ProcessWhatsAppFlowUseCase {
    /**
     * Processa um flow criptografado do WhatsApp.
     * 
     * @param request Dados criptografados do flow
     * @return Resposta criptografada para o WhatsApp
     */
    fun execute(request: EncryptedFlowRequest): String
}
