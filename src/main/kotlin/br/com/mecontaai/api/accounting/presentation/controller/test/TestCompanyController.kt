package br.com.mecontaai.api.accounting.presentation.controller.test

import br.com.mecontaai.api.accounting.application.dto.FullCompanyDataDto
import br.com.mecontaai.api.accounting.application.service.CompanyDataFetchService
import br.com.mecontaai.api.accounting.application.usecase.ValidateCompanyUseCase
import br.com.mecontaai.api.accounting.domain.dto.CompanyValidationResultDto
import br.com.mecontaai.api.accounting.domain.enum.CompanyValidationErrorType
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v1/test/companies")
class TestCompanyController(
    private val companyDataFetchService: CompanyDataFetchService,
    private val validateCompanyUseCase: ValidateCompanyUseCase,
) {
    private val logger = LoggerFactory.getLogger(TestCompanyController::class.java)

    @GetMapping("/{cnpj}")
    fun getCompanyData(
        @PathVariable cnpj: String,
    ): ResponseEntity<FullCompanyDataDto> {
        logger.info("🧪 [TEST] Fetching company data for CNPJ: $cnpj")
        val startTime = System.currentTimeMillis()
        val result = companyDataFetchService.fetchFullCompanyData(cnpj)
        val executionTime = System.currentTimeMillis() - startTime

        logger.info("🧪 [TEST] Company data fetched in ${executionTime}ms for CNPJ: $cnpj")
        return ResponseEntity.ok(result)
    }

    @GetMapping("/{cnpj}/validate")
    fun validateCompanyGet(
        @PathVariable cnpj: String,
    ): ResponseEntity<CompanyValidationResultDto> {
        logger.info("🧪 [TEST] Starting company validation for CNPJ: $cnpj")

        return try {
            val startTime = System.currentTimeMillis()
            val result = validateCompanyUseCase.execute(companyDataFetchService.fetchFullCompanyData(cnpj))
            val executionTime = System.currentTimeMillis() - startTime

            logger.info("🧪 [TEST] Validation completed in ${executionTime}ms for CNPJ: $cnpj")
            ResponseEntity.ok(result)
        } catch (e: Exception) {
            logger.error("🧪 [TEST] Error during validation for CNPJ: $cnpj", e)
            ResponseEntity.internalServerError().body(
                CompanyValidationResultDto.invalid(
                    errorType = CompanyValidationErrorType.OTHER_ERROR,
                    errorMessage = "Internal error: ${e.message}",
                ),
            )
        }
    }
}
