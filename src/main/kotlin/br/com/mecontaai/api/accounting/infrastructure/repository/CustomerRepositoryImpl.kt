package br.com.mecontaai.api.accounting.infrastructure.repository

import br.com.mecontaai.api.accounting.domain.entity.Customer
import br.com.mecontaai.api.accounting.domain.enum.DocumentType
import br.com.mecontaai.api.accounting.domain.repository.CustomerRepository
import br.com.mecontaai.api.accounting.infrastructure.database.model.CustomerEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.CompanyEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CustomerEntityRepository
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CompanyEntityRepository
import org.springframework.context.annotation.Primary
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Implementation of CustomerRepository using JPA.
 */
@Repository
@Primary
@Qualifier("jpaCustomerRepository")
class CustomerRepositoryImpl(
    private val customerEntityRepository: CustomerEntityRepository,
    private val companyEntityRepository: CompanyEntityRepository
) : CustomerRepository {

    override fun save(customer: Customer): Customer {
        val companyEntity = companyEntityRepository.findById(customer.companyId)
            .orElseThrow { IllegalArgumentException("Company not found with ID ${customer.companyId}") }
        
        val customerEntity = mapToEntity(customer, companyEntity)
        val savedEntity = customerEntityRepository.save(customerEntity)
        
        return mapToDomain(savedEntity)
    }

    override fun findById(id: Long): Customer? {
        return customerEntityRepository.findById(id)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByUuid(uuid: UUID): Customer? {
        return customerEntityRepository.findByUuid(uuid)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByDocument(document: String): Customer? {
        return customerEntityRepository.findByDocument(document)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByDocumentAndCompanyId(document: String, companyId: Long): Customer? {
        val company = companyEntityRepository.findById(companyId)
            .orElse(null) ?: return null
        
        return customerEntityRepository.findByDocumentAndCompany(document, company)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByCompanyId(companyId: Long): List<Customer> {
        val company = companyEntityRepository.findById(companyId)
            .orElse(null) ?: return emptyList()
        
        return customerEntityRepository.findByCompany(company)
            .map { mapToDomain(it) }
    }

    override fun findAll(): List<Customer> {
        return customerEntityRepository.findAll()
            .map { mapToDomain(it) }
    }

    /**
     * Map a domain Customer to a JPA CustomerEntity.
     */
    private fun mapToEntity(customer: Customer, companyEntity: CompanyEntity): CustomerEntity {
        val uuid = customer.uuid
        
        // If customer has an ID, try to get existing entity
        val existingEntity = customer.id?.let {
            customerEntityRepository.findById(it).orElse(null)
        }
        
        return CustomerEntity(
            company = companyEntity,
            name = customer.name,
            document = customer.document,
            documentType = customer.documentType.code,
            isForeigner = customer.isForeigner,
            email = customer.email,
            phone = customer.phone
        ).apply {
            // If updating an existing entity, preserve the UUID
            if (existingEntity != null) {
                this.uuid = existingEntity.uuid
            } else {
                this.uuid = uuid
            }
        }
    }

    /**
     * Map a JPA CustomerEntity to a domain Customer.
     */
    private fun mapToDomain(entity: CustomerEntity): Customer {
        // Converter o tipo de documento de String para DocumentType
        val documentType = DocumentType.fromCode(entity.documentType)
            ?: throw IllegalStateException("Invalid document type in database: ${entity.documentType}")
            
        // Note: Address list is not populated here, needs to be loaded separately
        return Customer(
            id = entity.id,
            uuid = entity.uuid,
            companyId = entity.company.id!!,
            name = entity.name,
            document = entity.document,
            documentType = documentType,
            isForeigner = entity.isForeigner,
            email = entity.email,
            phone = entity.phone,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            addresses = emptyList() // Address list would need to be populated by a service
        )
    }
} 