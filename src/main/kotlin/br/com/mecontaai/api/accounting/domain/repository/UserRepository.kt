package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.User
import java.util.UUID

/**
 * Repository interface for User operations.
 */
interface UserRepository {
    /**
     * Save a user.
     * 
     * @param user The user to save
     * @return The saved user with generated ID and UUID
     */
    fun save(user: User): User
    
    /**
     * Find a user by ID.
     * 
     * @param id The user ID
     * @return The user if found, null otherwise
     */
    fun findById(id: Long): User?
    
    /**
     * Find a user by UUID.
     * 
     * @param uuid The user UUID
     * @return The user if found, null otherwise
     */
    fun findByUuid(uuid: UUID): User?
    
    /**
     * Find a user by email.
     * 
     * @param email The user email
     * @return The user if found, null otherwise
     */
    fun findByEmail(email: String): User?
    
    /**
     * Check if an email is already in use.
     * 
     * @param email The email to check
     * @return true if the email is already in use, false otherwise
     */
    fun existsByEmail(email: String): Boolean
    
    /**
     * Check if a document (CPF) is already in use.
     * 
     * @param document The document to check
     * @return true if the document is already in use, false otherwise
     */
    fun existsByDocument(document: String): <PERSON><PERSON>an
} 