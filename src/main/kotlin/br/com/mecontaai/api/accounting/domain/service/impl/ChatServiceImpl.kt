package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.entity.Chat
import br.com.mecontaai.api.accounting.domain.service.ChatService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.ChatEntityRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * Implementation of ChatService.
 */
@Service
class ChatServiceImpl(
    private val chatEntityRepository: ChatEntityRepository
) : ChatService {
    
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun findById(id: Long): Chat? {
        logger.debug("Buscando chat por ID: $id")
        
        return chatEntityRepository.findById(id)
            .map { mapToChat(it) }
            .orElse(null)
    }

    @Transactional
    override fun findOrCreateChat(userPhoneNumber: String, metaPhoneNumberId: String): ChatEntity {
        logger.info("Buscando chat para o usuário $userPhoneNumber e metaPhoneNumberId $metaPhoneNumberId")
        
        // Primeiro, tenta encontrar um chat existente pelo telefone do usuário
        val existingChat = chatEntityRepository.findByUserPhoneNumber(userPhoneNumber)
        
        if (existingChat.isPresent) {
            val chat = existingChat.get()
            logger.info("Chat encontrado com ID ${chat.id} e UUID ${chat.uuid}")
            
            // Se o chat já existe, mas tem um metaPhoneNumberId diferente, atualiza
            if (chat.metaPhoneNumberId != metaPhoneNumberId) {
                logger.info("Atualizando metaPhoneNumberId do chat ${chat.id} de ${chat.metaPhoneNumberId} para $metaPhoneNumberId")
                // Como o metaPhoneNumberId é um campo imutável, precisamos criar um novo chat
                return createNewChat(userPhoneNumber, metaPhoneNumberId)
            }
            
            return chat
        }
        
        // Se não encontrou, cria um novo chat
        return createNewChat(userPhoneNumber, metaPhoneNumberId)
    }
    
    private fun createNewChat(userPhoneNumber: String, metaPhoneNumberId: String): ChatEntity {
        logger.info("Criando novo chat para o usuário $userPhoneNumber e metaPhoneNumberId $metaPhoneNumberId")
        
        val newChat = ChatEntity(
            userPhoneNumber = userPhoneNumber,
            metaPhoneNumberId = metaPhoneNumberId
        )
        
        val savedChat = chatEntityRepository.save(newChat)
        logger.info("Novo chat criado com ID ${savedChat.id} e UUID ${savedChat.uuid}")
        
        return savedChat
    }

    /**
     * Mapeia ChatEntity para Chat.
     */
    private fun mapToChat(entity: ChatEntity): Chat {
        return Chat(
            id = entity.id,
            uuid = entity.uuid,
            userPhoneNumber = entity.userPhoneNumber,
            conversationalWindowStartedAt = entity.conversationalWindowStartedAt,
            metaPhoneNumberId = entity.metaPhoneNumberId,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
} 