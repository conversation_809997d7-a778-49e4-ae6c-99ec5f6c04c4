package br.com.mecontaai.api.accounting.presentation.mapper

import br.com.mecontaai.api.accounting.application.dto.CreateNfseDto
import br.com.mecontaai.api.accounting.application.dto.NfseDto
import br.com.mecontaai.api.accounting.presentation.dto.NfseRequestDto
import br.com.mecontaai.api.accounting.presentation.dto.NfseResponseDto
import org.springframework.stereotype.Component
import java.time.LocalDateTime

/**
 * Mapper for converting between presentation and application DTOs for NFSe.
 */
@Component
class NfseMapper {

    /**
     * Converts from presentation request DTO to application DTO.
     *
     * @param requestDto The presentation request DTO
     * @return The application DTO
     */
    fun toCreateNfseDto(requestDto: NfseRequestDto): CreateNfseDto {
        return CreateNfseDto(
            customerId = requestDto.customerId,
            valorServico = requestDto.valorServico,
            discriminacao = requestDto.discriminacao,
            dataEmissao = requestDto.dataEmissao ?: LocalDateTime.now(),
            itemListaServico = requestDto.itemListaServico,
            issRetido = requestDto.issRetido,
            aliquota = requestDto.aliquota,
            codigoTributarioMunicipio = requestDto.codigoTributarioMunicipio
        )
    }

    /**
     * Converts from application DTO to presentation response DTO.
     * Mapeamento 1:1 direto - todos os campos do NfseDto.
     *
     * @param nfseDto The application DTO
     * @return The presentation response DTO
     */
    fun toResponseDto(nfseDto: NfseDto): NfseResponseDto {
        return NfseResponseDto(
            id = nfseDto.id,
            uuid = nfseDto.uuid,
            companyId = nfseDto.companyId,
            customerId = nfseDto.customerId,
            description = nfseDto.description,
            amount = nfseDto.amount,
            status = nfseDto.status,
            externalProcessId = nfseDto.externalProcessId,
            createdAt = nfseDto.createdAt,
            updatedAt = nfseDto.updatedAt
        )
    }
}