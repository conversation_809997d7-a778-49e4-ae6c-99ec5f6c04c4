package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.domain.dto.CertificateValidationResultDto
import java.io.File

/**
 * Use case para validação de certificado digital.
 */
interface ValidateCertificateUseCase {
    /**
     * Valida um certificado digital.
     * 
     * Validações:
     * - Arquivo é um certificado válido
     * - Senha está correta
     * - Certificado não está expirado
     * - CNPJ do certificado corresponde à empresa
     *
     * @param certificado Arquivo do certificado (.pfx)
     * @param senha Senha do certificado
     * @param cnpjEmpresa CNPJ da empresa para validação
     * @return Resultado da validação
     */
    fun execute(certificado: File, senha: String, cnpjEmpresa: String): CertificateValidationResultDto
} 