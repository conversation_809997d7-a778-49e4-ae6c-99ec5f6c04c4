package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.domain.enum.EntityType
import br.com.mecontaai.api.accounting.infrastructure.database.model.AddressEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

/**
 * JPA repository for AddressEntity.
 */
@Repository
interface AddressEntityRepository : JpaRepository<AddressEntity, Long> {
    /**
     * Find an address by UUID.
     *
     * @param uuid The address UUID
     * @return The address if found, wrapped in Optional
     */
    fun findByUuid(uuid: UUID): Optional<AddressEntity>

    /**
     * Find addresses by entity type and ID.
     *
     * @param entityType The entity type (e.g. 'user', 'company', 'customer')
     * @param entityId The entity ID
     * @return List of addresses for the entity
     */
    fun findByEntityTypeAndEntityId(entityType: EntityType, entityId: Long): List<AddressEntity>

    /**
     * Find primary address for entity.
     *
     * @param entityType The entity type (e.g. 'user', 'company', 'customer')
     * @param entityId The entity ID
     * @return The primary address if found, wrapped in Optional
     */
    @Query("SELECT a FROM AddressEntity a WHERE a.entityType = :entityType AND a.entityId = :entityId AND a.isPrimary = true")
    fun findPrimaryAddress(
        @Param("entityType") entityType: EntityType,
        @Param("entityId") entityId: Long
    ): Optional<AddressEntity>
} 