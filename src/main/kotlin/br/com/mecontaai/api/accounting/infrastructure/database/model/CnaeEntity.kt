package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*

/**
 * Entity class representing a CNAE in the database.
 */
@Entity
@Table(name = "cnaes")
class CnaeEntity(
    @Column(nullable = false, unique = true)
    val code: String,

    @Column(nullable = false)
    val description: String,

    @Column(name = "simples_nacional_annexo")
    val simplesNacionalAnnexo: Int? = null
) : BaseEntity()