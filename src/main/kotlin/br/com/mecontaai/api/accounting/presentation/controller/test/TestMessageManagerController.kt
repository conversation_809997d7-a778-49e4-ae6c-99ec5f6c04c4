package br.com.mecontaai.api.accounting.presentation.controller.test

import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.service.MessageManagerService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.presentation.dto.request.test.TestSendAgentResponsesRequest
import br.com.mecontaai.api.accounting.presentation.dto.response.test.TestSendAgentResponsesResponse
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * Test controller for MessageManagerService.sendAgentResponses method.
 */
@RestController
@RequestMapping("/api/v1/test/message-manager")
class TestMessageManagerController(
    private val messageManagerService: MessageManagerService,
) {
    private val logger = LoggerFactory.getLogger(TestMessageManagerController::class.java)

    /**
     * Test sending multiple agent responses.
     *
     * @param request Test request containing chat info and list of AgentResponse objects
     * @return Response with list of external message IDs
     */
    @PostMapping("/send-agent-responses")
    fun testSendAgentResponses(
        @Valid @RequestBody request: TestSendAgentResponsesRequest,
    ): ResponseEntity<TestSendAgentResponsesResponse> {
        logger.info("Testing sendAgentResponses for phone: ${request.userPhoneNumber} with ${request.agentResponses.size} responses")

        val chat = ChatEntity(
            userPhoneNumber = request.userPhoneNumber,
            metaPhoneNumberId = request.metaPhoneNumberId
        )

        val externalMessageIds = messageManagerService.sendAgentResponses(chat, request.agentResponses)

        return ResponseEntity.ok(
            TestSendAgentResponsesResponse(
                success = true,
                externalMessageIds = externalMessageIds,
                totalSent = externalMessageIds.size,
                totalRequested = request.agentResponses.size,
                message = "Agent responses sent successfully"
            )
        )
    }
}
