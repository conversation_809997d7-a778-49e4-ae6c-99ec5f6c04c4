package br.com.mecontaai.api.accounting.presentation.controller.test

import br.com.mecontaai.api.accounting.domain.model.response.AgentButton
import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.model.response.ButtonType
import br.com.mecontaai.api.accounting.domain.model.response.MediaType
import br.com.mecontaai.api.accounting.domain.service.MessageManagerService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.presentation.dto.request.test.TestAgentResponseRequest
import br.com.mecontaai.api.accounting.presentation.dto.response.test.TestMessageManagerResponse
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * Test controller for MessageManagerService.sendAgentResponse method.
 * Provides endpoints to test different types of AgentResponse objects.
 */
@RestController
@RequestMapping("/api/v1/test/message-manager")
class TestMessageManagerController(
    private val messageManagerService: MessageManagerService,
) {
    private val logger = LoggerFactory.getLogger(TestMessageManagerController::class.java)

    /**
     * Test sending a text response.
     *
     * @param request Test request containing chat info and text content
     * @return Response with external message ID
     */
    @PostMapping("/text")
    fun testTextResponse(
        @Valid @RequestBody request: TestAgentResponseRequest.TextRequest,
    ): ResponseEntity<TestMessageManagerResponse> {
        logger.info("Testing TextResponse for phone: ${request.userPhoneNumber}")

        val chat = createTestChat(request.userPhoneNumber, request.metaPhoneNumberId)
        val agentResponse = AgentResponse.TextResponse(
            content = request.content,
            manual = request.manual ?: true
        )

        val externalMessageId = messageManagerService.sendAgentResponse(chat, agentResponse)

        return ResponseEntity.ok(
            TestMessageManagerResponse(
                success = true,
                externalMessageId = externalMessageId,
                responseType = "TextResponse",
                message = "Text message sent successfully"
            )
        )
    }

    /**
     * Test sending a button response.
     *
     * @param request Test request containing chat info and button data
     * @return Response with external message ID
     */
    @PostMapping("/button")
    fun testButtonResponse(
        @Valid @RequestBody request: TestAgentResponseRequest.ButtonRequest,
    ): ResponseEntity<TestMessageManagerResponse> {
        logger.info("Testing ButtonResponse for phone: ${request.userPhoneNumber}")

        val chat = createTestChat(request.userPhoneNumber, request.metaPhoneNumberId)
        val buttons = request.buttons.map { buttonDto ->
            AgentButton(
                id = buttonDto.id,
                title = buttonDto.title,
                type = ButtonType.valueOf(buttonDto.type ?: "REPLY"),
                payload = buttonDto.payload,
                url = buttonDto.url
            )
        }

        val agentResponse = AgentResponse.ButtonResponse(
            message = request.message,
            buttons = buttons
        )

        val externalMessageId = messageManagerService.sendAgentResponse(chat, agentResponse)

        return ResponseEntity.ok(
            TestMessageManagerResponse(
                success = true,
                externalMessageId = externalMessageId,
                responseType = "ButtonResponse",
                message = "Button message sent successfully"
            )
        )
    }

    /**
     * Test sending a media response.
     *
     * @param request Test request containing chat info and media data
     * @return Response with external message ID
     */
    @PostMapping("/media")
    fun testMediaResponse(
        @Valid @RequestBody request: TestAgentResponseRequest.MediaRequest,
    ): ResponseEntity<TestMessageManagerResponse> {
        logger.info("Testing MediaResponse for phone: ${request.userPhoneNumber}")

        val chat = createTestChat(request.userPhoneNumber, request.metaPhoneNumberId)
        val agentResponse = AgentResponse.MediaResponse(
            mediaType = MediaType.valueOf(request.mediaType),
            url = request.url,
            caption = request.caption,
            filename = request.filename
        )

        val externalMessageId = messageManagerService.sendAgentResponse(chat, agentResponse)

        return ResponseEntity.ok(
            TestMessageManagerResponse(
                success = true,
                externalMessageId = externalMessageId,
                responseType = "MediaResponse",
                message = "Media message sent successfully"
            )
        )
    }

    /**
     * Test sending a composite response.
     *
     * @param request Test request containing chat info and multiple responses
     * @return Response with external message ID of the first response
     */
    @PostMapping("/composite")
    fun testCompositeResponse(
        @Valid @RequestBody request: TestAgentResponseRequest.CompositeRequest,
    ): ResponseEntity<TestMessageManagerResponse> {
        logger.info("Testing CompositeResponse for phone: ${request.userPhoneNumber}")

        val chat = createTestChat(request.userPhoneNumber, request.metaPhoneNumberId)
        
        // Create sub-responses based on request
        val responses = mutableListOf<AgentResponse>()
        
        request.textResponses?.forEach { textDto ->
            responses.add(AgentResponse.TextResponse(
                content = textDto.content,
                manual = textDto.manual ?: true
            ))
        }
        
        request.buttonResponses?.forEach { buttonDto ->
            val buttons = buttonDto.buttons.map { btn ->
                AgentButton(
                    id = btn.id,
                    title = btn.title,
                    type = ButtonType.valueOf(btn.type ?: "REPLY"),
                    payload = btn.payload,
                    url = btn.url
                )
            }
            responses.add(AgentResponse.ButtonResponse(
                message = buttonDto.message,
                buttons = buttons
            ))
        }

        val agentResponse = AgentResponse.CompositeResponse(responses = responses)
        val externalMessageId = messageManagerService.sendAgentResponse(chat, agentResponse)

        return ResponseEntity.ok(
            TestMessageManagerResponse(
                success = true,
                externalMessageId = externalMessageId,
                responseType = "CompositeResponse",
                message = "Composite message sent successfully"
            )
        )
    }

    /**
     * Test sending multiple agent responses in sequence.
     *
     * @param request Test request containing chat info and multiple responses
     * @return Response with list of external message IDs
     */
    @PostMapping("/multiple")
    fun testMultipleResponses(
        @Valid @RequestBody request: TestAgentResponseRequest.MultipleRequest,
    ): ResponseEntity<TestMessageManagerResponse.MultipleResponse> {
        logger.info("Testing multiple AgentResponses for phone: ${request.userPhoneNumber}")

        val chat = createTestChat(request.userPhoneNumber, request.metaPhoneNumberId)
        
        // Create responses from request
        val agentResponses = mutableListOf<AgentResponse>()
        
        request.responses.forEach { responseDto ->
            when (responseDto.type) {
                "text" -> {
                    agentResponses.add(AgentResponse.TextResponse(
                        content = responseDto.content ?: "Default text",
                        manual = true
                    ))
                }
                "button" -> {
                    val buttons = responseDto.buttons?.map { btn ->
                        AgentButton(
                            id = btn.id,
                            title = btn.title,
                            type = ButtonType.valueOf(btn.type ?: "REPLY"),
                            payload = btn.payload,
                            url = btn.url
                        )
                    } ?: emptyList()
                    
                    agentResponses.add(AgentResponse.ButtonResponse(
                        message = responseDto.content ?: "Choose an option:",
                        buttons = buttons
                    ))
                }
                "media" -> {
                    agentResponses.add(AgentResponse.MediaResponse(
                        mediaType = MediaType.valueOf(responseDto.mediaType ?: "IMAGE"),
                        url = responseDto.url ?: "https://example.com/image.jpg",
                        caption = responseDto.caption,
                        filename = responseDto.filename
                    ))
                }
            }
        }

        val externalMessageIds = messageManagerService.sendAgentResponses(chat, agentResponses)

        return ResponseEntity.ok(
            TestMessageManagerResponse.MultipleResponse(
                success = true,
                externalMessageIds = externalMessageIds,
                totalSent = externalMessageIds.size,
                totalRequested = agentResponses.size,
                message = "Multiple messages sent successfully"
            )
        )
    }

    /**
     * Creates a test ChatEntity with the provided phone numbers.
     */
    private fun createTestChat(userPhoneNumber: String, metaPhoneNumberId: String): ChatEntity {
        return ChatEntity(
            userPhoneNumber = userPhoneNumber,
            metaPhoneNumberId = metaPhoneNumberId
        )
    }
}
