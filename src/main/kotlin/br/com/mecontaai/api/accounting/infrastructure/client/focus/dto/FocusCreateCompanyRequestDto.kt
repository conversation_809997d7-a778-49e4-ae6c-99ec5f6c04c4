package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonInclude

@JsonInclude(JsonInclude.Include.NON_NULL)
data class FocusCreateCompanyRequestDto(
    val cnpj: String,
    val nome: String,
    val nome_fantasia: String,
    val logradouro: String,
    val numero: Long,
    val complemento: String,
    val bairro: String,
    val codigo_municipio: String? = null,
    val municipio: String,
    val uf: String,
    val cep: String,
    val codigo_pais: String = "1058",
    val pais: String = "BRASIL",
    val telefone: String,
    val email: String,
    val regime_tributario: Long,
    val cnae: String? = null,
    val inscricao_municipal: String? = null,
    val inscricao_estadual: String? = null,
    val inscricao_suframa: String? = null,
    val optante_simples_nacional: Boolean? = null,
    val discrimina_impostos: Boolean,
    val enviar_email_destinatario: Boolean,
    val enviar_email_homologacao: Boolean? = null,
    val habilita_nfce: Boolean? = null,
    val habilita_nfe: Boolean? = null,
    val habilita_nfse: Boolean,
    val habilita_cte: Boolean? = null,
    val habilita_mdfe: Boolean? = null,
    val habilita_manifestacao: Boolean? = null,
    val certificado_valido_de: String? = null,
    val certificado_valido_ate: String? = null,
    val certificado_cnpj: String? = null,
    val certificado_especifico: Boolean? = null,
    val orientacao_danfe: String? = null,
    val recibo_danfe: Boolean? = null,
    val exibe_sempre_ipi_danfe: Boolean? = null,
    val exibe_issqn_danfe: Boolean? = null,
    val exibe_impostos_adicionais_danfe: Boolean? = null,
    val exibe_fatura_danfe: Boolean? = null,
    val exibe_unidade_tributaria_danfe: Boolean? = null,
    val exibe_desconto_itens: Boolean? = null,
    val exibe_sempre_volumes_danfe: Boolean? = null,
    val exibe_composicao_carga_mdfe: Boolean? = null,
    val habilita_csrt_nfe: Boolean? = null,
    val nfe_sincrono: Boolean? = null,
    val nfe_sincrono_homologacao: Boolean? = null,
    val mdfe_sincrono: Boolean? = null,
    val mdfe_sincrono_homologacao: Boolean? = null
)