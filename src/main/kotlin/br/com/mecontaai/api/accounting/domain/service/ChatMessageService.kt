package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.model.ChatMessageEvent
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatMessageEntity

interface ChatMessageService {
    /**
     * Salva uma mensagem de chat no banco de dados.
     *
     * @param event O evento de mensagem a ser salvo
     * @param chat O chat ao qual a mensagem pertence
     * @return A entidade de mensagem salva
     */
    fun saveMessage(event: ChatMessageEvent, chat: ChatEntity): ChatMessageEntity
} 