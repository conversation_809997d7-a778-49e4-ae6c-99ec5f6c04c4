package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "button_callbacks")
data class ButtonCallbackEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,

    @Column(name = "callback_id", unique = true, nullable = false)
    val callbackId: String,

    @Column(name = "chat_uuid", nullable = false)
    val chatUuid: UUID,

    @Column(name = "tool_name", nullable = false)
    val toolName: String,

    @Column(name = "action_type", nullable = false)
    val actionType: String,

    @Column(name = "context_data", columnDefinition = "TEXT")
    val contextData: String,

    @Column(name = "expires_at", nullable = false)
    val expiresAt: LocalDateTime,

    @Column(name = "external_message_id")
    val externalMessageId: String? = null,

    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    val status: ButtonCallbackStatus = ButtonCallbackStatus.PENDING,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "executed_at")
    val executedAt: LocalDateTime? = null
)

enum class ButtonCallbackStatus {
    PENDING,
    EXECUTED,
    EXPIRED,
    CANCELLED
}
