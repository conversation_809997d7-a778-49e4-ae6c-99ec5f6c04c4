package br.com.mecontaai.api.accounting.domain.service.step

import br.com.mecontaai.api.accounting.domain.model.ChatMessageEvent
import br.com.mecontaai.api.accounting.domain.service.ChatService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Step responsible for resolving or creating the chat entity for a message event.
 * This step handles the chat lookup/creation logic.
 */
@Component
class ChatResolutionStep(
    private val chatService: ChatService,
) {
    private val logger = LoggerFactory.getLogger(ChatResolutionStep::class.java)

    /**
     * Resolves the chat entity for the given message event.
     * Creates a new chat if one doesn't exist.
     *
     * @param event The chat message event
     * @return The resolved or created chat entity
     */
    fun execute(event: ChatMessageEvent): ChatEntity {
        logger.debug("Resolving chat for message: ${event.externalMessageId}")

        val chat =
            chatService.findOrCreateChat(
                userPhoneNumber =
                    event.messageAuthorPhoneNumber
                        ?: throw IllegalStateException("Número de telefone do autor não pode ser nulo"),
                metaPhoneNumberId =
                    event.metaPhoneNumberId
                        ?: throw IllegalStateException("metaPhoneNumberId não pode ser nulo"),
            )

        logger.info("Chat found/created: ${chat.uuid}")
        return chat
    }
}
