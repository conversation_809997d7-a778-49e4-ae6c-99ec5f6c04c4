package br.com.mecontaai.api.accounting.application.dto

import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.CnpjConsultaResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto

/**
 * DTO containing full company data from multiple sources.
 * 
 * This composite object contains the raw responses from both Focus API and ReceitaWS API
 * without any field-by-field mapping, allowing consumers to access the specific data
 * they need from each source.
 */
data class FullCompanyDataDto(
    val cnpj: String,
    /**
     * Company data from Focus API
     */
    val focusData: CnpjConsultaResponseDto,
    
    /**
     * Company data from ReceitaWS API
     */
    val receitaWsData: ReceitaWsResponseDto
)
