package br.com.mecontaai.api.accounting.infrastructure.service

import br.com.mecontaai.api.accounting.domain.dto.BlobData
import br.com.mecontaai.api.accounting.domain.dto.BlobMetadata
import br.com.mecontaai.api.accounting.domain.enum.ContentCategory
import br.com.mecontaai.api.accounting.domain.enum.BlobEntityType
import br.com.mecontaai.api.accounting.domain.enum.StorageType
import br.com.mecontaai.api.accounting.domain.repository.BlobRepository
import br.com.mecontaai.api.accounting.domain.service.BlobService
import br.com.mecontaai.api.accounting.infrastructure.database.model.BlobEntity
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.HeadObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest
import java.time.Duration
import java.time.LocalDateTime
import java.util.UUID

/**
 * Implementação do BlobService usando Amazon S3
 */
@Service
class S3BlobServiceImpl(
    private val blobRepository: BlobRepository,
    private val s3Client: S3Client,
    private val s3Presigner: S3Presigner,
    @Qualifier("s3BucketName") private val bucketName: String,
    private val objectMapper: ObjectMapper
) : BlobService {

    private val logger = LoggerFactory.getLogger(javaClass)

    /**
     * Salva um blob no S3 e registra seus metadados no banco de dados
     */
    @Transactional
    override fun save(blob: ByteArray, metadata: BlobMetadata): UUID {
        logger.info("Salvando blob de tamanho ${blob.size} bytes no S3")
        
        // Cria a entidade para salvar no banco
        val blobEntity = BlobEntity().apply {
            initialize(
                contentType = metadata.contentType,
                contentCategory = metadata.contentCategory,
                filename = metadata.filename,
                size = blob.size.toLong(),
                entityType = metadata.entityType,
                entityId = metadata.entityId,
                storageType = StorageType.S3,
                storagePath = "" // Será atualizado após salvar no S3
            )
        }
        
        // Salva inicialmente para obter UUID
        blobRepository.save(blobEntity)
        
        // Define o caminho no S3 com o UUID gerado
        val s3Key = buildS3Key(blobEntity.uuid, metadata.contentCategory, metadata.filename)
        blobEntity.storagePath = s3Key
        
        // Atualiza com o caminho correto
        blobRepository.save(blobEntity)
        
        // Fazer upload para o S3
        try {
            val putObjectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(s3Key)
                .contentType(metadata.contentType)
                .build()
            
            s3Client.putObject(putObjectRequest, RequestBody.fromBytes(blob))
            logger.info("Blob salvo com sucesso no S3 com a chave: $s3Key")
            
            return blobEntity.uuid
        } catch (e: Exception) {
            logger.error("Erro ao salvar blob no S3: ${e.message}", e)
            throw RuntimeException("Falha ao salvar blob no S3", e)
        }
    }

    /**
     * Recupera um blob do S3 pelo UUID
     */
    override fun get(uuid: UUID): BlobData? {
        logger.info("Buscando blob com UUID: $uuid")
        
        // Busca os metadados no banco
        val blobEntity = blobRepository.findByUuidAndIsActive(uuid) ?: return null
        
        // Atualiza a data de último acesso
        blobEntity.lastAccessedAt = LocalDateTime.now()
        blobRepository.save(blobEntity)
        
        // Busca o conteúdo no S3
        try {
            val getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(blobEntity.storagePath)
                .build()
            
            val s3Object = s3Client.getObject(getObjectRequest)
            val content = s3Object.readAllBytes()
            logger.info("Blob recuperado com sucesso do S3, tamanho: ${content.size} bytes")
            
            return BlobData(
                content = content,
                metadata = mapEntityToMetadata(blobEntity)
            )
        } catch (e: Exception) {
            logger.error("Erro ao recuperar blob do S3: ${e.message}", e)
            return null
        }
    }

    /**
     * Recupera apenas os metadados de um blob
     */
    override fun getMetadata(uuid: UUID): BlobMetadata? {
        logger.info("Buscando metadados do blob com UUID: $uuid")
        
        val blobEntity = blobRepository.findByUuidAndIsActive(uuid) ?: return null
        return mapEntityToMetadata(blobEntity)
    }

    /**
     * Marca um blob como inativo e remove do S3
     */
    @Transactional
    override fun delete(uuid: UUID): Boolean {
        logger.info("Deletando blob com UUID: $uuid")
        
        val blobEntity = blobRepository.findByUuidAndIsActive(uuid) ?: return false
        
        // Marca como inativo no banco
        blobEntity.isActive = false
        blobRepository.save(blobEntity)
        
        // Remove do S3
        try {
            val deleteObjectRequest = DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key(blobEntity.storagePath)
                .build()
            
            s3Client.deleteObject(deleteObjectRequest)
            logger.info("Blob removido com sucesso do S3")
            
            return true
        } catch (e: Exception) {
            logger.error("Erro ao remover blob do S3: ${e.message}", e)
            // Continua com a deleção lógica mesmo se falhar a remoção física
            return true
        }
    }

    /**
     * Verifica se um blob existe no S3
     */
    override fun exists(uuid: UUID): Boolean {
        logger.info("Verificando existência do blob com UUID: $uuid")
        
        // Verifica primeiro no banco de dados
        if (!blobRepository.existsByUuidAndIsActive(uuid)) {
            return false
        }
        
        // Busca os metadados para obter o caminho
        val blobEntity = blobRepository.findByUuidAndIsActive(uuid) ?: return false
        
        // Verifica se existe no S3
        try {
            val headObjectRequest = HeadObjectRequest.builder()
                .bucket(bucketName)
                .key(blobEntity.storagePath)
                .build()
            
            s3Client.headObject(headObjectRequest)
            return true
        } catch (e: Exception) {
            logger.error("Erro ao verificar existência do blob no S3: ${e.message}")
            return false
        }
    }

    /**
     * Gera uma URL pré-assinada para acesso temporário ao blob
     */
    override fun generatePublicUrl(uuid: UUID, expirationMinutes: Int): String? {
        logger.info("Gerando URL pública para blob com UUID: {}, expiração: {} minutos", uuid, expirationMinutes)
        
        // Verifica se o blob existe
        val blobEntity = blobRepository.findByUuidAndIsActive(uuid) ?: return null
        
        try {
            val getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(blobEntity.storagePath)
                .build()
            
            val presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(expirationMinutes.toLong()))
                .getObjectRequest(getObjectRequest)
                .build()
            
            val presignedGetObjectRequest = s3Presigner.presignGetObject(presignRequest)
            return presignedGetObjectRequest.url().toString()
        } catch (e: Exception) {
            logger.error("Erro ao gerar URL pré-assinada: ${e.message}", e)
            return null
        }
    }

    /**
     * Busca blobs por entidade relacionada
     */
    override fun findByEntity(entityType: BlobEntityType, entityId: String): List<BlobMetadata> {
        logger.info("Buscando blobs relacionados à entidade: $entityType, ID: $entityId")
        
        val blobEntities = blobRepository.findByEntityTypeAndEntityIdAndIsActive(
            entityType, entityId
        )
        
        return blobEntities.map { mapEntityToMetadata(it) }
    }
    
    /**
     * Constrói a chave para o objeto no S3
     */
    private fun buildS3Key(uuid: UUID, contentCategory: ContentCategory, filename: String?): String {
        val extension = filename?.let { 
            if (it.contains('.')) {
                ".${it.substringAfterLast('.')}"
            } else {
                ""
            }
        } ?: ""
        
        return "${contentCategory.name.lowercase()}/${uuid}$extension"
    }
    
    /**
     * Converte a entidade em um objeto de metadados
     */
    private fun mapEntityToMetadata(entity: BlobEntity): BlobMetadata {
        return BlobMetadata(
            uuid = entity.uuid,
            contentType = entity.contentType,
            contentCategory = entity.contentCategory,
            filename = entity.filename,
            size = entity.size,
            entityType = entity.entityType,
            entityId = entity.entityId,
            storageType = entity.storageType
        )
    }
} 