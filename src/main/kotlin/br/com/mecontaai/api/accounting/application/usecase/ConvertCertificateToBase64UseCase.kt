package br.com.mecontaai.api.accounting.application.usecase

import java.io.File

/**
 * Use case para conversão de certificado para base64.
 */
interface ConvertCertificateToBase64UseCase {
    /**
     * Converte um arquivo de certificado (.pfx) para formato base64.
     * 
     * @param certificado Arquivo do certificado
     * @return Certificado convertido em base64
     * @throws IllegalArgumentException se o arquivo for inválido
     */
    fun execute(certificado: File): String
} 