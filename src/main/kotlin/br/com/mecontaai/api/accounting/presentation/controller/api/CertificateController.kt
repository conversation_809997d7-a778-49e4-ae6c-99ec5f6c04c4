package br.com.mecontaai.api.accounting.presentation.controller.api

import br.com.mecontaai.api.accounting.application.usecase.ProcessCertificateOnboardingUseCase
import br.com.mecontaai.api.accounting.domain.service.CertificateService
import br.com.mecontaai.api.accounting.presentation.dto.CertificateResponseDto
import br.com.mecontaai.api.accounting.presentation.dto.OnboardingResponseDto
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.server.ResponseStatusException

/**
 * Controller para endpoints de certificado digital.
 */
@RestController
@RequestMapping("/api/v1/certificates")
class CertificateController(
    private val processCertificateOnboardingUseCase: ProcessCertificateOnboardingUseCase,
    private val certificateService: CertificateService
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    /**
     * Endpoint para upload e processamento de certificado digital.
     *
     * @param file Arquivo do certificado (.pfx ou .p12)
     * @param companyId ID da empresa
     * @param password Senha do certificado
     * @return Resultado do processo de onboarding
     */
    @PostMapping("/upload", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun uploadCertificate(
        @RequestParam("file") file: MultipartFile,
        @RequestParam("companyId") companyId: Long,
        @RequestParam("password") password: String
    ): ResponseEntity<OnboardingResponseDto> {
        logger.info("Recebendo upload de certificado para empresa $companyId")
        
        try {
            val result = processCertificateOnboardingUseCase.execute(
                empresaId = companyId,
                certificado = file,
                senha = password
            )
            
            // Buscar dados do certificado se foi criado com sucesso
            val certificateResponse = if (result.success && result.certificateId != null) {
                certificateService.findById(result.certificateId)?.let { certificate ->
                    CertificateResponseDto(
                        id = certificate.id!!,
                        uuid = certificate.uuid,
                        companyId = certificate.companyId,
                        expirationDate = certificate.expirationDate,
                        isActive = certificate.isActive,
                        isValid = certificate.isValid(),
                        isExpiringSoon = certificate.isExpiringSoon(),
                        createdAt = certificate.createdAt,
                        updatedAt = certificate.updatedAt
                    )
                }
            } else null
            
            val response = OnboardingResponseDto(
                success = result.success,
                status = result.status.name,
                message = result.message,
                certificateId = result.certificateId,
                certificate = certificateResponse
            )
            
            val httpStatus = if (result.success) HttpStatus.CREATED else HttpStatus.BAD_REQUEST
            return ResponseEntity.status(httpStatus).body(response)
            
        } catch (e: Exception) {
            logger.error("Erro ao processar upload de certificado", e)
            throw ResponseStatusException(
                HttpStatus.INTERNAL_SERVER_ERROR, 
                "Erro interno ao processar certificado: ${e.message}"
            )
        }
    }
    
    /**
     * Endpoint para buscar certificado ativo de uma empresa.
     *
     * @param companyId ID da empresa
     * @return Certificado ativo da empresa
     */
    @GetMapping("/company/{companyId}/active")
    fun getActiveCertificate(@PathVariable companyId: Long): ResponseEntity<CertificateResponseDto> {
        try {
            val certificate = certificateService.findActiveByCompanyId(companyId)
                ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "Certificado ativo não encontrado para a empresa")
            
            val response = CertificateResponseDto(
                id = certificate.id!!,
                uuid = certificate.uuid,
                companyId = certificate.companyId,
                expirationDate = certificate.expirationDate,
                isActive = certificate.isActive,
                isValid = certificate.isValid(),
                isExpiringSoon = certificate.isExpiringSoon(),
                createdAt = certificate.createdAt,
                updatedAt = certificate.updatedAt
            )
            
            return ResponseEntity.ok(response)
            
        } catch (e: ResponseStatusException) {
            throw e
        } catch (e: Exception) {
            logger.error("Erro ao buscar certificado ativo da empresa $companyId", e)
            throw ResponseStatusException(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "Erro interno ao buscar certificado: ${e.message}"
            )
        }
    }
    
    /**
     * Endpoint para listar todos os certificados de uma empresa.
     *
     * @param companyId ID da empresa
     * @return Lista de certificados da empresa
     */
    @GetMapping("/company/{companyId}")
    fun getCertificatesByCompany(@PathVariable companyId: Long): ResponseEntity<List<CertificateResponseDto>> {
        try {
            val certificates = certificateService.findByCompanyId(companyId)
            
            val response = certificates.map { certificate ->
                CertificateResponseDto(
                    id = certificate.id!!,
                    uuid = certificate.uuid,
                    companyId = certificate.companyId,
                    expirationDate = certificate.expirationDate,
                    isActive = certificate.isActive,
                    isValid = certificate.isValid(),
                    isExpiringSoon = certificate.isExpiringSoon(),
                    createdAt = certificate.createdAt,
                    updatedAt = certificate.updatedAt
                )
            }
            
            return ResponseEntity.ok(response)
            
        } catch (e: Exception) {
            logger.error("Erro ao buscar certificados da empresa $companyId", e)
            throw ResponseStatusException(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "Erro interno ao buscar certificados: ${e.message}"
            )
        }
    }
} 