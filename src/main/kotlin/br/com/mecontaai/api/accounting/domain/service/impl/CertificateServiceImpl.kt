package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.entity.Certificate
import br.com.mecontaai.api.accounting.domain.repository.CertificateRepository
import br.com.mecontaai.api.accounting.domain.service.BlobService
import br.com.mecontaai.api.accounting.domain.service.CertificateService
import br.com.mecontaai.api.accounting.domain.dto.BlobMetadata
import br.com.mecontaai.api.accounting.domain.enum.BlobEntityType
import br.com.mecontaai.api.accounting.domain.enum.ContentCategory
import br.com.mecontaai.api.accounting.domain.enum.StorageType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * Implementation of the CertificateService interface.
 */
@Service
class CertificateServiceImpl(
    private val certificateRepository: CertificateRepository,
    private val blobService: BlobService
) : CertificateService {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun createCertificate(certificate: Certificate, file: ByteArray, filename: String): Certificate {
        logger.info("Creating certificate for company ${certificate.companyId}")
        
        validateCertificate(certificate)

        // Desativar outros certificados da mesma empresa
        logger.info("Desativando outros certificados da empresa ${certificate.companyId}")
        val existingCertificates = certificateRepository.findByCompanyId(certificate.companyId)
        existingCertificates.forEach { existingCert ->
            if (existingCert.isActive) {
                logger.info("Desativando certificado ${existingCert.id} da empresa ${certificate.companyId}")
                certificateRepository.save(existingCert.copy(isActive = false))
            }
        }

        logger.info("Saving certificate file to blob storage...")
        val blobMetadata = BlobMetadata(
            contentType = "application/x-pkcs12",
            contentCategory = ContentCategory.BINARY,
            filename = filename,
            size = file.size.toLong(),
            entityType = BlobEntityType.CERTIFICATE,
            storageType = StorageType.S3
        )
        val blobUuid = blobService.save(file, blobMetadata)

        val certificateWithBlob = certificate.copy(blobUuid = blobUuid.toString(), isActive = true)
        logger.info("Saving certificate to database...")
        return certificateRepository.save(certificateWithBlob)
    }

    override fun findById(id: Long): Certificate? {
        return certificateRepository.findById(id)
    }

    override fun findByUuid(uuid: UUID): Certificate? {
        return certificateRepository.findByUuid(uuid)
    }

    override fun findActiveByCompanyId(companyId: Long): Certificate? {
        return certificateRepository.findActiveByCompanyId(companyId)
    }

    override fun findByCompanyId(companyId: Long): List<Certificate> {
        return certificateRepository.findByCompanyId(companyId)
    }

    override fun findByBlobUuid(blobUuid: String): Certificate? {
        return certificateRepository.findByBlobUuid(blobUuid)
    }

    override fun getFile(id: Long): ByteArray {
        logger.debug("Buscando arquivo do certificado {}", id)
        
        val certificate = certificateRepository.findById(id)
            ?: throw IllegalArgumentException("Certificado não encontrado com ID: $id")
            
        val blobUuid = UUID.fromString(certificate.blobUuid)
        val blobData = blobService.get(blobUuid)
            ?: throw IllegalArgumentException("Arquivo do certificado não encontrado no blob storage")
            
        return blobData.content
    }

    @Transactional
    override fun activateCertificate(certificateId: Long, companyId: Long): Certificate? {
        logger.info("Activating certificate $certificateId for company $companyId")
        
        val certificate = certificateRepository.findById(certificateId) ?: return null
        
        if (certificate.companyId != companyId) {
            logger.warn("Certificate $certificateId does not belong to company $companyId")
            return null
        }
        
        // Deactivate all certificates for the company first
        val companyCertificates = certificateRepository.findByCompanyId(companyId)
        companyCertificates.forEach { cert ->
            if (cert.isActive) {
                certificateRepository.save(cert.copy(isActive = false))
            }
        }
        
        // Activate the specified certificate
        val activatedCertificate = certificate.copy(isActive = true)
        return certificateRepository.save(activatedCertificate)
    }

    @Transactional
    override fun deactivateCertificate(certificateId: Long): Certificate? {
        logger.info("Deactivating certificate $certificateId")
        
        val certificate = certificateRepository.findById(certificateId) ?: return null
        
        val deactivatedCertificate = certificate.copy(isActive = false)
        return certificateRepository.save(deactivatedCertificate)
    }

    override fun hasActiveCertificate(companyId: Long): Boolean {
        return certificateRepository.hasActiveCertificate(companyId)
    }
    
    @Transactional
    override fun updateExpirationDate(certificateId: Long, expirationDate: java.time.LocalDate): Certificate? {
        logger.info("Atualizando data de expiração do certificado $certificateId para $expirationDate")
        
        val certificate = certificateRepository.findById(certificateId) ?: return null
        
        val updatedCertificate = certificate.copy(expirationDate = expirationDate)
        val savedCertificate = certificateRepository.save(updatedCertificate)
        
        logger.info("Data de expiração do certificado $certificateId atualizada com sucesso para $expirationDate")
        return savedCertificate
    }
    
    /**
     * Validates certificate data.
     *
     * @param certificate The certificate to validate
     */
    private fun validateCertificate(certificate: Certificate) {
        require(certificate.companyId > 0) { "Company ID must be positive" }
        require(certificate.expirationDate.isAfter(java.time.LocalDate.now())) {
            "Certificate expiration date must be in the future" 
        }
    }
} 