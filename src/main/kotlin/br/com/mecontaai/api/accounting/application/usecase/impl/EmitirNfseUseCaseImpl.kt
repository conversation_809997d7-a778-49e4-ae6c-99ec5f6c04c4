package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.dto.EmitirNfseRequest
import br.com.mecontaai.api.accounting.application.dto.NfseDto
import br.com.mecontaai.api.accounting.application.usecase.EmitirNfseUseCase
import br.com.mecontaai.api.accounting.domain.entity.Company
import br.com.mecontaai.api.accounting.domain.entity.Nfse
import br.com.mecontaai.api.accounting.domain.repository.CompanyRepository
import br.com.mecontaai.api.accounting.domain.repository.CustomerRepository
import br.com.mecontaai.api.accounting.domain.service.MessageManagerService
import br.com.mecontaai.api.accounting.domain.service.NfseService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.LocalDateTime

/**
 * Implementação do caso de uso para emissão de NFS-e.
 */
@Component
class EmitirNfseUseCaseImpl(
    private val nfseService: NfseService,
    private val companyRepository: CompanyRepository,
    private val customerRepository: CustomerRepository,
) : EmitirNfseUseCase {
    private val logger = LoggerFactory.getLogger(MessageManagerService::class.java)

    /**
     * Executa a emissão de uma NFS-e.
     *
     * @param request dados necessários para emissão da NFS-e
     * @return dados da NFS-e emitida
     * @throws IllegalArgumentException se empresa ou cliente não for encontrado
     * @throws IllegalStateException se empresa não tiver dados necessários para emissão
     */
    override fun execute(request: EmitirNfseRequest): NfseDto {
        val company =
            companyRepository.findById(request.companyId)
                ?: throw IllegalArgumentException("Empresa com ID ${request.companyId} não encontrada")

        logger.info("Emitindo NFS-e para empresa: ${company.name} (ID: ${company.id}) token: ${company.token})")

        val customer =
            customerRepository.findById(request.customerId)
                ?: throw IllegalArgumentException("Cliente com ID ${request.customerId} não encontrado")

        validateCompanyData(company)

        // Chamar diretamente o NfseService
        val nfse =
            nfseService.createNfse(
                companyId = company.id!!,
                token = company.token!!,
                customer = customer,
                valorServico = request.valorServico,
                discriminacao = request.discriminacao,
                dataEmissao = LocalDateTime.now(),
                itemListaServico = request.itemListaServico,
                issRetido = false,
                aliquota = null,
                codigoMunicipioPrestador = company.codigoMunicipio!!,
                inscricaoMunicipalPrestador = company.municipalRegistration!!,
                cnpjPrestador = company.document,
                codigoTributarioMunicipio = null,
            )

        // Mapear para DTO
        return mapToDto(nfse)
    }

    /**
     * Valida se a empresa tem todos os dados necessários para emitir NFS-e.
     */
    private fun validateCompanyData(company: Company) {
        if (company.municipalRegistration.isNullOrBlank()) {
            throw IllegalStateException("Empresa não possui inscrição municipal cadastrada")
        }

        if (company.codigoMunicipio.isNullOrBlank()) {
            throw IllegalStateException("Empresa não possui código do município cadastrado")
        }

        if (company.token.isNullOrBlank()) {
            throw IllegalStateException("Empresa não possui token Focus NFe cadastrado")
        }

        // Aqui poderiam ser adicionadas outras validações:
        // - Verificar se tem certificado digital válido
        // - Verificar se está ativa
    }

    /**
     * Mapeia entidade Nfse para DTO.
     */
    private fun mapToDto(nfse: Nfse): NfseDto =
        NfseDto(
            id = nfse.id,
            uuid = nfse.uuid.toString(),
            companyId = nfse.companyId,
            customerId = nfse.customerId,
            description = nfse.description,
            amount = nfse.amount,
            status = nfse.status,
            externalProcessId = nfse.externalProcessId,
            createdAt = nfse.createdAt,
            updatedAt = nfse.updatedAt,
        )
}
