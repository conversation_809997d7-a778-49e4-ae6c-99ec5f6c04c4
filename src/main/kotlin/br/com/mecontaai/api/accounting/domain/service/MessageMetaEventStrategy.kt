package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.model.ChatMessageEvent
import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.service.llm.LlmService
import br.com.mecontaai.api.accounting.domain.service.llm.assistants.LlmAssistant
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.ContextChatMessage
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class MessageMetaEventStrategy(
    private val chatService: ChatService,
    private val chatMessageService: ChatMessageService,
    private val messageManagerService: MessageManagerService,
    private val contextChatMessageService: ContextChatMessageService,
    private val objectMapper: ObjectMapper,
    private val llmService: LlmService,
    private val defaultAssistant: LlmAssistant,
) : MetaEventStrategy {
    private val logger = LoggerFactory.getLogger(MessageMetaEventStrategy::class.java)

    override fun handle(event: ChatMessageEvent) {
        logger.info("Processando evento de mensagem do Meta: ${event.externalMessageId}")
        val chat = getChat(event)
        saveMessageAndContext(event, chat)
        processMessageAndSendResponse(event, chat)
    }

    private fun getChat(event: ChatMessageEvent): ChatEntity {
        val chat =
            chatService.findOrCreateChat(
                userPhoneNumber =
                    event.messageAuthorPhoneNumber
                        ?: throw IllegalStateException("Número de telefone do autor não pode ser nulo"),
                metaPhoneNumberId =
                    event.metaPhoneNumberId
                        ?: throw IllegalStateException("metaPhoneNumberId não pode ser nulo"),
            )
        logger.info("Chat encontrado/criado: ${chat.uuid}")
        return chat
    }

    private fun saveMessageAndContext(
        event: ChatMessageEvent,
        chat: ChatEntity,
    ) {
        // Salva a mensagem
        val savedMessage = chatMessageService.saveMessage(event, chat)
        logger.info("Mensagem salva com ID ${savedMessage.id} e UUID ${savedMessage.uuid}")

        // Cria o JSON com o conteúdo da mensagem
        val messageContent =
            objectMapper.createObjectNode().apply {
                put("role", "user")
                put("content", event.message ?: "[Mensagem sem texto]")
            }

        // Salva o contexto da mensagem do usuário
        val contextMessage =
            ContextChatMessage(
                chatId = chat.id!!,
                role = "user",
                content = objectMapper.writeValueAsString(messageContent),
                externalMessageId = event.externalMessageId,
            )
        contextChatMessageService.save(contextMessage)
        logger.info("Contexto da mensagem do usuário salvo com UUID ${contextMessage.uuid}")
    }

    private fun processMessageAndSendResponse(
        event: ChatMessageEvent,
        chat: ChatEntity,
    ) {
        try {
            // Processa a mensagem usando o LlmService, deixando o assistant montar o contexto
            val llmResponse =
                llmService.processUserMessage(
                    assistant = defaultAssistant,
                    chatMessageEvent = event,
                    useFallbackProvider = false,
                    useFallbackModel = false,
                )

            // Verifica se temos alguma resposta para enviar
            if (llmResponse.agentResponses.isEmpty()) {
                logger.warn("Não foi possível gerar uma resposta para a mensagem ${event.externalMessageId}")
                return
            }

            // Pega a primeira resposta como texto principal
            val responseText =
                when (val firstResponse = llmResponse.agentResponses.first()) {
                    is AgentResponse.TextResponse -> firstResponse.content
                    else -> "Não foi possível processar a resposta"
                }

            // Salva o contexto temporário de todas as mensagens
            val tempContextMessages = llmResponse.tempContextMessages

            tempContextMessages.forEach { tempChatMessage ->
                try {
                    val chatMessageJsonStr = objectMapper.writeValueAsString(tempChatMessage)

                    val contextChatMessage =
                        ContextChatMessage(
                            chatId = chat.id!!,
                            role = tempChatMessage.role.name.lowercase(),
                            content = chatMessageJsonStr,
                            externalMessageId = null,
                        )

                    val savedContextChatMessage = contextChatMessageService.save(contextChatMessage)
                    logger.info("Mensagem temporária de contexto salva: ${savedContextChatMessage.uuid}")
                } catch (e: Exception) {
                    logger.error("Erro ao converter mensagem temporária para JSON: ${e.message}", e)
                    throw RuntimeException(e)
                }
            }

            // Envia a resposta para o usuário e salva contexto
            val externalMessageId =
                messageManagerService.sendAgentResponse(
                    chat,
                    AgentResponse.TextResponse(responseText),
                )

            logger.info("Resposta enviada para ${chat.userPhoneNumber} com ID externo: $externalMessageId")
        } catch (e: Exception) {
            logger.error("Erro ao gerar/enviar resposta para a mensagem ${event.externalMessageId}", e)
            // Envia a resposta para o usuário e salva contexto
            val externalMessageId =
                messageManagerService.sendAgentResponse(
                    chat,
                    AgentResponse.TextResponse("Desculpe, tive um problema, tente novamente"),
                )
            logger.info("Resposta de erro enviada para ${chat.userPhoneNumber} com ID externo: $externalMessageId")
        }
    }
}
