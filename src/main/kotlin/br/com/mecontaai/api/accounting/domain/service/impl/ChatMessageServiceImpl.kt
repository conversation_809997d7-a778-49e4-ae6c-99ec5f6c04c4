package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.enum.ChatMessageStatus
import br.com.mecontaai.api.accounting.domain.model.ChatMessageEvent
import br.com.mecontaai.api.accounting.domain.service.ChatMessageService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatMessageEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.ChatMessageEntityRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId

@Service
class ChatMessageServiceImpl(
    private val chatMessageEntityRepository: ChatMessageEntityRepository
) : ChatMessageService {
    private val logger = LoggerFactory.getLogger(ChatMessageServiceImpl::class.java)

    @Transactional
    override fun saveMessage(event: ChatMessageEvent, chat: ChatEntity): ChatMessageEntity {
        logger.info("Salvando mensagem para o chat ${chat.uuid}")
        
        // Verifica se a mensagem já existe
        val existingMessage = event.externalMessageId?.let {
            chatMessageEntityRepository.findByExternalMessageId(it)
        }
        
        if (existingMessage?.isPresent == true) {
            logger.info("Mensagem já existe com ID ${existingMessage.get().id}")
            return existingMessage.get()
        }
        
        // Cria uma nova entidade de mensagem
        val messageEntity = ChatMessageEntity(
            chat = chat,
            externalMessageId = event.externalMessageId,
            metaPhoneNumberId = event.metaPhoneNumberId,
            status = ChatMessageStatus.RECEIVED,
            isMessageToMe = event.isMessageToMe,
            isMessageFromMe = event.isMessageFromMe,
            isGroupMessage = event.isGroupMessage,
            isForwardedMessage = event.isForwardedMessage,
            isFromBot = event.isFromBot,
            message = event.message,
            messageAuthorName = event.messageAuthorName,
            messageAuthorPhoneNumber = event.messageAuthorPhoneNumber,
            mediaMimeType = event.media?.mimeType,
            mediaDownloadUrl = event.media?.downloadUrl,
            mediaFilename = event.media?.fileName,
            mediaCaption = event.media?.caption,
            sentAt = event.timestamp?.let { LocalDateTime.ofInstant(Instant.ofEpochMilli(it), ZoneId.systemDefault()) },
            originatingMessageUuid = event.chatMessageUuid
        )
        
        // Salva a mensagem
        val savedMessage = chatMessageEntityRepository.save(messageEntity)
        logger.info("Mensagem salva com ID ${savedMessage.id} e UUID ${savedMessage.uuid}")
        
        return savedMessage
    }
} 