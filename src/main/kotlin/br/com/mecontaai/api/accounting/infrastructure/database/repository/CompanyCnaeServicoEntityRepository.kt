package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.CompanyCnaeServicoEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

/**
 * JPA repository for CompanyCnaeServicoEntity.
 */
@Repository
interface CompanyCnaeServicoEntityRepository : JpaRepository<CompanyCnaeServicoEntity, Long> {
    /**
     * Find a company-CNAE-service association by UUID.
     *
     * @param uuid The UUID to search for
     * @return The company-CNAE-service entity wrapped in an Optional
     */
    fun findByUuid(uuid: UUID): Optional<CompanyCnaeServicoEntity>

    /**
     * Find all company-CNAE-service associations for a company.
     *
     * @param companyId The company ID
     * @return A list of company-CNAE-service entities
     */
    @Query("SELECT ccs FROM CompanyCnaeServicoEntity ccs WHERE ccs.company.id = :companyId")
    fun findByCompanyId(@Param("companyId") companyId: Long): List<CompanyCnaeServicoEntity>

    /**
     * Find the primary company-CNAE-service association for a company.
     *
     * @param companyId The company ID
     * @return The primary company-CNAE-service entity wrapped in an Optional
     */
    @Query("SELECT ccs FROM CompanyCnaeServicoEntity ccs WHERE ccs.company.id = :companyId AND ccs.isPrimary = true")
    fun findPrimaryByCompanyId(@Param("companyId") companyId: Long): Optional<CompanyCnaeServicoEntity>

    /**
     * Find company-CNAE-service associations by CNAE ID.
     *
     * @param cnaeId The CNAE ID
     * @return A list of company-CNAE-service entities
     */
    @Query("SELECT ccs FROM CompanyCnaeServicoEntity ccs WHERE ccs.cnae.id = :cnaeId")
    fun findByCnaeId(@Param("cnaeId") cnaeId: Long): List<CompanyCnaeServicoEntity>

    /**
     * Find company-CNAE-service associations by service code ID.
     *
     * @param codigoServicoId The service code ID
     * @return A list of company-CNAE-service entities
     */
    @Query("SELECT ccs FROM CompanyCnaeServicoEntity ccs WHERE ccs.codigoServico.id = :codigoServicoId")
    fun findByCodigoServicoId(@Param("codigoServicoId") codigoServicoId: Long): List<CompanyCnaeServicoEntity>
} 