package br.com.mecontaai.api.accounting.infrastructure.client.infosimples.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO que representa a resposta da API do InfoSimples para consulta de CCM.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class InfoSimplesResponseDto(
    @JsonProperty("code")
    val code: Int,
    
    @JsonProperty("code_message")
    val codeMessage: String,
    
    @JsonProperty("header")
    val header: InfoSimplesHeaderDto,
    
    @JsonProperty("data_count")
    val dataCount: Int,
    
    @JsonProperty("data")
    val data: List<InfoSimplesCcmDataDto>,
    
    @JsonProperty("errors")
    val errors: List<String>,
    
    @JsonProperty("site_receipts")
    val siteReceipts: List<String>
)

/**
 * DTO que representa o header da resposta do InfoSimples.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class InfoSimplesHeaderDto(
    @JsonProperty("api_version")
    val apiVersion: String,
    
    @JsonProperty("api_version_full")
    val apiVersionFull: String,
    
    @JsonProperty("product")
    val product: String,
    
    @JsonProperty("service")
    val service: String,
    
    @JsonProperty("parameters")
    val parameters: Map<String, String>,
    
    @JsonProperty("client_name")
    val clientName: String,
    
    @JsonProperty("token_name")
    val tokenName: String,
    
    @JsonProperty("billable")
    val billable: Boolean,
    
    @JsonProperty("price")
    val price: String,
    
    @JsonProperty("requested_at")
    val requestedAt: String,
    
    @JsonProperty("elapsed_time_in_milliseconds")
    val elapsedTimeInMilliseconds: Int,
    
    @JsonProperty("remote_ip")
    val remoteIp: String,
    
    @JsonProperty("signature")
    val signature: String
)

/**
 * DTO que representa os dados de CCM retornados pelo InfoSimples.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class InfoSimplesCcmDataDto(
    @JsonProperty("atualizacao_data")
    val atualizacaoData: String?,
    
    @JsonProperty("cancelada")
    val cancelada: Boolean,
    
    @JsonProperty("cancelamento_data")
    val cancelamentoData: String?,
    
    @JsonProperty("ccm")
    val ccm: String,
    
    @JsonProperty("ccm_centralizador")
    val ccmCentralizador: String?,
    
    @JsonProperty("cnaes")
    val cnaes: List<InfoSimplesCnaeDto>,
    
    @JsonProperty("cnpj")
    val cnpj: String,
    
    @JsonProperty("cpf")
    val cpf: String?,
    
    @JsonProperty("endereco_bairro")
    val enderecoBairro: String,
    
    @JsonProperty("endereco_cep")
    val enderecoCep: String,
    
    @JsonProperty("endereco_localizacao")
    val enderecoLocalizacao: String,
    
    @JsonProperty("endereco_tipo")
    val enderecoTipo: String,
    
    @JsonProperty("inicio_data")
    val inicioData: String,
    
    @JsonProperty("inscricao_data")
    val inscricaoData: String,
    
    @JsonProperty("iptu_numero")
    val iptuNumero: String?,
    
    @JsonProperty("nome")
    val nome: String,
    
    @JsonProperty("normalizado_atualizacao_data")
    val normalizadoAtualizacaoData: String?,
    
    @JsonProperty("normalizado_ccm")
    val normalizadoCcm: String,
    
    @JsonProperty("normalizado_cnpj")
    val normalizadoCnpj: String,
    
    @JsonProperty("normalizado_cpf")
    val normalizadoCpf: String?,
    
    @JsonProperty("normalizado_endereco_cep")
    val normalizadoEnderecoCep: String,
    
    @JsonProperty("normalizado_inicio_data")
    val normalizadoInicioData: String,
    
    @JsonProperty("normalizado_inscricao_data")
    val normalizadoInscricaoData: String,
    
    @JsonProperty("pessoa_juridica")
    val pessoaJuridica: String,
    
    @JsonProperty("telefone")
    val telefone: String?,
    
    @JsonProperty("tipo_unidade")
    val tipoUnidade: String,
    
    @JsonProperty("tributos")
    val tributos: List<InfoSimplesTributoDto>,
    
    @JsonProperty("site_receipt")
    val siteReceipt: String?
)

/**
 * DTO que representa um CNAE retornado pelo InfoSimples.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class InfoSimplesCnaeDto(
    @JsonProperty("codigo")
    val codigo: String,
    
    @JsonProperty("descricao")
    val descricao: String,
    
    @JsonProperty("tipo")
    val tipo: String,
    
    @JsonProperty("data_inicio")
    val dataInicio: String
)

/**
 * DTO que representa um tributo retornado pelo InfoSimples.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class InfoSimplesTributoDto(
    @JsonProperty("codigo")
    val codigo: String,
    
    @JsonProperty("data_inicio")
    val dataInicio: String,
    
    @JsonProperty("tributo")
    val tributo: String,
    
    @JsonProperty("aliquota")
    val aliquota: String,
    
    @JsonProperty("quantidade")
    val quantidade: String?
)
