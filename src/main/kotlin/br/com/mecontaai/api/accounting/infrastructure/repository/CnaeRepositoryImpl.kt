package br.com.mecontaai.api.accounting.infrastructure.repository

import br.com.mecontaai.api.accounting.domain.entity.Cnae
import br.com.mecontaai.api.accounting.domain.repository.CnaeRepository
import br.com.mecontaai.api.accounting.infrastructure.database.model.CnaeEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CnaeEntityRepository
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Implementação do repositório de CNAEs.
 */
@Repository
class CnaeRepositoryImpl(
    private val cnaeEntityRepository: CnaeEntityRepository
) : CnaeRepository {

    override fun save(cnae: Cnae): Cnae {
        val entity = mapToEntity(cnae)
        val savedEntity = cnaeEntityRepository.save(entity)
        return mapToDomain(savedEntity)
    }

    override fun findById(id: Long): Cnae? {
        return cnaeEntityRepository.findById(id)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByUuid(uuid: String): Cnae? {
        return try {
            val uuidObj = UUID.fromString(uuid)
            cnaeEntityRepository.findByUuid(uuidObj)
                .map { mapToDomain(it) }
                .orElse(null)
        } catch (e: IllegalArgumentException) {
            null
        }
    }

    override fun findByCode(code: String): Cnae? {
        return cnaeEntityRepository.findByCode(code)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findAll(): List<Cnae> {
        return cnaeEntityRepository.findAll()
            .map { mapToDomain(it) }
    }

    /**
     * Converte entidade de domínio para entidade JPA.
     */
    private fun mapToEntity(cnae: Cnae): CnaeEntity {
        val entity = CnaeEntity(
            code = cnae.code,
            description = cnae.description,
            simplesNacionalAnnexo = cnae.simplesNacionalAnnexo?.toIntOrNull()
        )
        
        entity.id = cnae.id
        entity.uuid = cnae.uuid
        entity.isActive = cnae.isActive
        entity.createdAt = cnae.createdAt
        entity.updatedAt = cnae.updatedAt
        
        return entity
    }

    /**
     * Converte entidade JPA para entidade de domínio.
     */
    private fun mapToDomain(entity: CnaeEntity): Cnae {
        return Cnae(
            id = entity.id,
            uuid = entity.uuid,
            code = entity.code,
            description = entity.description,
            simplesNacionalAnnexo = entity.simplesNacionalAnnexo?.toString(),
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
} 