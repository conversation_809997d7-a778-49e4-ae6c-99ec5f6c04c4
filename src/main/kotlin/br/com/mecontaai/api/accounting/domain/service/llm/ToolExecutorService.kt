package br.com.mecontaai.api.accounting.domain.service.llm

import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.ToolExecutionResult
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.constants.ChatMessageRole
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.dto.ChatMessage
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.dto.NotFoundException
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.dto.Tool
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.dto.ToolCall
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.dto.ToolExecutionResponse
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Serviço para execução de chamadas de ferramentas de LLM.
 */
@Service
class ToolExecutorService(
    private val objectMapper: ObjectMapper,
) {
    private val log = LoggerFactory.getLogger(javaClass)

    fun executeTools(
        assistantTools: Map<String, Tool<*, *>>?,
        toolCalls: List<ToolCall>,
    ): List<ToolExecutionResponse> {
        toolCalls.forEach { toolCall -> log.info("Executando chamada de ferramenta: {}", toolCall) }

        val responses =
            toolCalls.map { toolCall ->
                log.info("Executando ferramenta com nome: {}", toolCall.name)
                val response = executeTool(assistantTools, toolCall)
                log.info("Execução da ferramenta concluída para: {}. Resposta: {}", toolCall.name, response)
                response
            }

        log.info("Concluída a execução de todas as ferramentas.")
        return responses
    }

    private fun executeTool(
        assistantTools: Map<String, Tool<*, *>>?,
        toolCall: ToolCall,
    ): ToolExecutionResponse {
        try {
            val tool =
                assistantTools?.get(toolCall.name)
                    ?: throw NotFoundException("Ferramenta ${toolCall.name} não encontrada")

            val argumentsObject: Any?
            try {
                log.info("Analisando argumentos {} para classe {}", toolCall.arguments, tool.parameters)
                argumentsObject = objectMapper.readValue(toolCall.arguments, tool.parameters)
                log.info("Objeto de argumentos analisado: {}", argumentsObject)
            } catch (e: JsonProcessingException) {
                log.error(
                    "Erro ao processar ferramenta {}. Não foi possível analisar para classe {} os argumentos {}",
                    toolCall.name,
                    tool.parameters,
                    toolCall.arguments,
                    e,
                )
                throw e
            }

            // Executa a ferramenta
            // Usamos Any como tipo para contornar o erro de compilação
            val result =
                when (val any = argumentsObject) {
                    null -> throw RuntimeException("Argumentos não podem ser nulos")
                    else ->
                        try {
                            @Suppress("UNCHECKED_CAST")
                            (tool.executor as Function1<Any, Any>).invoke(any)
                        } catch (e: ClassCastException) {
                            throw RuntimeException("Erro de tipo ao executar ferramenta", e)
                        } catch (e: Exception) {
                            throw RuntimeException("Erro ao executar ferramenta", e)
                        }
                }

            // Convertemos o resultado para ToolExecutionResult
            val toolExecutionResult =
                result as? ToolExecutionResult
                    ?: throw RuntimeException("Resultado inválido: ${result?.javaClass?.name}")

            if (toolExecutionResult.systemInstruction != null) {
                throw ArgumentsViolatedException(
                    toolCall.name,
                    toolCall.arguments,
                    toolExecutionResult.systemInstruction,
                )
            }

            val chatMessageContent =
                try {
                    // Tentamos primeiro usar o content, se disponível
                    if (toolExecutionResult.content != null) {
                        objectMapper.writeValueAsString(toolExecutionResult.content)
                    } else {
                        // Se não, usamos a resposta manual
                        objectMapper.writeValueAsString(toolExecutionResult.agentResponses)
                    }
                } catch (e: JsonProcessingException) {
                    log.error(
                        "Erro ao processar conteúdo do resultado da execução da ferramenta: {} - resultado: {}",
                        toolCall.name,
                        toolExecutionResult,
                        e,
                    )
                    throw e
                }

            val toolChatMessage =
                ChatMessage(
                    role = ChatMessageRole.TOOL,
                    content = chatMessageContent,
                    toolCallId = toolCall.id,
                )

            return ToolExecutionResponse(toolExecutionResult, toolChatMessage)
        } catch (e: NotFoundException) {
            log.error("Ferramenta não encontrada: {}", toolCall.name, e)
            throw FunctionExecutorToolNotFoundException(toolCall.name, toolCall.arguments, toolCall.id, e)
        } catch (e: JsonProcessingException) {
            log.error("Erro ao processar argumentos JSON: {}", e.message, e)
            throw FunctionExecutorArgumentParsingException(toolCall.name, toolCall.arguments, e.message ?: "")
        } catch (e: ArgumentsViolatedException) {
            log.error("Argumentos violados: {}", e.instruction, e)
            throw e
        } catch (e: Exception) {
            log.error("Erro ao executar ferramenta: {}", toolCall.name, e)
            throw RuntimeException("Erro ao executar ferramenta ${toolCall.name} com argumentos ${toolCall.arguments}", e)
        }
    }
}
