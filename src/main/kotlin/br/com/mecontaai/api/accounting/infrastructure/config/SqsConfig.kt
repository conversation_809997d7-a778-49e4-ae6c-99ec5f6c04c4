package br.com.mecontaai.api.accounting.infrastructure.config

import br.com.mecontaai.api.accounting.infrastructure.event.SqsQueues
import io.awspring.cloud.sqs.config.SqsMessageListenerContainerFactory
import io.awspring.cloud.sqs.operations.SqsTemplate
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.context.event.EventListener
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest

@Configuration
class SqsConfig(
    @Value("\${aws.region}")
    private val region: String,
    @Value("\${aws.access-key-id}")
    private val accessKeyId: String,
    @Value("\${aws.secret-access-key}")
    private val secretAccessKey: String,
    @Value("\${aws.sqs.endpoint:#{null}}")
    private val endpoint: String?,
) {
    private val logger = LoggerFactory.getLogger(SqsConfig::class.java)

    @Bean
    fun sqsClient(): SqsClient {
        val credentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey)
        val credentialsProvider = StaticCredentialsProvider.create(credentials)

        val builder =
            SqsClient
                .builder()
                .region(Region.of(region))
                .credentialsProvider(credentialsProvider)

        endpoint?.let { builder.endpointOverride(java.net.URI(it)) }

        return builder.build()
    }

    @Bean
    fun sqsAsyncClient(): SqsAsyncClient {
        val credentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey)
        val credentialsProvider = StaticCredentialsProvider.create(credentials)

        val builder =
            SqsAsyncClient
                .builder()
                .region(Region.of(region))
                .credentialsProvider(credentialsProvider)

        endpoint?.let { builder.endpointOverride(java.net.URI(it)) }

        return builder.build()
    }

    @Bean
    fun sqsTemplate(sqsAsyncClient: SqsAsyncClient): SqsTemplate =
        SqsTemplate
            .builder()
            .sqsAsyncClient(sqsAsyncClient)
            .build()

    @Bean
    fun defaultSqsListenerContainerFactory(sqsAsyncClient: SqsAsyncClient): SqsMessageListenerContainerFactory<Any> =
        SqsMessageListenerContainerFactory
            .builder<Any>()
            .sqsAsyncClient(sqsAsyncClient)
            .build()

    @EventListener(ContextRefreshedEvent::class)
    fun createQueues(event: ContextRefreshedEvent) {
        val client = event.applicationContext.getBean(SqsClient::class.java)

        listOf(
            SqsQueues.WEBHOOK_META_NEW_MESSAGE,
            SqsQueues.CHAT_MESSAGE_RECEIVED
        ).forEach { queueName ->
            val createQueueRequest =
                CreateQueueRequest
                    .builder()
                    .queueName(queueName)
                    .build()

            try {
                client.createQueue(createQueueRequest)
                logger.info("Fila SQS criada com sucesso: $queueName")
            } catch (e: Exception) {
                logger.error("Error creating queue $queueName: ${e.message}")
            }
        }
    }
}
