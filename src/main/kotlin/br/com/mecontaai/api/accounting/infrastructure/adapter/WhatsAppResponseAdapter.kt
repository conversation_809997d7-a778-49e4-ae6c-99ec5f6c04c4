package br.com.mecontaai.api.accounting.infrastructure.adapter

import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.model.response.MediaType
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.WhatsAppClient
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.infrastructure.client.whatsapp.dto.WhatsAppInteractiveMessageDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Adapter that converts generic AgentResponse objects to WhatsApp-specific API calls.
 * This allows the domain layer to work with provider-agnostic responses.
 */
@Component
class WhatsAppResponseAdapter(
    private val whatsAppClient: WhatsAppClient,
) {
    private val logger = LoggerFactory.getLogger(WhatsAppResponseAdapter::class.java)

    /**
     * Sends an agent response via WhatsApp.
     *
     * @param response The generic agent response
     * @param chat The chat entity containing recipient information
     * @return The external message ID from WhatsApp
     */
    fun sendResponse(
        response: AgentResponse,
        chat: ChatEntity,
    ): String {
        val phoneNumberId =
            chat.metaPhoneNumberId
                ?: throw IllegalStateException("Chat ${chat.uuid} has no metaPhoneNumberId")

        val recipientPhone = chat.userPhoneNumber

        logger.info("Sending ${response::class.simpleName} to $recipientPhone via WhatsApp")

        return when (response) {
            is AgentResponse.TextResponse -> sendTextResponse(response, phoneNumberId, recipientPhone)
            is AgentResponse.ButtonResponse -> sendButtonResponse(response, phoneNumberId, recipientPhone)
            is AgentResponse.MediaResponse -> sendMediaResponse(response, phoneNumberId, recipientPhone)
            is AgentResponse.ListResponse -> sendListResponse(response, phoneNumberId, recipientPhone)
            is AgentResponse.TemplateResponse -> sendTemplateResponse(response, phoneNumberId, recipientPhone)
            is AgentResponse.FlowResponse -> sendFlowResponse(response, phoneNumberId, recipientPhone)
            is AgentResponse.CompositeResponse -> sendCompositeResponse(response, chat)
        }
    }

    private fun sendTextResponse(
        response: AgentResponse.TextResponse,
        phoneNumberId: String,
        recipientPhone: String,
    ): String =
        whatsAppClient
            .sendMessage(phoneNumberId, recipientPhone, response.content)
            .messages
            .first()
            .id

    private fun sendButtonResponse(
        response: AgentResponse.ButtonResponse,
        phoneNumberId: String,
        recipientPhone: String,
    ): String {
        // Convert generic buttons to WhatsApp DTO format
        val whatsAppButtons =
            response.buttons.take(3).map { button ->
                // WhatsApp limit: 3 buttons
                WhatsAppInteractiveMessageDto.Button(
                    type = "reply",
                    reply =
                        WhatsAppInteractiveMessageDto.Reply(
                            id = button.id,
                            title = button.title.take(20), // WhatsApp limit: 20 chars
                        ),
                )
            }

        val interactivePayload =
            WhatsAppInteractiveMessageDto(
                to = recipientPhone,
                interactive =
                    WhatsAppInteractiveMessageDto.Interactive(
                        type = "button",
                        header = null, // No header for simple button messages
                        body = WhatsAppInteractiveMessageDto.Body(text = response.message),
                        action = WhatsAppInteractiveMessageDto.Action(buttons = whatsAppButtons),
                    ),
            )

        return whatsAppClient
            .sendInteractiveMessage(phoneNumberId, recipientPhone, interactivePayload)
            .messages
            .first()
            .id
    }

    private fun sendMediaResponse(
        response: AgentResponse.MediaResponse,
        phoneNumberId: String,
        recipientPhone: String,
    ): String =
        when (response.mediaType) {
            MediaType.IMAGE -> {
                whatsAppClient
                    .sendImageMessageWithUrl(
                        phoneNumberId = phoneNumberId,
                        to = recipientPhone,
                        imageUrl = response.url,
                        caption = response.caption,
                    ).messages
                    .first()
                    .id
            }

            MediaType.DOCUMENT -> {
                // Implement document sending
                sendDocumentMessage(phoneNumberId, recipientPhone, response.url, response.caption, response.filename)
            }

            MediaType.AUDIO -> {
                // Implement audio sending
                sendAudioMessage(phoneNumberId, recipientPhone, response.url)
            }

            MediaType.VIDEO -> {
                // Implement video sending
                sendVideoMessage(phoneNumberId, recipientPhone, response.url, response.caption)
            }

            MediaType.STICKER -> {
                // Implement sticker sending
                sendStickerMessage(phoneNumberId, recipientPhone, response.url)
            }
        }

    private fun sendListResponse(
        response: AgentResponse.ListResponse,
        phoneNumberId: String,
        recipientPhone: String,
    ): String {
        // Convert to WhatsApp list DTO format
        val sections =
            listOf(
                WhatsAppInteractiveMessageDto.Section(
                    title = null, // WhatsApp lists usually don't have section titles for simple lists
                    rows =
                        response.items.map { item ->
                            WhatsAppInteractiveMessageDto.Row(
                                id = item.id,
                                title = item.title,
                                description = item.description,
                            )
                        },
                ),
            )

        val interactivePayload =
            WhatsAppInteractiveMessageDto(
                to = recipientPhone,
                interactive =
                    WhatsAppInteractiveMessageDto.Interactive(
                        type = "list",
                        header = null, // No header for simple lists
                        body = WhatsAppInteractiveMessageDto.Body(text = response.title),
                        action =
                            WhatsAppInteractiveMessageDto.Action(
                                button = response.buttonText ?: "Ver opções",
                                sections = sections,
                            ),
                    ),
            )

        return whatsAppClient
            .sendInteractiveMessage(phoneNumberId, recipientPhone, interactivePayload)
            .messages
            .first()
            .id
    }

    private fun sendTemplateResponse(
        response: AgentResponse.TemplateResponse,
        phoneNumberId: String,
        recipientPhone: String,
    ): String {
        // Implement template message sending
        // This would require extending WhatsAppClient to support templates
        logger.warn("Template messages not yet implemented, sending as text")
        return whatsAppClient
            .sendMessage(
                phoneNumberId,
                recipientPhone,
                "Template: ${response.templateName}",
            ).messages
            .first()
            .id
    }

    private fun sendFlowResponse(
        response: AgentResponse.FlowResponse,
        phoneNumberId: String,
        recipientPhone: String,
    ): String {
        // Implement WhatsApp Flow sending
        logger.warn("Flow messages not yet implemented, sending as text")
        return whatsAppClient
            .sendMessage(
                phoneNumberId,
                recipientPhone,
                response.triggerText ?: "Iniciando fluxo: ${response.flowId}",
            ).messages
            .first()
            .id
    }

    private fun sendCompositeResponse(
        response: AgentResponse.CompositeResponse,
        chat: ChatEntity,
    ): String {
        // Send multiple responses in sequence
        var lastMessageId = ""
        response.responses.forEach { subResponse ->
            lastMessageId = sendResponse(subResponse, chat)
            // Add small delay between messages if needed
            Thread.sleep(500)
        }
        return lastMessageId
    }

    private fun formatText(
        text: String,
        formatting: br.com.mecontaai.api.accounting.domain.model.response.TextFormatting?,
    ): String {
        if (formatting == null) return text

        var formatted = text
        if (formatting.bold) formatted = "*$formatted*"
        if (formatting.italic) formatted = "_${formatted}_"
        if (formatting.monospace) formatted = "```$formatted```"

        return formatted
    }

    // Placeholder methods for media types not yet implemented
    private fun sendDocumentMessage(
        phoneNumberId: String,
        to: String,
        url: String,
        caption: String?,
        filename: String?,
    ): String {
        logger.warn("Document messages not yet implemented")
        return whatsAppClient
            .sendMessage(phoneNumberId, to, "📄 Documento: ${filename ?: "arquivo"}")
            .messages
            .first()
            .id
    }

    private fun sendAudioMessage(
        phoneNumberId: String,
        to: String,
        url: String,
    ): String {
        logger.warn("Audio messages not yet implemented")
        return whatsAppClient
            .sendMessage(phoneNumberId, to, "🎵 Áudio anexado")
            .messages
            .first()
            .id
    }

    private fun sendVideoMessage(
        phoneNumberId: String,
        to: String,
        url: String,
        caption: String?,
    ): String {
        logger.warn("Video messages not yet implemented")
        return whatsAppClient
            .sendMessage(phoneNumberId, to, "🎥 Vídeo: ${caption ?: ""}")
            .messages
            .first()
            .id
    }

    private fun sendStickerMessage(
        phoneNumberId: String,
        to: String,
        url: String,
    ): String {
        logger.warn("Sticker messages not yet implemented")
        return whatsAppClient
            .sendMessage(phoneNumberId, to, "😊 Sticker")
            .messages
            .first()
            .id
    }
}
