package br.com.mecontaai.api.accounting.domain.exception

/**
 * Exceção lançada quando se tenta criar uma empresa com um documento (CNPJ) que já existe.
 */
class CompanyDocumentAlreadyExistsException(document: String) : 
    RuntimeException("Já existe uma empresa cadastrada com o documento $document")

/**
 * Exception thrown when a company with a specific document (CNPJ) is not found.
 */
class CompanyNotFoundException(message: String) : RuntimeException(message)

/**
 * Exceção lançada quando os dados de um CNPJ não são encontrados durante a criação de uma empresa.
 */
class CNPJDataNotFoundException(cnpj: String) : RuntimeException("Dados para o CNPJ $cnpj não encontrados") 