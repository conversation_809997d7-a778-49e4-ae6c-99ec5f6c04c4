package br.com.mecontaai.api.accounting.infrastructure.event.consumer

import br.com.mecontaai.api.accounting.domain.model.ChatMessageEvent
import br.com.mecontaai.api.accounting.domain.service.ChatMessageProcessingService
import br.com.mecontaai.api.accounting.infrastructure.event.SqsQueues
import com.fasterxml.jackson.databind.ObjectMapper
import io.awspring.cloud.sqs.annotation.SqsListener
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Infrastructure consumer for processing ChatMessageEvent objects from SQS queue.
 * This consumer is provider-agnostic - it processes ChatMessageEvent regardless of whether
 * it came from Meta, Discord, Telegram, or any other messaging provider.
 *
 * Responsibilities:
 * - Consumes messages from SQS queue
 * - Deserializes ChatMessageEvent
 * - Validates message source
 * - Delegates to domain service for business logic processing
 */
@Component
class ChatMessageConsumer(
    private val objectMapper: ObjectMapper,
    private val chatMessageProcessingService: ChatMessageProcessingService,
) {
    private val logger = LoggerFactory.getLogger(ChatMessageConsumer::class.java)

    @SqsListener(SqsQueues.CHAT_MESSAGE_RECEIVED)
    fun handleChatMessage(message: String) {
        try {
            val chatMessageEvent = objectMapper.readValue(message, ChatMessageEvent::class.java)
            logger.info("Processing ChatMessageEvent: ${chatMessageEvent.externalMessageId} from ${chatMessageEvent.source}")

            // Validate that this is a message from a user (not from bot)
            if (!chatMessageEvent.isMessageFromUser()) {
                logger.debug("Skipping message that is not from user: ${chatMessageEvent.externalMessageId}")
                return
            }

            // Delegate to domain service for complete processing
            chatMessageProcessingService.processUserMessage(chatMessageEvent)

            logger.info("ChatMessageEvent processed successfully: ${chatMessageEvent.externalMessageId}")
        } catch (e: Exception) {
            logger.error("Error processing ChatMessageEvent: ${e.message}", e)
            // throw e // This will cause SQS to retry the message
        }
    }
}
