package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.enum.BlobEntityType
import br.com.mecontaai.api.accounting.infrastructure.database.model.BlobEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Repositório para acesso aos blobs no banco de dados.
 */
@Repository
interface BlobRepository : JpaRepository<BlobEntity, Long> {
    /**
     * Busca um blob pelo UUID
     * 
     * @param uuid UUID do blob
     * @return O blob, ou null se não encontrado
     */
    fun findByUuid(uuid: UUID): BlobEntity?
    
    /**
     * Busca um blob pelo UUID onde está ativo
     * 
     * @param uuid UUID do blob
     * @param isActive Se o blob está ativo
     * @return O blob, ou null se não encontrado
     */
    fun findByUuidAndIsActive(uuid: UUID, isActive: Boolean = true): BlobEntity?
    
    /**
     * Busca blobs por tipo de entidade e ID
     * 
     * @param entityType Tipo da entidade
     * @param entityId ID da entidade
     * @param isActive Se o blob está ativo
     * @return Lista de blobs relacionados à entidade
     */
    fun findByEntityTypeAndEntityIdAndIsActive(
        entityType: BlobEntityType,
        entityId: String,
        isActive: Boolean = true
    ): List<BlobEntity>
    
    /**
     * Verifica se um blob existe pelo UUID
     * 
     * @param uuid UUID do blob
     * @param isActive Se o blob está ativo
     * @return true se o blob existe, false caso contrário
     */
    fun existsByUuidAndIsActive(uuid: UUID, isActive: Boolean = true): Boolean
} 