package br.com.mecontaai.api.accounting.presentation.controller.webhook

import FlowProcessingException
import br.com.mecontaai.api.accounting.application.usecase.ProcessMetaWebhookUseCase
import br.com.mecontaai.api.accounting.application.usecase.ProcessWhatsAppFlowUseCase
import br.com.mecontaai.api.accounting.application.usecase.impl.FlowProcessingException
import br.com.mecontaai.api.accounting.infrastructure.crypto.CryptoException
import br.com.mecontaai.api.accounting.presentation.dto.flows.EncryptedFlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * Controller responsável por receber webhooks do Meta (WhatsApp).
 */
@RestController
@RequestMapping("/api/v1/webhooks/meta")
class MetaWebhookController(
    private val processMetaWebhookUseCase: ProcessMetaWebhookUseCase,
    private val processWhatsAppFlowUseCase: ProcessWhatsAppFlowUseCase,
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(MetaWebhookController::class.java)

    /**
     * Endpoint para receber webhooks gerais do Meta (mensagens, status, etc.).
     */
    @PostMapping
    fun receiveMetaWebhook(
        @RequestBody payload: String,
    ): ResponseEntity<Unit> {
        logger.info("🛜 Recebido webhook Meta: $payload")
        return try {
            val metaWebhookPayload = objectMapper.readValue(payload, MetaWebhookPayload::class.java)
            processMetaWebhookUseCase.execute(metaWebhookPayload)
            ResponseEntity.ok().build()
        } catch (ex: Exception) {
            logger.error("Erro ao processar webhook Meta: $payload", ex)
            ResponseEntity.internalServerError().build()
        }
    }

    /**
     * Endpoint para processar WhatsApp Flows criptografados.
     */
    @PostMapping("/flows")
    fun handleWhatsAppFlow(
        @RequestBody payload: String,
    ): ResponseEntity<String> {
        logger.info("🛜 Recebido WhatsApp Flow: $payload")
        return try {
            val metaFlowsPayload = objectMapper.readValue(payload, EncryptedFlowRequest::class.java)
            val encryptedResponse = processWhatsAppFlowUseCase.execute(metaFlowsPayload)
            ResponseEntity.ok(encryptedResponse)
        } catch (ex: CryptoException) {
            logger.error("❌ Erro de criptografia no WhatsApp Flow", ex)
            ResponseEntity.badRequest().build()
        } catch (ex: FlowProcessingException) {
            logger.error("❌ Erro no processamento do WhatsApp Flow", ex)
            ResponseEntity.internalServerError().build()
        } catch (ex: Exception) {
            logger.error("❌ Erro inesperado no WhatsApp Flow", ex)
            ResponseEntity.internalServerError().build()
        }
    }
}
