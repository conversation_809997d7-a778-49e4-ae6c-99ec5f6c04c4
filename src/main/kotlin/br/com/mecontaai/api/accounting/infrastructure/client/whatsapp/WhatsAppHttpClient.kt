package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp

import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto.WhatsAppDocumentMessageDto
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto.WhatsAppImageMessageDto
import br.com.mecontaai.api.infrastructure.client.whatsapp.dto.WhatsAppInteractiveMessageDto
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto.WhatsAppMessageDto
import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto.WhatsAppResponseDto
import feign.Headers
import feign.Param
import feign.RequestLine

/**
 * Interface que define os endpoints da API do WhatsApp.
 */
@Headers("Content-Type: application/json")
interface WhatsAppHttpClient {
    @RequestLine("POST /{version}/{phoneNumberId}/messages")
    fun sendMessage(
        @Param("version") version: String,
        @Param("phoneNumberId") phoneNumberId: String,
        message: WhatsAppMessageDto
    ): WhatsAppResponseDto

    @RequestLine("POST /{version}/{phoneNumberId}/messages")
    fun sendImageMessage(
        @Param("version") version: String,
        @Param("phoneNumberId") phoneNumberId: String,
        message: WhatsAppImageMessageDto
    ): WhatsAppResponseDto

    @RequestLine("POST /{version}/{phoneNumberId}/messages")
    fun sendDocumentMessage(
        @Param("version") version: String,
        @Param("phoneNumberId") phoneNumberId: String,
        message: WhatsAppDocumentMessageDto
    ): WhatsAppResponseDto

    @RequestLine("POST /{version}/{phoneNumberId}/messages")
    fun sendInteractiveMessage(
        @Param("version") version: String,
        @Param("phoneNumberId") phoneNumberId: String,
        message: WhatsAppInteractiveMessageDto
    ): WhatsAppResponseDto
} 