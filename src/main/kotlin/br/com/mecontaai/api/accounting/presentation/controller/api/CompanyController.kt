package br.com.mecontaai.api.accounting.presentation.controller.api

import br.com.mecontaai.api.accounting.application.usecase.CreateCompanyInFocusUseCase
import br.com.mecontaai.api.accounting.application.usecase.impl.CreateCompanyUseCase
import br.com.mecontaai.api.accounting.domain.exception.CNPJDataNotFoundException
import br.com.mecontaai.api.accounting.domain.exception.CompanyDocumentAlreadyExistsException
import br.com.mecontaai.api.accounting.presentation.dto.CompanyResponseDto
import br.com.mecontaai.api.accounting.presentation.dto.CreateCompanyRequestDto
import br.com.mecontaai.api.accounting.presentation.mapper.CompanyMapper
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException

/**
 * Controller para endpoints de empresa.
 */
@RestController
@RequestMapping("/api/v1/companies")
class CompanyController(
    private val createCompanyUseCase: CreateCompanyUseCase,
    private val createCompanyInFocusUseCase: CreateCompanyInFocusUseCase,
    private val companyMapper: CompanyMapper
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    /**
     * Endpoint para criar uma nova empresa a partir do CNPJ.
     *
     * @param requestDto DTO com o CNPJ da empresa a ser criada
     * @return DTO com os dados da empresa criada
     */
    @PostMapping
    fun createCompany(@Valid @RequestBody requestDto: CreateCompanyRequestDto): ResponseEntity<CompanyResponseDto> {
        logger.info("Criando empresa com CNPJ: ${requestDto.document}")

        try {
            val createCompanyDto = companyMapper.toCreateCompanyDto(requestDto)
            val companyDto = createCompanyUseCase.execute(createCompanyDto)
            val responseDto = companyMapper.toCompanyResponseDto(companyDto)

            logger.info("Empresa criada com sucesso - ID: ${companyDto.id}")

            return ResponseEntity.status(HttpStatus.CREATED).body(responseDto)
        } catch (e: CompanyDocumentAlreadyExistsException) {
            logger.warn("CNPJ já existe: ${requestDto.document}")
            throw ResponseStatusException(HttpStatus.CONFLICT, e.message)
        } catch (e: CNPJDataNotFoundException) {
            logger.warn("CNPJ não encontrado: ${requestDto.document}")
            throw ResponseStatusException(HttpStatus.NOT_FOUND, e.message)
        } catch (e: Exception) {
            logger.error("Erro ao criar empresa com CNPJ ${requestDto.document}: ${e.message}", e)
            throw ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Erro ao criar empresa: ${e.message}")
        }
    }

    /**
     * Endpoint para criar uma empresa na Focus. Apenas para testar o caso de uso.
     *
     * @param companyId ID da empresa a ser criada na Focus
     * @return ID da empresa na Focus
     */
    @PostMapping("/{companyId}/focus")
    fun createCompanyInFocus(@PathVariable companyId: Long): ResponseEntity<Map<String, String>> {
        logger.info("Criando empresa na Focus - companyId: $companyId")

        try {
            val focusId = createCompanyInFocusUseCase.execute(companyId)
            val response = mapOf("focusId" to focusId)

            logger.info("Empresa criada na Focus com sucesso - focusId: $focusId")

            return ResponseEntity.ok(response)
        } catch (e: IllegalArgumentException) {
            logger.warn("Empresa não encontrada: $companyId")
            throw ResponseStatusException(HttpStatus.NOT_FOUND, e.message)
        } catch (e: Exception) {
            logger.error("Erro ao criar empresa na Focus: ${e.message}", e)
            throw ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Erro ao criar empresa na Focus: ${e.message}")
        }
    }
} 