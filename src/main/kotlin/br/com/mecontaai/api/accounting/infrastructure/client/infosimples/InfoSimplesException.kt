package br.com.mecontaai.api.accounting.infrastructure.client.infosimples

/**
 * Exception lançada para erros relacionados à API do InfoSimples.
 */
open class InfoSimplesException : RuntimeException {
    val httpStatus: Int
    val errorCode: String?

    /**
     * Cria uma exceção com mensagem
     */
    constructor(message: String) : super(message) {
        this.httpStatus = 0
        this.errorCode = null
    }

    /**
     * Cria uma exceção com mensagem e código HTTP
     */
    constructor(httpStatus: Int, message: String) : super(message) {
        this.httpStatus = httpStatus
        this.errorCode = null
    }

    /**
     * Cria uma exceção com mensagem, código HTTP e código de erro
     */
    constructor(httpStatus: Int, errorCode: String?, message: String) : super(message) {
        this.httpStatus = httpStatus
        this.errorCode = errorCode
    }

    /**
     * Cria uma exceção com mensagem e causa
     */
    constructor(message: String, cause: Throwable) : super(message, cause) {
        this.httpStatus = 0
        this.errorCode = null
    }
}

/**
 * Exception específica para quando o CNPJ não é encontrado no InfoSimples.
 */
class InfoSimplesCnpjNotFoundException(cnpj: String) : InfoSimplesException("CNPJ $cnpj não encontrado no InfoSimples")
