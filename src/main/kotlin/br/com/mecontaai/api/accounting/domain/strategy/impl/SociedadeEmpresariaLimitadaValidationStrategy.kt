package br.com.mecontaai.api.accounting.domain.strategy.impl

import br.com.mecontaai.api.accounting.domain.dto.CompanyValidationResultDto
import br.com.mecontaai.api.accounting.domain.enum.CompanyValidationErrorType
import br.com.mecontaai.api.accounting.domain.strategy.CompanyValidationStrategy
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto

/**
 * Estratégia de validação para Sociedade Empresária Limitada.
 * Aplica as validações tradicionais de QSA.
 */
class SociedadeEmpresariaLimitadaValidationStrategy : CompanyValidationStrategy {
    override fun validate(cnpjData: ReceitaWsResponseDto): CompanyValidationResultDto? {
        // Valida se existe pelo menos um sócio no QSA
        if (cnpjData.qsa.isNullOrEmpty()) {
            return CompanyValidationResultDto.invalid(
                errorType = CompanyValidationErrorType.NO_PARTNERS,
                errorMessage = "Empresa não possui sócios",
            )
        }

        // Valida se existe exatamente um sócio no QSA
        if (cnpjData.qsa.size > 1) {
            return CompanyValidationResultDto.invalid(
                errorType = CompanyValidationErrorType.MULTIPLE_PARTNERS,
                errorMessage = "Empresa deve ter exatamente um sócio, encontrado: ${cnpjData.qsa.size}",
            )
        }

        // Valida se o sócio é pessoa física (não possui representante legal)
        val socio = cnpjData.qsa.first()
        if (!socio.nome_rep_legal.isNullOrBlank()) {
            return CompanyValidationResultDto.invalid(
                errorType = CompanyValidationErrorType.NON_INDIVIDUAL_PARTNER,
                errorMessage = "Empresa deve ter apenas sócio pessoa física, encontrado pessoa jurídica",
            )
        }
        
        return null
    }
    
    override fun calculatePartnerCount(cnpjData: ReceitaWsResponseDto): Int {
        return cnpjData.qsa?.size ?: 0
    }
    
    override fun hasLegalEntityPartner(cnpjData: ReceitaWsResponseDto): Boolean {
        val socio = cnpjData.qsa?.firstOrNull()
        return !socio?.nome_rep_legal.isNullOrBlank()
    }
}
