package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.enum.ChatMessageStatus
import br.com.mecontaai.api.accounting.domain.model.Button
import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.model.response.MediaType
import br.com.mecontaai.api.accounting.domain.service.callback.ButtonCallbackService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatMessageEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.ContextChatMessage
import br.com.mecontaai.api.accounting.infrastructure.database.repository.ChatMessageEntityRepository
import br.com.mecontaai.api.infrastructure.client.whatsapp.dto.WhatsAppInteractiveMessageDto
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * Serviço responsável por gerenciar o envio e salvamento de mensagens.
 * Centraliza a lógica de envio via MessageSenderService e salvamento no banco de dados.
 */
@Service
class MessageManagerService(
    private val messageSenderService: MessageSenderService,
    private val chatMessageEntityRepository: ChatMessageEntityRepository,
    private val contextChatMessageService: ContextChatMessageService,
    private val buttonCallbackService: ButtonCallbackService,
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(MessageManagerService::class.java)

    /**
     * Processa múltiplas respostas do agente.
     *
     * @param chat Chat para o qual as mensagens serão enviadas
     * @param agentResponses Lista de respostas do agente
     * @return Lista de IDs das mensagens no provedor externo
     */
    fun sendAgentResponses(
        chat: ChatEntity,
        agentResponses: List<AgentResponse>,
    ): List<String> {
        logger.info("Enviando ${agentResponses.size} respostas do agente para ${chat.userPhoneNumber}")

        val messageIds = mutableListOf<String>()

        agentResponses.forEach { response ->
            try {
                val messageId = sendAgentResponse(chat, response)
                messageIds.add(messageId)

                // Pequeno delay entre mensagens para evitar rate limiting
                if (agentResponses.size > 1) {
                    Thread.sleep(300)
                }
            } catch (e: Exception) {
                logger.error("Erro ao enviar resposta do agente: ${response::class.simpleName}", e)
                // Continua com as outras respostas
            }
        }

        logger.info("Enviadas ${messageIds.size}/${agentResponses.size} respostas para ${chat.userPhoneNumber}")
        return messageIds
    }

    /**
     * Processa uma resposta do agente e envia a mensagem apropriada.
     *
     * @param chat Chat para o qual a mensagem será enviada
     * @param agentResponse Resposta do agente a ser processada
     * @return ID da mensagem no provedor externo
     */
    fun sendAgentResponse(
        chat: ChatEntity,
        agentResponse: AgentResponse,
    ): String {
        // Log detalhado do AgentResponse em JSON
        logger.info("🤖 AgentResponse | Para: {} | Tipo: {} | JSON: {}",
            chat.userPhoneNumber,
            agentResponse::class.simpleName,
            objectMapper.writeValueAsString(agentResponse))

        return when (agentResponse) {
            is AgentResponse.TextResponse -> {
                sendTextMessage(chat, agentResponse.content)
            }

            is AgentResponse.ButtonResponse -> {
                val buttons =
                    agentResponse.buttons.map { agentButton ->
                        Button(
                            id = agentButton.id,
                            label = agentButton.title,
                            action = agentButton.payload ?: agentButton.id,
                        )
                    }
                sendButtonMessage(chat, agentResponse.message, buttons, agentResponse.buttons)
            }

            is AgentResponse.MediaResponse -> {
                when (agentResponse.mediaType) {
                    MediaType.IMAGE -> {
                        if (agentResponse.url.startsWith("http")) {
                            sendImageMessageWithUrl(chat, agentResponse.url, agentResponse.caption)
                        } else {
                            // Assume que é um ID de imagem
                            sendImageMessage(chat, agentResponse.url, agentResponse.caption)
                        }
                    }

                    else -> {
                        // Para outros tipos de mídia, enviar como texto por enquanto
                        val message = agentResponse.caption ?: "Mídia: ${agentResponse.mediaType.name.lowercase()}"
                        sendTextMessage(chat, message)
                    }
                }
            }

            is AgentResponse.CompositeResponse -> {
                // Para respostas compostas, enviar apenas a primeira por enquanto
                // TODO: Implementar envio de múltiplas mensagens
                if (agentResponse.responses.isNotEmpty()) {
                    sendAgentResponse(chat, agentResponse.responses.first())
                } else {
                    sendTextMessage(chat, "Resposta vazia")
                }
            }

            else -> {
                // Para tipos não suportados, enviar mensagem de fallback
                logger.warn("Tipo de resposta não suportado: ${agentResponse::class.simpleName}")
                sendTextMessage(chat, "Tipo de resposta não suportado no momento.")
            }
        }
    }

    /**
     * Envia uma mensagem de texto e salva no banco de dados.
     *
     * @param chat Chat para qual a mensagem será enviada
     * @param text Texto da mensagem
     * @return ID da mensagem no provedor externo
     */
    private fun sendTextMessage(
        chat: ChatEntity,
        text: String,
    ): String {
        logger.info("Enviando mensagem de texto para ${chat.userPhoneNumber}: $text")

        // Envia a mensagem via MessageSenderService
        val externalMessageId = messageSenderService.sendTextMessage(chat.userPhoneNumber, text)

        // Salva a mensagem no banco
        val chatMessageEntity =
            ChatMessageEntity(
                chat = chat,
                externalMessageId = externalMessageId,
                status = ChatMessageStatus.SENT,
                isMessageToMe = false,
                isMessageFromMe = true,
                isGroupMessage = false,
                isForwardedMessage = false,
                isFromBot = true,
                message = text,
                sentAt = LocalDateTime.now(),
            )

        val savedMessage = chatMessageEntityRepository.save(chatMessageEntity)
        logger.info("Mensagem salva com ID ${savedMessage.id} e UUID ${savedMessage.uuid}")

        // Cria o JSON com o conteúdo da mensagem
        val messageContent =
            objectMapper.createObjectNode().apply {
                put("role", "assistant")
                put("content", text)
            }

        // Salva o contexto da mensagem
        val contextMessage =
            ContextChatMessage(
                chatId = chat.id!!,
                role = "assistant",
                content = objectMapper.writeValueAsString(messageContent),
                externalMessageId = externalMessageId,
            )
        contextChatMessageService.save(contextMessage)
        logger.info("Contexto da mensagem salvo com UUID ${contextMessage.uuid}")

        return externalMessageId
    }

    /**
     * Envia uma mensagem com imagem usando o ID da imagem e salva no banco de dados.
     *
     * @param chat Chat para qual a mensagem será enviada
     * @param imageId ID da imagem no provedor externo
     * @param caption Legenda da imagem (opcional)
     * @return ID da mensagem no provedor externo
     */
    private fun sendImageMessage(
        chat: ChatEntity,
        imageId: String,
        caption: String? = null,
    ): String {
        logger.info("Enviando mensagem com imagem para ${chat.userPhoneNumber}")

        // Envia a mensagem via MessageSenderService
        val externalMessageId =
            messageSenderService.sendImageMessage(
                to = chat.userPhoneNumber,
                imageId = imageId,
                caption = caption,
            )

        // Salva a mensagem no banco
        val message =
            ChatMessageEntity(
                chat = chat,
                externalMessageId = externalMessageId,
                status = ChatMessageStatus.SENT,
                isMessageToMe = false,
                isMessageFromMe = true,
                isGroupMessage = false,
                isForwardedMessage = false,
                isFromBot = true,
                message = caption,
                mediaMimeType = "image/jpeg",
                mediaDownloadUrl = "mock://images/$imageId",
                sentAt = LocalDateTime.now(),
            )

        val savedMessage = chatMessageEntityRepository.save(message)
        logger.info("Mensagem salva com ID ${savedMessage.id} e UUID ${savedMessage.uuid}")

        // Cria o JSON com o conteúdo da mensagem
        val messageContent =
            objectMapper.createObjectNode().apply {
                put("role", "assistant")
                put("content", caption ?: "[Imagem enviada]")
                val mediaNode = putObject("media")
                mediaNode.put("type", "image")
                mediaNode.put("url", "mock://images/$imageId")
                mediaNode.put("mimeType", "image/jpeg")
            }

        // Salva o contexto da mensagem
        val contextMessage =
            ContextChatMessage(
                chatId = chat.id!!,
                role = "assistant",
                content = objectMapper.writeValueAsString(messageContent),
                externalMessageId = externalMessageId,
            )
        contextChatMessageService.save(contextMessage)
        logger.info("Contexto da mensagem salvo com UUID ${contextMessage.uuid}")

        return externalMessageId
    }

    /**
     * Envia uma mensagem com imagem usando a URL da imagem e salva no banco de dados.
     *
     * @param chat Chat para qual a mensagem será enviada
     * @param imageUrl URL da imagem
     * @param caption Legenda da imagem (opcional)
     * @return ID da mensagem no provedor externo
     */
    private fun sendImageMessageWithUrl(
        chat: ChatEntity,
        imageUrl: String,
        caption: String? = null,
    ): String {
        logger.info("Enviando mensagem com imagem via URL para ${chat.userPhoneNumber}")

        // Envia a mensagem via MessageSenderService
        val externalMessageId =
            messageSenderService.sendImageMessageWithUrl(
                to = chat.userPhoneNumber,
                imageUrl = imageUrl,
                caption = caption,
            )

        // Salva a mensagem no banco
        val message =
            ChatMessageEntity(
                chat = chat,
                externalMessageId = externalMessageId,
                status = ChatMessageStatus.SENT,
                isMessageToMe = false,
                isMessageFromMe = true,
                isGroupMessage = false,
                isForwardedMessage = false,
                isFromBot = true,
                message = caption,
                mediaMimeType = "image/jpeg",
                mediaDownloadUrl = imageUrl,
                sentAt = LocalDateTime.now(),
            )

        val savedMessage = chatMessageEntityRepository.save(message)
        logger.info("Mensagem salva com ID ${savedMessage.id} e UUID ${savedMessage.uuid}")

        // Cria o JSON com o conteúdo da mensagem
        val messageContent =
            objectMapper.createObjectNode().apply {
                put("role", "assistant")
                put("content", caption ?: "[Imagem enviada]")
                val mediaNode = putObject("media")
                mediaNode.put("type", "image")
                mediaNode.put("url", imageUrl)
                mediaNode.put("mimeType", "image/jpeg")
            }

        // Salva o contexto da mensagem
        val contextMessage =
            ContextChatMessage(
                chatId = chat.id!!,
                role = "assistant",
                content = objectMapper.writeValueAsString(messageContent),
                externalMessageId = externalMessageId,
            )
        contextChatMessageService.save(contextMessage)
        logger.info("Contexto da mensagem salvo com UUID ${contextMessage.uuid}")

        return externalMessageId
    }

    /**
     * Envia uma mensagem com botões e salva no banco de dados.
     *
     * @param chat Chat para o qual a mensagem será enviada.
     * @param text Texto da mensagem.
     * @param buttons Lista de botões.
     * @param agentButtons Lista de botões originais do agente (para extrair callbackIds).
     * @return ID da mensagem no provedor externo.
     */
    private fun sendButtonMessage(
        chat: ChatEntity,
        text: String,
        buttons: List<Button>,
        agentButtons: List<br.com.mecontaai.api.accounting.domain.model.response.AgentButton>,
    ): String {
        logger.info("Enviando mensagem com botões para ${chat.userPhoneNumber}")

        // Extrai callbackIds dos payloads dos botões
        val callbackIds =
            agentButtons.mapNotNull { agentButton ->
                agentButton.payload?.let { payload ->
                    if (payload.contains(":")) {
                        payload.split(":", limit = 2)[0] // Extrai callbackId antes do ":"
                    } else {
                        null
                    }
                }
            }

        // Monta a estrutura de botões no formato esperado usando payload completo como ID
        val buttonTemplates =
            agentButtons.mapIndexed { index, agentButton ->
                val buttonId =
                    if (agentButton.payload?.contains(":") == true) {
                        // Usa o payload completo como ID do botão (callbackId:clickedValue)
                        agentButton.payload
                    } else {
                        // Fallback para o ID original
                        agentButton.id
                    }

                WhatsAppInteractiveMessageDto.Button(
                    reply =
                        WhatsAppInteractiveMessageDto.Reply(
                            id = buttonId,
                            title = agentButton.title,
                        ),
                )
            }

        // Cria o DTO da mensagem interativa
        val whatsAppInteractiveMessageDto =
            WhatsAppInteractiveMessageDto(
                recipientType = "individual",
                to = chat.userPhoneNumber,
                type = "interactive",
                interactive =
                    WhatsAppInteractiveMessageDto.Interactive(
                        type = "button",
                        header =
                            WhatsAppInteractiveMessageDto.Header(
                                type = "text",
                                text = "Escolha uma opção:",
                            ),
                        body =
                            WhatsAppInteractiveMessageDto.Body(
                                text = text,
                            ),
                        action =
                            WhatsAppInteractiveMessageDto.Action(
                                buttons = buttonTemplates,
                            ),
                    ),
            )

        // Serializa a mensagem para JSON
        val payloadJson = objectMapper.writeValueAsString(whatsAppInteractiveMessageDto)

        // Envia a mensagem com o serviço MessageSenderService
        val externalMessageId =
            messageSenderService.sendInteractiveMessage(
                to = chat.userPhoneNumber,
                payload = whatsAppInteractiveMessageDto,
            )

        // Atualiza os callbacks com o external message ID
        callbackIds.forEach { callbackId ->
            try {
                buttonCallbackService.updateCallbackWithExternalMessageId(callbackId, externalMessageId)
                logger.info("Callback $callbackId atualizado com external message ID: $externalMessageId")
            } catch (e: Exception) {
                logger.error("Erro ao atualizar callback $callbackId com external message ID", e)
            }
        }

        // Salva a mensagem no banco de dados
        val chatMessageEntity =
            ChatMessageEntity(
                chat = chat,
                externalMessageId = externalMessageId,
                status = ChatMessageStatus.SENT,
                isMessageToMe = false,
                isMessageFromMe = true,
                isGroupMessage = false,
                isForwardedMessage = false,
                isFromBot = true,
                message = text + " [botões]",
                sentAt = LocalDateTime.now(),
            )

        val savedMessage = chatMessageEntityRepository.save(chatMessageEntity)
        logger.info("Mensagem com botões salva com ID ${savedMessage.id} e UUID ${savedMessage.uuid}")

        // Salva o contexto da mensagem
        val contextMessage =
            ContextChatMessage(
                chatId = chat.id!!,
                role = "assistant",
                content = payloadJson,
                externalMessageId = externalMessageId,
            )
        contextChatMessageService.save(contextMessage)
        logger.info("Contexto da mensagem com botões salvo com UUID ${contextMessage.uuid}")

        return externalMessageId
    }
}
