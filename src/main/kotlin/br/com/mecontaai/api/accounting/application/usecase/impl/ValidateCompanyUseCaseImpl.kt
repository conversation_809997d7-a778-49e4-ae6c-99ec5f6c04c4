package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.dto.FullCompanyDataDto
import br.com.mecontaai.api.accounting.application.usecase.ValidateCompanyUseCase
import br.com.mecontaai.api.accounting.domain.dto.CompanyCnpjDataDto
import br.com.mecontaai.api.accounting.domain.dto.CompanyValidationResultDto
import br.com.mecontaai.api.accounting.domain.enum.CompanyValidationErrorType
import br.com.mecontaai.api.accounting.domain.enum.NaturezaJuridicaEnum
import br.com.mecontaai.api.accounting.domain.factory.CompanyValidationStrategyFactory
import org.springframework.stereotype.Component

/**
 * Implementação do caso de uso para validação de empresas.
 */
@Component
class ValidateCompanyUseCaseImpl : ValidateCompanyUseCase {
    /**
     * Valida um CNPJ para verificar se atende aos requisitos para criação de empresa.
     *
     * Requisitos:
     * - CNPJ válido e ativo na Receita Federal
     * - Empresa deve ser optante do Simples Nacional
     * - Deve possuir apenas um sócio pessoa física (QSA)
     *
     * @param document CNPJ a ser validado
     * @return Resultado da validação contendo status e dados do CNPJ ou motivo da falha
     */
    override fun execute(fullCompanyDataDto: FullCompanyDataDto): CompanyValidationResultDto {
        // Consulta os dados do CNPJ na Receita Federal
        val receitaWsData = fullCompanyDataDto.receitaWsData

        // Valida se a situação da empresa está ATIVA
        if (receitaWsData.situacao != "ATIVA") {
            return CompanyValidationResultDto.invalid(
                errorType = CompanyValidationErrorType.CNPJ_NOT_ACTIVE,
                errorMessage = "CNPJ não está ativo: ${fullCompanyDataDto.cnpj}",
            )
        }

        // Valida se a empresa é optante do Simples Nacional
        if (receitaWsData.simples?.optante != true) {
            return CompanyValidationResultDto.invalid(
                errorType = CompanyValidationErrorType.NOT_SIMPLES_NACIONAL,
                errorMessage = "Empresa não é optante do Simples Nacional: $fullCompanyDataDto.cnpj",
            )
        }

        // Determina a natureza jurídica e obtém a estratégia de validação apropriada
        val naturezaJuridica = NaturezaJuridicaEnum.fromReceitaWsString(receitaWsData.natureza_juridica)
        val validationStrategy = CompanyValidationStrategyFactory.getStrategy(naturezaJuridica)

        // Aplica a validação específica da natureza jurídica
        val validationResult = validationStrategy.validate(receitaWsData)
        if (validationResult != null) {
            return validationResult
        }

        // Extrair código e descrição do CNAE de forma segura
        val atividadePrincipal = receitaWsData.atividade_principal?.firstOrNull()

        // Valores padrão se não conseguir extrair
        val codigoCnae = extractPropertySafely(atividadePrincipal, "code") ?: ""
        val descricaoCnae = extractPropertySafely(atividadePrincipal, "text") ?: ""

        // Converte os dados para o DTO independente de API
        val companyCnpjData =
            CompanyCnpjDataDto(
                cnpj = receitaWsData.cnpj ?: "",
                razaoSocial = receitaWsData.nome ?: "",
                nomeFantasia = receitaWsData.fantasia,
                simplesNacional = receitaWsData.simples.optante,
                situacao = receitaWsData.situacao,
                dataSituacao = receitaWsData.data_situacao?.toString() ?: "",
                capitalSocial = receitaWsData.capital_social,
                naturezaJuridica = naturezaJuridica,
                // Endereço
                cep = receitaWsData.cep ?: "",
                logradouro = receitaWsData.logradouro ?: "",
                numero = receitaWsData.numero ?: "",
                complemento = receitaWsData.complemento,
                bairro = receitaWsData.bairro ?: "",
                municipio = receitaWsData.municipio ?: "",
                codigoMunicipio = null, // ReceitaWS não retorna código de município
                uf = receitaWsData.uf ?: "",
                // CNAE
                cnaeCodigoPrincipal = codigoCnae,
                cnaeDescricaoPrincipal = descricaoCnae,
                // QSA - usando a estratégia para calcular quantidade e verificar pessoa jurídica
                quantidadeSocios = validationStrategy.calculatePartnerCount(receitaWsData),
                possuiSocioPessoaJuridica = validationStrategy.hasLegalEntityPartner(receitaWsData),
            )

        return CompanyValidationResultDto.valid(companyCnpjData)
    }

    /**
     * Extrai uma propriedade de um objeto de forma segura usando reflection.
     * Funciona com qualquer tipo de objeto, seja ele um DTO específico ou um Map.
     */
    private fun extractPropertySafely(
        obj: Any?,
        propertyName: String,
    ): String? =
        try {
            when (obj) {
                is Map<*, *> -> obj[propertyName]?.toString()
                null -> null
                else -> {
                    // Tentativa de acessar uma propriedade via reflection
                    val property = obj::class.java.getDeclaredField(propertyName)
                    property.isAccessible = true
                    property.get(obj)?.toString()
                }
            }
        } catch (e: Exception) {
            null
        }
}
