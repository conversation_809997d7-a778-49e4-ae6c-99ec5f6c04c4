package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.service.ReceitaWsService
import br.com.mecontaai.api.accounting.application.usecase.ValidateCompanyUseCase
import br.com.mecontaai.api.accounting.domain.dto.CompanyCnpjDataDto
import br.com.mecontaai.api.accounting.domain.dto.CompanyValidationResultDto
import br.com.mecontaai.api.accounting.domain.enum.CompanyValidationErrorType
import br.com.mecontaai.api.accounting.domain.enum.NaturezaJuridicaEnum
import br.com.mecontaai.api.accounting.domain.exception.CNPJDataNotFoundException
import br.com.mecontaai.api.accounting.domain.factory.CompanyValidationStrategyFactory
import org.springframework.stereotype.Component

/**
 * Implementação do caso de uso para validação de empresas.
 */
@Component
class ValidateCompanyUseCaseImpl(
    private val receitaWsService: ReceitaWsService,
) : ValidateCompanyUseCase {
    /**
     * Valida um CNPJ para verificar se atende aos requisitos para criação de empresa.
     *
     * Requisitos:
     * - CNPJ válido e ativo na Receita Federal
     * - Empresa deve ser optante do Simples Nacional
     * - Deve possuir apenas um sócio pessoa física (QSA)
     *
     * @param document CNPJ a ser validado
     * @return Resultado da validação contendo status e dados do CNPJ ou motivo da falha
     */
    override fun execute(document: String): CompanyValidationResultDto {
        // Consulta os dados do CNPJ na Receita Federal
        val cnpjData =
            try {
                receitaWsService.getCnpjData(document)
            } catch (e: CNPJDataNotFoundException) {
                return CompanyValidationResultDto.invalid(
                    errorType = CompanyValidationErrorType.CNPJ_NOT_FOUND,
                    errorMessage = "CNPJ não encontrado: $document",
                )
            } catch (e: Exception) {
                return CompanyValidationResultDto.invalid(
                    errorType = CompanyValidationErrorType.OTHER_ERROR,
                    errorMessage = "Erro ao consultar CNPJ: ${e.message}",
                )
            }

        // Valida se a situação da empresa está ATIVA
        if (cnpjData.situacao != "ATIVA") {
            return CompanyValidationResultDto.invalid(
                errorType = CompanyValidationErrorType.CNPJ_NOT_ACTIVE,
                errorMessage = "CNPJ não está ativo: $document",
            )
        }

        // Valida se a empresa é optante do Simples Nacional
        if (cnpjData.simples?.optante != true) {
            return CompanyValidationResultDto.invalid(
                errorType = CompanyValidationErrorType.NOT_SIMPLES_NACIONAL,
                errorMessage = "Empresa não é optante do Simples Nacional: $document",
            )
        }

        // Determina a natureza jurídica e obtém a estratégia de validação apropriada
        val naturezaJuridica = NaturezaJuridicaEnum.fromReceitaWsString(cnpjData.natureza_juridica)
        val validationStrategy = CompanyValidationStrategyFactory.getStrategy(naturezaJuridica)

        // Aplica a validação específica da natureza jurídica
        val validationResult = validationStrategy.validate(cnpjData)
        if (validationResult != null) {
            return validationResult
        }

        // Extrair código e descrição do CNAE de forma segura
        val atividadePrincipal = cnpjData.atividade_principal?.firstOrNull()

        // Valores padrão se não conseguir extrair
        val codigoCnae = extractPropertySafely(atividadePrincipal, "code") ?: ""
        val descricaoCnae = extractPropertySafely(atividadePrincipal, "text") ?: ""

        // Converte os dados para o DTO independente de API
        val companyCnpjData =
            CompanyCnpjDataDto(
                cnpj = cnpjData.cnpj ?: "",
                razaoSocial = cnpjData.nome ?: "",
                nomeFantasia = cnpjData.fantasia,
                simplesNacional = cnpjData.simples.optante,
                situacao = cnpjData.situacao,
                dataSituacao = cnpjData.data_situacao?.toString() ?: "",
                capitalSocial = cnpjData.capital_social,
                naturezaJuridica = naturezaJuridica,
                // Endereço
                cep = cnpjData.cep ?: "",
                logradouro = cnpjData.logradouro ?: "",
                numero = cnpjData.numero ?: "",
                complemento = cnpjData.complemento,
                bairro = cnpjData.bairro ?: "",
                municipio = cnpjData.municipio ?: "",
                codigoMunicipio = null, // ReceitaWS não retorna código de município
                uf = cnpjData.uf ?: "",
                // CNAE
                cnaeCodigoPrincipal = codigoCnae,
                cnaeDescricaoPrincipal = descricaoCnae,
                // QSA - usando a estratégia para calcular quantidade e verificar pessoa jurídica
                quantidadeSocios = validationStrategy.calculatePartnerCount(cnpjData),
                possuiSocioPessoaJuridica = validationStrategy.hasLegalEntityPartner(cnpjData),
            )

        return CompanyValidationResultDto.valid(companyCnpjData)
    }

    /**
     * Extrai uma propriedade de um objeto de forma segura usando reflection.
     * Funciona com qualquer tipo de objeto, seja ele um DTO específico ou um Map.
     */
    private fun extractPropertySafely(
        obj: Any?,
        propertyName: String,
    ): String? =
        try {
            when (obj) {
                is Map<*, *> -> obj[propertyName]?.toString()
                null -> null
                else -> {
                    // Tentativa de acessar uma propriedade via reflection
                    val property = obj::class.java.getDeclaredField(propertyName)
                    property.isAccessible = true
                    property.get(obj)?.toString()
                }
            }
        } catch (e: Exception) {
            null
        }
}
