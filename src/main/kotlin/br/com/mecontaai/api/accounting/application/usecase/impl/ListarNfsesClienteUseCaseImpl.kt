package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.dto.NfseDto
import br.com.mecontaai.api.accounting.application.usecase.ListarNfsesClienteUseCase
import br.com.mecontaai.api.accounting.domain.entity.Nfse
import br.com.mecontaai.api.accounting.domain.service.NfseService
import org.springframework.stereotype.Component

/**
 * Implementação do caso de uso para listar NFS-es de um cliente.
 */
@Component
class ListarNfsesClienteUseCaseImpl(
    private val nfseService: NfseService
) : ListarNfsesClienteUseCase {

    /**
     * Lista todas as NFS-es de um cliente.
     *
     * @param customerId ID do cliente
     * @return lista de NFS-es do cliente
     */
    override fun execute(customerId: Long): List<NfseDto> {
        val nfses = nfseService.getNfsesByCustomer(customerId)
        
        return nfses.map { mapToDto(it) }
    }
    
    private fun mapToDto(nfse: Nfse): NfseDto {
        return NfseDto(
            id = nfse.id,
            uuid = nfse.uuid.toString(),
            companyId = nfse.companyId,
            customerId = nfse.customerId,
            description = nfse.description,
            amount = nfse.amount,
            status = nfse.status,
            externalProcessId = nfse.externalProcessId,
            createdAt = nfse.createdAt,
            updatedAt = nfse.updatedAt
        )
    }
} 