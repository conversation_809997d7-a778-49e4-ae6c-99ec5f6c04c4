package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.application.dto.NfseDto

/**
 * Interface para o caso de uso de obter status de NFS-e.
 */
interface ObterStatusNfseUseCase {
    
    /**
     * Obtém o status atual de uma NFS-e.
     *
     * @param reference referência única da NFS-e
     * @return dados atualizados da NFS-e
     */
    fun execute(reference: String): NfseDto
} 