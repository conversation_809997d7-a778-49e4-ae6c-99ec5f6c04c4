package br.com.mecontaai.api.accounting.infrastructure.webhook.model

import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse

/**
 * Representa o resultado do processamento de uma interação.
 */
sealed class ProcessingResult {
    
    /**
     * Processamento bem-sucedido com respostas para enviar ao usuário.
     */
    data class Success(val responses: List<AgentResponse>) : ProcessingResult()
    
    /**
     * Processamento falhou, mas há uma mensagem de fallback.
     */
    data class Fallback(val message: String) : ProcessingResult()
    
    /**
     * Erro durante o processamento.
     */
    data class Error(val error: String, val cause: Throwable? = null) : ProcessingResult()
    
    companion object {
        fun success(responses: List<AgentResponse>) = Success(responses)
        
        fun success(response: AgentResponse) = Success(listOf(response))
        
        fun fallback(message: String) = Fallback(message)
        
        fun error(error: String, cause: Throwable? = null) = Error(error, cause)
    }
}
