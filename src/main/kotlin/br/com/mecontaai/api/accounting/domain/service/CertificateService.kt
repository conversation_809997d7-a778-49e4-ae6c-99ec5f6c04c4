package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.entity.Certificate
import java.util.UUID

/**
 * Service interface for Certificate operations.
 */
interface CertificateService {
    /**
     * Creates a new certificate.
     *
     * @param certificate The certificate to create
     * @param file The certificate file content
     * @param filename The name of the certificate file
     * @return The created certificate
     */
    fun createCertificate(certificate: Certificate, file: ByteArray, filename: String): Certificate
    
    /**
     * Finds a certificate by ID.
     *
     * @param id The certificate ID
     * @return The certificate if found, null otherwise
     */
    fun findById(id: Long): Certificate?
    
    /**
     * Finds a certificate by UUID.
     *
     * @param uuid The certificate UUID
     * @return The certificate if found, null otherwise
     */
    fun findByUuid(uuid: UUID): Certificate?
    
    /**
     * Finds the active certificate for a company.
     *
     * @param companyId The company ID
     * @return The active certificate if found, null otherwise
     */
    fun findActiveByCompanyId(companyId: Long): Certificate?
    
    /**
     * Finds all certificates for a specific company.
     *
     * @param companyId The company ID
     * @return List of certificates belonging to the company
     */
    fun findByCompanyId(companyId: Long): List<Certificate>
    
    /**
     * Finds a certificate by blob UUID.
     *
     * @param blobUuid The blob UUID
     * @return The certificate if found, null otherwise
     */
    fun findByBlobUuid(blobUuid: String): Certificate?
    
    /**
     * Gets the certificate file content.
     *
     * @param id The certificate ID
     * @return The certificate file content as byte array
     * @throws IllegalArgumentException if the certificate is not found
     */
    fun getFile(id: Long): ByteArray
    
    /**
     * Deactivates all certificates for a company and activates the specified one.
     *
     * @param certificateId The certificate ID to activate
     * @param companyId The company ID
     * @return The activated certificate
     */
    fun activateCertificate(certificateId: Long, companyId: Long): Certificate?
    
    /**
     * Deactivates a certificate.
     *
     * @param certificateId The certificate ID to deactivate
     * @return The deactivated certificate
     */
    fun deactivateCertificate(certificateId: Long): Certificate?
    
    /**
     * Checks if a company has an active certificate.
     *
     * @param companyId The company ID
     * @return true if the company has an active certificate, false otherwise
     */
    fun hasActiveCertificate(companyId: Long): Boolean
    
    /**
     * Updates the expiration date of a certificate.
     *
     * @param certificateId The certificate ID
     * @param expirationDate The new expiration date
     * @return The updated certificate
     */
    fun updateExpirationDate(certificateId: Long, expirationDate: java.time.LocalDate): Certificate?
} 