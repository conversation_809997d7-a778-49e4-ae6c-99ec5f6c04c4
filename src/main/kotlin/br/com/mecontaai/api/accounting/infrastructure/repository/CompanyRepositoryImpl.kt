package br.com.mecontaai.api.accounting.infrastructure.repository

import br.com.mecontaai.api.accounting.domain.entity.Company
import br.com.mecontaai.api.accounting.domain.repository.CompanyRepository
import br.com.mecontaai.api.accounting.infrastructure.database.model.CompanyEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CompanyEntityRepository
import org.slf4j.LoggerFactory
import org.springframework.core.env.Environment
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Implementation of CompanyRepository using JPA.
 */
@Repository
class CompanyRepositoryImpl(
    private val companyEntityRepository: CompanyEntityRepository,
    private val environment: Environment
) : CompanyRepository {

    private val logger = LoggerFactory.getLogger(javaClass)

    /**
     * Save a company to the database.
     *
     * @param company The company to save
     * @return The saved company with generated ID
     */
    override fun save(company: Company): Company {
        logger.info("Iniciando o salvamento da Company: ${company}")

        val companyEntity = mapToEntity(company)
        logger.debug("Company Entity mapeada: $companyEntity")

        val savedEntity = companyEntityRepository.save(companyEntity)
        logger.info("Empresa salva com sucesso: ID=${savedEntity.id}")
        return mapToDomain(savedEntity)
    }

    /**
     * Find a company by ID.
     *
     * @param id The company ID
     * @return The company if found, null otherwise
     */
    override fun findById(id: Long): Company? {
        return companyEntityRepository.findById(id).map { mapToDomain(it) }.orElse(null)
    }

    /**
     * Find a company by UUID.
     *
     * @param uuid The company UUID
     * @return The company if found, null otherwise
     */
    override fun findByUuid(uuid: UUID): Company? {
        return companyEntityRepository.findByUuid(uuid).map { mapToDomain(it) }.orElse(null)
    }

    /**
     * Find a company by document (CNPJ).
     *
     * @param document The company document (CNPJ)
     * @return The company if found, null otherwise
     */
    override fun findByDocument(document: String): Company? {
        return companyEntityRepository.findByDocument(document).map { mapToDomain(it) }.orElse(null)
    }

    /**
     * Check if a company with the given document (CNPJ) exists.
     *
     * @param document The document (CNPJ) to check
     * @return true if a company with the document exists, false otherwise
     */
    override fun existsByDocument(document: String): Boolean {
        return companyEntityRepository.existsByDocument(document)
    }

    /**
     * Find all companies.
     *
     * @return List of all companies
     */
    override fun findAll(): List<Company> {
        return companyEntityRepository.findAll().map { mapToDomain(it) }
    }

    /**
     * Map a domain Company to a database CompanyEntity.
     */
    private fun mapToEntity(company: Company): CompanyEntity {
        return CompanyEntity(
            name = company.name,
            document = company.document,
            municipalRegistration = company.municipalRegistration,
            stateRegistration = company.stateRegistration,
            simplesNacional = company.simplesNacional,
            codigoMunicipio = company.codigoMunicipio,
            codigoSiafi = company.codigoSiafi,
            codigoIbge = company.codigoIbge,
            naturezaJuridica = company.naturezaJuridica,
            focusId = company.focusId
        )
        // Os campos de BaseEntity (id, uuid, isActive, createdAt, updatedAt) 
        // são inicializados automaticamente na construção da CompanyEntity
    }

    /**
     * Map a database CompanyEntity to a domain Company.
     */
    private fun mapToDomain(entity: CompanyEntity): Company {
        val isProduction = environment.activeProfiles.contains("production")
        val token = if (isProduction) entity.tokenProducao else entity.tokenHomologacao
        
        return Company(
            id = entity.id,
            uuid = entity.uuid,
            name = entity.name,
            document = entity.document,
            municipalRegistration = entity.municipalRegistration,
            stateRegistration = entity.stateRegistration,
            simplesNacional = entity.simplesNacional,
            codigoMunicipio = entity.codigoMunicipio,
            codigoSiafi = entity.codigoSiafi,
            codigoIbge = entity.codigoIbge,
            naturezaJuridica = entity.naturezaJuridica,
            token = token,
            focusId = entity.focusId,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
} 