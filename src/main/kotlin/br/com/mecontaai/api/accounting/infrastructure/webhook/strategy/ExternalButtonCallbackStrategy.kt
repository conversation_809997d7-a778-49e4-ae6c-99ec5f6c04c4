package br.com.mecontaai.api.accounting.infrastructure.webhook.strategy

import br.com.mecontaai.api.accounting.domain.service.callback.ButtonCallbackService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.webhook.model.InteractivePayload
import br.com.mecontaai.api.accounting.infrastructure.webhook.model.ProcessingResult
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Estratégia para processar cliques em botões usando external message ID.
 * Esta é a nova abordagem que usa o context.id do webhook para recuperar callbacks.
 */
@Component
class ExternalButtonCallbackStrategy(
    private val buttonCallbackService: ButtonCallbackService
) : InteractionStrategy {
    
    private val logger = LoggerFactory.getLogger(javaClass)
    
    override fun getInteractionType(): String = "button_callback_external"
    
    override fun canHandle(interactionType: String): Boolean = 
        interactionType == "button_callback_external"
    
    override fun process(interaction: InteractivePayload, chat: ChatEntity): ProcessingResult {
        logger.info("🔘 Processando button callback via external message ID")
        logger.info("📦 Payload: ${interaction.payload}")
        logger.info("💬 Chat: ${chat.uuid}")
        logger.info("📋 Metadata: ${interaction.metadata}")
        
        return try {
            // Parse do payload (formato: externalMessageId:clickedValue)
            val (externalMessageId, clickedValue) = parseCallbackPayload(interaction.payload)
            
            logger.info("🆔 External Message ID: $externalMessageId")
            logger.info("👆 Clicked Value: $clickedValue")
            
            // Executa o callback usando external message ID
            val result = buttonCallbackService.executeCallbackByExternalMessageId(
                externalMessageId, 
                clickedValue, 
                chat
            )
            
            if (result != null) {
                logger.info("✅ Callback executado com sucesso via external message ID")
                val responses = result.agentResponses ?: emptyList()
                ProcessingResult.success(responses)
            } else {
                logger.warn("⚠️ Callback não encontrado ou expirado para external message ID: $externalMessageId")
                ProcessingResult.fallback("Esta ação não está mais disponível.")
            }
            
        } catch (e: Exception) {
            logger.error("❌ Erro ao processar button callback via external message ID", e)
            ProcessingResult.error("Erro ao processar clique do botão", e)
        }
    }
    
    /**
     * Faz o parse do payload do callback.
     * Formato esperado: "externalMessageId:clickedValue"
     */
    private fun parseCallbackPayload(payload: String): Pair<String, String> {
        val parts = payload.split(":", limit = 2)
        
        if (parts.size != 2) {
            throw IllegalArgumentException("Formato de payload inválido. Esperado: 'externalMessageId:clickedValue', recebido: '$payload'")
        }
        
        return Pair(parts[0], parts[1])
    }
}
