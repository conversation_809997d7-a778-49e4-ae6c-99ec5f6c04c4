package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Request DTO for creating an NFSe (Services Invoice).
 *
 * This DTO follows the exact structure required by the Focus NFSe API.
 * See the API documentation for detailed information on each field.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class NfseEnvioRequestDto(
    /**
     * Date and time of NFSe issuance in ISO format
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val data_emissao: LocalDateTime,

    /**
     * Service provider information
     */
    val prestador: PrestadorDto,

    /**
     * Service recipient information
     */
    val tomador: TomadorDto,

    /**
     * Service details
     */
    val servico: ServicoDto,

    /**
     * Nature of the operation (1-6)
     * 1 = Tributação no município
     * 2 = Tributação fora do município
     * 3 = Isenção
     * 4 = Imune
     * 5 = Exigibilidade suspensa por decisão judicial
     * 6 = Exigibilidade suspensa por procedimento administrativo
     */
    val natureza_operacao: Int? = null,

    /**
     * Indicates if the service provider is under Simples Nacional tax regime
     */
    val optante_simples_nacional: Boolean? = null,

    /**
     * Indicates if the service provider is incentivized by cultural incentives
     */
    val incentivador_cultural: Boolean? = null,

    /**
     * RPS (Recibo Provisório de Serviço) serial number
     */
    val numero_rps: String? = null,

    /**
     * RPS series
     */
    val serie_rps: String? = null,

    /**
     * RPS type
     * 1 = RPS
     * 2 = Nota Fiscal Conjugada (Mista)
     * 3 = Cupom
     */
    val tipo_rps: Int? = null,

    /**
     * Competency period (Month/Year) in which the service was provided
     */
    @JsonFormat(pattern = "yyyy-MM")
    val competencia: String? = null,

    /**
     * Special taxation regime
     * 1 = Microempresa municipal
     * 2 = Estimativa
     * 3 = Sociedade de profissionais
     * 4 = Cooperativa
     * 5 = MEI – Simples Nacional
     * 6 = ME EPP – Simples Nacional
     */
    val regime_especial_tributacao: Int? = null
)

/**
 * Service provider information
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class PrestadorDto(
    /**
     * CNPJ of the service provider
     */
    val cnpj: String,
    
    /**
     * Municipal registration number
     */
    val inscricao_municipal: String,
    
    /**
     * Municipality code (IBGE 7-digit code)
     */
    val codigo_municipio: String
)

/**
 * Service recipient information
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class TomadorDto(
    /**
     * CNPJ of the service recipient (required if CPF not provided)
     */
    val cnpj: String? = null,

    /**
     * CPF of the service recipient (required if CNPJ not provided)
     */
    val cpf: String? = null,

    /**
     * Legal name of the service recipient
     */
    val razao_social: String? = null,

    /**
     * Email for sending NFSe copy
     */
    val email: String? = null,

    /**
     * Recipient's address
     */
    val endereco: EnderecoDto? = null,

    /**
     * Recipient's phone number
     */
    val telefone: String? = null
)

/**
 * Address information
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class EnderecoDto(
    /**
     * Street name
     */
    val logradouro: String? = null,
    
    /**
     * Street number
     */
    val numero: String? = null,
    
    /**
     * Additional address info
     */
    val complemento: String? = null,
    
    /**
     * Neighborhood
     */
    val bairro: String? = null,
    
    /**
     * Municipality code (IBGE 7-digit code)
     */
    val codigo_municipio: String? = null,
    
    /**
     * State abbreviation
     */
    val uf: String? = null,
    
    /**
     * ZIP/Postal code
     */
    val cep: String? = null
)

/**
 * Service details
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class ServicoDto(
    /**
     * Value of the service
     */
    val valor_servicos: BigDecimal,
    
    /**
     * Whether ISS tax was withheld by the recipient
     */
    @JsonProperty("iss_retido")
    val issRetido: Boolean,
    
    /**
     * Service code according to LC 116/2003
     */
    val item_lista_servico: String,
    
    /**
     * Service description
     */
    val discriminacao: String,
    
    /**
     * Municipality code where service was provided
     */
    val codigo_municipio: String? = null,
    
    /**
     * Municipal tax code (specific to each municipality)
     */
    val codigo_tributario_municipio: String? = null,
    
    /**
     * ISS tax rate (e.g., 3.0 for 3%)
     */
    val aliquota: Double? = null,
    
    /**
     * CNAE code
     */
    val codigo_cnae: String? = null,
    
    /**
     * Value of deductions
     */
    val valor_deducoes: Double? = null,
    
    /**
     * Value of social security contribution (INSS)
     */
    val valor_inss: Double? = null,
    
    /**
     * Value of income tax (IR)
     */
    val valor_ir: Double? = null,
    
    /**
     * Value of Contribution on Gross Revenue (COFINS)
     */
    val valor_cofins: Double? = null,
    
    /**
     * Value of contribution to CSLL
     */
    val valor_csll: Double? = null,
    
    /**
     * Value of PIS/PASEP contribution
     */
    val valor_pis: Double? = null,
    
    /**
     * Value of ISS tax
     */
    val valor_iss: Double? = null,
    
    /**
     * Value of ISS tax withheld
     */
    val valor_iss_retido: Double? = null,
    
    /**
     * Other withholdings
     */
    val outras_retencoes: Double? = null,
    
    /**
     * Discount value (unconditional)
     */
    val desconto_incondicionado: Double? = null,
    
    /**
     * Discount value (conditional)
     */
    val desconto_condicionado: Double? = null,
    
    /**
     * Net amount due
     */
    val valor_liquido: Double? = null
)