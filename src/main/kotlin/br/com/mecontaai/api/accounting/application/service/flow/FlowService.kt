package br.com.mecontaai.api.accounting.application.service.flow

import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import org.springframework.stereotype.Service

/**
 * Main service for processing WhatsApp flows.
 * Now uses FlowRouter to route requests to appropriate flow handlers.
 */
@Service
class FlowService(
    private val flowRouter: FlowRouter
) {
    /**
     * Process a flow request by routing it to the appropriate handler
     */
    fun processFlow(request: FlowRequest): FlowResponse {
        return flowRouter.route(request)
    }
}
