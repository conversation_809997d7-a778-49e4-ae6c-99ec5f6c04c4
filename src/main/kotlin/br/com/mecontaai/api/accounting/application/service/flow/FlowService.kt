package br.com.mecontaai.api.accounting.application.service.flow

import br.com.mecontaai.api.accounting.domain.service.flow.FlowActionHandler
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import org.springframework.stereotype.Service

@Service
class FlowService(
    // Injeta todas as implementações de FlowActionHandler disponíveis
    handlers: List<FlowActionHandler>,
) {
    // Mapeia action -> handler apropriado
    private val handlerMap: Map<String, FlowActionHandler> =
        handlers.associateBy { it.actionName }

    fun processFlow(request: FlowRequest): FlowResponse {
        val action = request.action.lowercase() // normalizar
        val handler = handlerMap[action] ?: throw IllegalArgumentException("Ação desconhecida: $action")
        return handler.handle(request)
    }
}
