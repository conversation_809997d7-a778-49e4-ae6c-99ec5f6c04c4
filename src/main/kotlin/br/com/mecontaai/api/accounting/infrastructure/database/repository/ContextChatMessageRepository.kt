package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.ContextChatMessage
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ContextChatMessageRepository : JpaRepository<ContextChatMessage, Long> {

    fun findByUuid(uuid: UUID): ContextChatMessage?
    fun findByChatIdOrderByCreatedAtDesc(chatId: Long, pageable: Pageable): List<ContextChatMessage>
}