package br.com.mecontaai.api.accounting.application.dto

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Request para emissão de NFS-e.
 */
data class EmitirNfseRequest(
    /**
     * ID da empresa emissora (prestador do serviço)
     */
    val companyId: Long,

    /**
     * ID do cliente/tomador do serviço
     */
    val customerId: Long,

    /**
     * Descrição do serviço prestado
     */
    val discriminacao: String,

    /**
     * Código do item da lista de serviços
     */
    val itemListaServico: String,

    /**
     * Valor do serviço
     */
    val valorServico: BigDecimal
)

/**
 * Application DTO for submitting an NFSe (Services Invoice).
 */
data class CreateNfseDto(
    /**
     * Customer ID (service recipient)
     */
    val customerId: Long,

    /**
     * Value of the service
     */
    val valorServico: BigDecimal,

    /**
     * Service description
     */
    val discriminacao: String,

    /**
     * Date and time of NFSe issuance
     */
    val dataEmissao: LocalDateTime = LocalDateTime.now(),

    /**
     * Service code according to LC 116/2003
     */
    val itemListaServico: String,

    /**
     * Whether ISS tax is retained
     */
    val issRetido: Boolean = false,

    /**
     * ISS tax rate
     */
    val aliquota: Double? = null,

    /**
     * Municipal tax code
     */
    val codigoTributarioMunicipio: String? = null
)

/**
 * Application DTO for canceling an NFSe.
 */
data class CancelNfseDto(
    /**
     * NFSe reference
     */
    val reference: String,
    
    /**
     * Justification for cancellation
     */
    val justificativa: String
)

/**
 * Application DTO for sending an NFSe via email.
 */
data class SendNfseEmailDto(
    /**
     * NFSe reference
     */
    val reference: String,
    
    /**
     * List of email addresses
     */
    val emails: List<String>
)

/**
 * Application DTO for representing an NFSe.
 */
data class NfseDto(
    /**
     * ID único da NFSe
     */
    val id: Long?,
    
    /**
     * UUID da NFSe
     */
    val uuid: String,
    
    /**
     * ID da empresa
     */
    val companyId: Long,
    
    /**
     * ID do cliente
     */
    val customerId: Long,
    
    /**
     * Descrição do serviço
     */
    val description: String,
    
    /**
     * Valor do serviço
     */
    val amount: BigDecimal,
    
    /**
     * Status atual da NFSe
     */
    val status: String,
    
    /**
     * ID do processo externo (Focus NFe)
     */
    val externalProcessId: String?,
    
    /**
     * Data de criação
     */
    val createdAt: LocalDateTime,
    
    /**
     * Data de atualização
     */
    val updatedAt: LocalDateTime,
    
    /**
     * UUID do blob contendo o arquivo XML da NFSe
     */
    val xmlBlobUuid: String? = null,
    
    /**
     * UUID do blob contendo o arquivo PDF/DANFSE da NFSe
     */
    val pdfBlobUuid: String? = null
)