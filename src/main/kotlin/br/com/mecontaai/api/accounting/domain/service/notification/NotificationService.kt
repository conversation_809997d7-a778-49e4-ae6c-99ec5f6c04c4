package br.com.mecontaai.api.accounting.domain.service.notification

/**
 * Serviço de domínio para envio de notificações.
 * Abstrai a implementação específica de notificação (Slack, email, etc.)
 */
interface NotificationService {
    /**
     * Envia uma notificação de suporte quando um usuário precisa de ajuda humana.
     *
     * @param userPhone Telefone do usuário que precisa de ajuda
     * @param reason Motivo da solicitação de ajuda (opcional)
     * @param chatUuid UUID do chat
     * @param userName Nome do usuário (se disponível)
     */
    fun sendSupportNotification(
        userPhone: String,
        reason: String?,
        chatUuid: String,
        userName: String? = null,
    )

    /**
     * Envia uma notificação genérica.
     *
     * @param message Mensagem a ser enviada
     * @param channel Canal de destino (opcional, usa padrão se não especificado)
     */
    fun sendNotification(
        message: String,
        channel: String,
    )
}
