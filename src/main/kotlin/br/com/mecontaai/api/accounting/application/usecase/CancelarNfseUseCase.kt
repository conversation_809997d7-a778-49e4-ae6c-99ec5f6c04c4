package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.application.dto.NfseDto

/**
 * Interface para o caso de uso de cancelar NFS-e.
 */
interface CancelarNfseUseCase {
    
    /**
     * Cancela uma NFS-e autorizada.
     *
     * @param request dados para cancelamento
     * @return dados da NFS-e cancelada
     */
    fun execute(request: CancelarNfseRequest): NfseDto
}

/**
 * Request para cancelamento de NFS-e.
 */
data class CancelarNfseRequest(
    /**
     * ID da empresa
     */
    val companyId: Long,
    
    /**
     * Referência única da NFS-e
     */
    val reference: String,
    
    /**
     * Justificativa para o cancelamento
     */
    val justificativa: String
) 