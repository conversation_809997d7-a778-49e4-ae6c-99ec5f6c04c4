package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.infrastructure.client.whatsapp.dto.WhatsAppInteractiveMessageDto

/**
 * Interface para serviços de envio de mensagens.
 * Cada implementação representa um provedor de mensagens diferente (WhatsApp, SMS, Email, etc).
 */
interface MessageSenderService {
    /**
     * Envia uma mensagem de texto.
     *
     * @param to Número do destinatário no formato internacional (ex: +*************)
     * @param text Texto da mensagem
     * @return ID da mensagem no provedor externo
     */
    fun sendTextMessage(to: String, text: String): String

    /**
     * Envia uma mensagem com imagem usando o ID da imagem.
     *
     * @param to Número do destinatário no formato internacional (ex: +*************)
     * @param imageId ID da imagem no provedor externo
     * @param caption Legenda da imagem (opcional)
     * @return ID da mensagem no provedor externo
     */
    fun sendImageMessage(to: String, imageId: String, caption: String? = null): String

    /**
     * Envia uma mensagem com documento usando a URL do documento.
     *
     * @param to Número do destinatário no formato internacional (ex: +*************)
     * @param documentUrl URL do documento
     * @param caption Legenda do documento (opcional)
     * @return ID da mensagem no provedor externo
     */
    fun sendDocumentMessage(to: String, documentUrl: String, caption: String? = null): String

    /**
     * Envia uma mensagem com imagem usando a URL da imagem.
     *
     * @param to Número do destinatário no formato internacional (ex: +*************)
     * @param imageUrl URL da imagem
     * @param caption Legenda da imagem (opcional)
     * @return ID da mensagem no provedor externo
     */
    fun sendImageMessageWithUrl(to: String, imageUrl: String, caption: String? = null): String

    /**
     * Envia uma mensagem interativa para um destinatário específico.
     *
     * @param to O identificador do destinatário (ex.: número de telefone).
     * @param payload O payload contendo informações da mensagem interativa.
     * @return O identificador único da mensagem gerado pelo provedor externo.
     */
    fun sendInteractiveMessage(to: String, payload: WhatsAppInteractiveMessageDto): String
} 