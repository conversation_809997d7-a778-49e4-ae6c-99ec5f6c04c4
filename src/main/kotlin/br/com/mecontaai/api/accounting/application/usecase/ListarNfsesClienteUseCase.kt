package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.application.dto.NfseDto

/**
 * Interface para o caso de uso de listar NFS-es de um cliente.
 */
interface ListarNfsesClienteUseCase {
    
    /**
     * Lista todas as NFS-es de um cliente.
     *
     * @param customerId ID do cliente
     * @return lista de NFS-es do cliente
     */
    fun execute(customerId: Long): List<NfseDto>
} 