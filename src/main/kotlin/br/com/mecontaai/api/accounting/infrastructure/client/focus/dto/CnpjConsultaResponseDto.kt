package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO para a resposta da consulta de CNPJ na API do Focus.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class CnpjConsultaResponseDto(
    @JsonProperty("cnpj")
    val cnpj: String,

    @JsonProperty("razao_social")
    val razaoSocial: String,

    @JsonProperty("endereco")
    val endereco: CnpjEnderecoDto,

    @JsonProperty("cnae_principal")
    val cnaePrincipal: String,

    @JsonProperty("cnaes_secundarios")
    val cnaesSecundarios: List<String>? = null,

    @JsonProperty("data_abertura")
    val dataAbertura: String? = null,

    @JsonProperty("situacao_cadastral")
    val situacaoCadastral: String,

    @JsonProperty("optante_simples_nacional")
    val isOptanteSimplesNacional: Boolean = false,

    @JsonProperty("optante_mei")
    val isOptanteMei: Boolean = false,

    @JsonProperty("telefones")
    val telefones: List<String>? = null,

    @JsonProperty("email")
    val email: String? = null
)

/**
 * DTO para o endereço na resposta da consulta de CNPJ.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class CnpjEnderecoDto(
    @JsonProperty("logradouro")
    val logradouro: String,

    @JsonProperty("numero")
    val numero: String,

    @JsonProperty("complemento")
    val complemento: String = "",

    @JsonProperty("bairro")
    val bairro: String,

    @JsonProperty("cep")
    val cep: String,

    @JsonProperty("uf")
    val uf: String,

    @JsonProperty("nome_municipio")
    val nomeMunicipio: String,

    @JsonProperty("codigo_municipio")
    val codigoMunicipio: String? = null,

    @JsonProperty("codigo_siafi")
    val codigoSiafi: String? = null,

    @JsonProperty("codigo_ibge")
    val codigoIbge: String? = null
) 