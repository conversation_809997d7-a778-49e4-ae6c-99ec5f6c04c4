package br.com.mecontaai.api.accounting.application.dto

import br.com.mecontaai.api.accounting.domain.enum.DocumentType

/**
 * DTO para representar um tipo de documento.
 */
data class DocumentTypeDto(
    /**
     * Código do tipo de documento (ex: "CPF", "CNPJ")
     */
    val code: String,
    
    /**
     * Descrição do tipo de documento
     */
    val description: String
) {
    companion object {
        /**
         * Cria um DocumentTypeDto a partir de um DocumentType.
         *
         * @param documentType O DocumentType para converter
         * @return O DocumentTypeDto criado
         */
        fun fromDocumentType(documentType: DocumentType): DocumentTypeDto {
            return DocumentTypeDto(
                code = documentType.code,
                description = documentType.description
            )
        }
        
        /**
         * Cria um DocumentTypeDto a partir de um código.
         *
         * @param code O código para buscar
         * @return O DocumentTypeDto criado ou null se o código for inválido
         */
        fun fromCode(code: String): DocumentTypeDto? {
            val documentType = DocumentType.fromCode(code) ?: return null
            return fromDocumentType(documentType)
        }
    }
} 