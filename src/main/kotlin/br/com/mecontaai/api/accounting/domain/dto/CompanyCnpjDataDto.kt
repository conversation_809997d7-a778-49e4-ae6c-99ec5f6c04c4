package br.com.mecontaai.api.accounting.domain.dto

import br.com.mecontaai.api.accounting.domain.enum.NaturezaJuridicaEnum

/**
 * DTO contendo os dados essenciais de um CNPJ.
 * Este DTO é independente da fonte de dados (API ReceitaWS, Focus, etc.).
 */
data class CompanyCnpjDataDto(
    val cnpj: String,
    val razaoSocial: String,
    val nomeFantasia: String?,
    val simplesNacional: Boolean,
    val situacao: String,
    val dataSituacao: String,
    val capitalSocial: String?,
    val naturezaJuridica: NaturezaJuridicaEnum?,
    // Endereço
    val cep: String,
    val logradouro: String,
    val numero: String,
    val complemento: String?,
    val bairro: String,
    val municipio: String,
    val codigoMunicipio: String?,
    val uf: String,
    // CNAE
    val cnaeCodigoPrincipal: String,
    val cnaeDescricaoPrincipal: String,
    // QSA (Quadro de Sócios e Administradores)
    val quantidadeSocios: Int,
    val possuiSocioPessoaJuridica: Boolean,
)
