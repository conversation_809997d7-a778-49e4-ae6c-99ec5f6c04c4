package br.com.mecontaai.api.accounting.application.dto

import java.time.LocalDateTime
import java.util.UUID

/**
 * DTO for creating a new user.
 */
data class CreateUserDto(
    /**
     * User's full name.
     */
    val name: String,
    
    /**
     * User's email.
     */
    val email: String,
    
    /**
     * User's password (plain text).
     */
    val password: String,
    
    /**
     * User's document (CPF).
     */
    val document: String? = null
)

/**
 * DTO representing a user.
 */
data class UserDto(
    /**
     * User's ID.
     */
    val id: Long?,
    
    /**
     * User's UUID.
     */
    val uuid: UUID,
    
    /**
     * User's full name.
     */
    val name: String,
    
    /**
     * User's email.
     */
    val email: String,
    
    /**
     * User's document (CPF).
     */
    val document: String?,
    
    /**
     * Whether the user is active.
     */
    val isActive: Boolean,
    
    /**
     * When the user was created.
     */
    val createdAt: LocalDateTime,
    
    /**
     * When the user was last updated.
     */
    val updatedAt: LocalDateTime
) 