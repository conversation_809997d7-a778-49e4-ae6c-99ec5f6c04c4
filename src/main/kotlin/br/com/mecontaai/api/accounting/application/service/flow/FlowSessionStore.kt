package br.com.mecontaai.api.accounting.application.service.flow

import org.springframework.stereotype.Component
import java.util.concurrent.ConcurrentHashMap

/**
 * Simple in-memory session storage for WhatsApp flows.
 * Stores data between screens for each flow session.
 */
@Component
class FlowSessionStore {
    private val sessions = ConcurrentHashMap<String, MutableMap<String, Any>>()
    
    /**
     * Get a value from session
     */
    fun get(sessionId: String, key: String): Any? {
        return sessions[sessionId]?.get(key)
    }
    
    /**
     * Put a value in session
     */
    fun put(sessionId: String, key: String, value: Any) {
        sessions.computeIfAbsent(sessionId) { mutableMapOf() }[key] = value
    }
    
    /**
     * Get all session data
     */
    fun getAll(sessionId: String): Map<String, Any> {
        return sessions[sessionId] ?: emptyMap()
    }
    
    /**
     * Clear session data
     */
    fun clear(sessionId: String) {
        sessions.remove(sessionId)
    }
    
    /**
     * Check if session exists
     */
    fun exists(sessionId: String): Boolean {
        return sessions.contains<PERSON>ey(sessionId)
    }
}
