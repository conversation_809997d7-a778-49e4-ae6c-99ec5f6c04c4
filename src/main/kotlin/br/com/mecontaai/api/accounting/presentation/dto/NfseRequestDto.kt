package br.com.mecontaai.api.accounting.presentation.dto

import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Presentation DTO for NFSe creation request.
 */
data class NfseRequestDto(
    /**
     * Company ID (service provider)
     */
    @field:NotNull(message = "Company ID is required")
    val companyId: Long,
    
    /**
     * Customer ID (service recipient)
     */
    @field:NotNull(message = "Customer ID is required")
    val customerId: Long,
    
    /**
     * Value of the service
     */
    @field:NotNull(message = "Service value is required")
    @field:Positive(message = "Service value must be positive")
    val valorServico: BigDecimal,
    
    /**
     * Service description
     */
    @field:NotBlank(message = "Service description is required")
    val discriminacao: String,
    
    /**
     * Date and time of NFSe issuance
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    val dataEmissao: LocalDateTime? = null,
    
    /**
     * Service code according to LC 116/2003
     */
    @field:NotBlank(message = "Service code is required")
    val itemListaServico: String,
    
    /**
     * Whether ISS tax is retained
     */
    val issRetido: Boolean = false,
    
    /**
     * ISS tax rate
     */
    val aliquota: Double? = null,
    
    /**
     * Municipal tax code
     */
    val codigoTributarioMunicipio: String? = null
)

/**
 * Presentation DTO for NFSe cancellation request.
 */
data class NfseCancelRequestDto(
    /**
     * Company ID (service provider)
     */
    @field:NotNull(message = "Company ID is required")
    val companyId: Long,
    /**
     * Justification for cancellation
     */
    @field:NotBlank(message = "Justification is required")
    val justificativa: String
)

/**
 * Presentation DTO for NFSe email request.
 */
data class NfseEmailRequestDto(
    /**
     * Company ID (service provider)
     */
    @field:NotNull(message = "Company ID is required")
    val companyId: Long,
    /**
     * List of email addresses
     */
    @field:NotNull(message = "Email list is required")
    val emails: List<String>
)