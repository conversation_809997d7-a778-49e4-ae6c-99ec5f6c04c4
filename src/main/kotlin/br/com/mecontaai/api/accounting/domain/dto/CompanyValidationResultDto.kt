package br.com.mecontaai.api.accounting.domain.dto

import br.com.mecontaai.api.accounting.domain.enum.CompanyValidationErrorType

/**
 * DTO para o resultado da validação de uma empresa.
 *
 * @property isValid Indica se a empresa é válida para criação
 * @property errorType Tipo de erro caso a validação falhe (null se isValid for true)
 * @property errorMessage Mensagem de erro caso a validação falhe (null se isValid for true)
 * @property cnpjData Dados do CNPJ validado (populado apenas se isValid for true)
 */
data class CompanyValidationResultDto(
    val isValid: Boolean,
    val errorType: CompanyValidationErrorType? = null,
    val errorMessage: String? = null,
) {
    companion object {
        /**
         * Cria um resultado de validação de sucesso.
         */
        fun valid(cnpjData: CompanyCnpjDataDto): CompanyValidationResultDto {
            return CompanyValidationResultDto(
                isValid = true,
                cnpjData = cnpjData
            )
        }

        /**
         * Cria um resultado de validação com erro.
         */
        fun invalid(errorType: CompanyValidationErrorType, errorMessage: String): CompanyValidationResultDto {
            return CompanyValidationResultDto(
                isValid = false,
                errorType = errorType,
                errorMessage = errorMessage
            )
        }
    }
} 