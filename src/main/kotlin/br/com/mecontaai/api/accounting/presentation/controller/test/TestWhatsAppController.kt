package br.com.mecontaai.api.accounting.presentation.controller.test

import br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.WhatsAppClient
import br.com.mecontaai.api.accounting.presentation.dto.request.WhatsAppImageMessageRequest
import br.com.mecontaai.api.accounting.presentation.dto.request.WhatsAppInteractiveMessageRequest
import br.com.mecontaai.api.accounting.presentation.dto.request.WhatsAppTextMessageRequest
import br.com.mecontaai.api.accounting.presentation.dto.response.WhatsAppMessageResponse
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * Controller para endpoints relacionados ao WhatsApp.
 */
@RestController
@RequestMapping("/api/v1/whatsapp")
class TestWhatsAppController(
    private val whatsAppClient: WhatsAppClient,
) {
    /**
     * Endpoint para enviar mensagem de texto via WhatsApp.
     *
     * @param request Dados da mensagem a ser enviada
     * @return Resposta da API do WhatsApp
     */
    @PostMapping("/messages/text")
    fun sendTextMessage(
        @Valid @RequestBody request: WhatsAppTextMessageRequest,
    ): ResponseEntity<WhatsAppMessageResponse> {
        val response =
            whatsAppClient.sendMessage(
                phoneNumberId = request.metaPhoneNumberId,
                to = request.to,
                text = request.message,
            )

        val camelCaseResponse =
            WhatsAppMessageResponse(
                messagingProduct = response.messagingProduct,
                contacts =
                    response.contacts.map { contact ->
                        WhatsAppMessageResponse.WhatsAppContactResponse(
                            input = contact.input,
                            waId = contact.waId,
                        )
                    },
                messages =
                    response.messages.map { message ->
                        WhatsAppMessageResponse.WhatsAppMessageResponse(
                            id = message.id,
                        )
                    },
            )

        return ResponseEntity.ok(camelCaseResponse)
    }

    /**
     * Endpoint para enviar mensagem com imagem via WhatsApp.
     *
     * @param request Dados da mensagem com imagem a ser enviada
     * @return Resposta da API do WhatsApp
     */
    @PostMapping("/messages/image")
    fun sendImageMessage(
        @Valid @RequestBody request: WhatsAppImageMessageRequest,
    ): ResponseEntity<WhatsAppMessageResponse> {
        require(request.image != null) { "Os dados da imagem são obrigatórios" }
        require(request.image.link != null || request.image.id != null) { "É necessário fornecer um id ou link para a imagem" }

        val response =
            if (request.image.link != null) {
                whatsAppClient.sendImageMessageWithUrl(
                    phoneNumberId = request.metaPhoneNumberId,
                    to = request.to,
                    imageUrl = request.image.link,
                    caption = request.image.caption,
                )
            } else {
                whatsAppClient.sendImageMessage(
                    phoneNumberId = request.metaPhoneNumberId,
                    to = request.to,
                    imageId = request.image.id!!,
                    caption = request.image.caption,
                )
            }

        val camelCaseResponse =
            WhatsAppMessageResponse(
                messagingProduct = response.messagingProduct,
                contacts =
                    response.contacts.map { contact ->
                        WhatsAppMessageResponse.WhatsAppContactResponse(
                            input = contact.input,
                            waId = contact.waId,
                        )
                    },
                messages =
                    response.messages.map { message ->
                        WhatsAppMessageResponse.WhatsAppMessageResponse(
                            id = message.id,
                        )
                    },
            )

        return ResponseEntity.ok(camelCaseResponse)
    }

    /**
     * Endpoint para enviar mensagem interativa via WhatsApp.
     *
     * @param request Dados da mensagem interativa a ser enviada
     * @return Resposta da API do WhatsApp
     */
    @PostMapping("/messages/interactive")
    fun sendInteractiveMessage(
        @Valid @RequestBody request: WhatsAppInteractiveMessageRequest,
    ): ResponseEntity<WhatsAppMessageResponse> {
        val response =
            whatsAppClient.sendInteractiveMessage(
                phoneNumberId = request.metaPhoneNumberId,
                to = request.to,
                payload = request.payload,
            )

        val camelCaseResponse =
            WhatsAppMessageResponse(
                messagingProduct = response.messagingProduct,
                contacts =
                    response.contacts.map { contact ->
                        WhatsAppMessageResponse.WhatsAppContactResponse(
                            input = contact.input,
                            waId = contact.waId,
                        )
                    },
                messages =
                    response.messages.map { message ->
                        WhatsAppMessageResponse.WhatsAppMessageResponse(
                            id = message.id,
                        )
                    },
            )

        return ResponseEntity.ok(camelCaseResponse)
    }
}
