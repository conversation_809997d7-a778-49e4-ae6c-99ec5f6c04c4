package br.com.mecontaai.api.accounting.infrastructure.client.infosimples

import br.com.mecontaai.api.accounting.infrastructure.client.infosimples.dto.InfoSimplesResponseDto

/**
 * Interface que define o contrato para interação com a API do InfoSimples.
 *
 * Este client permite consultar dados de CCM (Cadastro de Contribuintes Mobiliários)
 * de empresas na prefeitura de São Paulo através do CNPJ.
 */
interface InfoSimplesClient {

    /**
     * Consulta dados de CCM de uma empresa através do CNPJ no InfoSimples.
     *
     * @param cnpj CNPJ a ser consultado (apenas dígitos)
     * @return Dados do CCM da empresa, incluindo informações cadastrais, CNAEs e tributos
     * @throws InfoSimplesException se o CNPJ não for encontrado ou ocorrer outro erro na consulta
     */
    fun consultarCcm(cnpj: String): InfoSimplesResponseDto
}
