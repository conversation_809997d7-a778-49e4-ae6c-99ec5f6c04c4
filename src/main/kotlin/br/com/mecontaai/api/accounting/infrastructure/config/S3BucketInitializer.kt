package br.com.mecontaai.api.accounting.infrastructure.config

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Configuration
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.context.event.EventListener
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.BucketAlreadyExistsException
import software.amazon.awssdk.services.s3.model.BucketAlreadyOwnedByYouException
import software.amazon.awssdk.services.s3.model.CreateBucketRequest
import software.amazon.awssdk.services.s3.model.HeadBucketRequest

/**
 * Inicializador que garante que o bucket do S3 existe
 */
@Configuration
class S3BucketInitializer(
    private val s3Client: S3Client,
    @Qualifier("s3BucketName") private val bucketName: String,
    @Value("\${aws.s3.endpoint:#{null}}") private val endpoint: String?
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    
    @EventListener(ContextRefreshedEvent::class)
    fun initializeBucket() {
        logger.info("Verificando se o bucket S3 existe: $bucketName")
        
        try {
            // Primeiro, verificamos se o bucket já existe
            val headBucketRequest = HeadBucketRequest.builder()
                .bucket(bucketName)
                .build()
                
            try {
                s3Client.headBucket(headBucketRequest)
                logger.info("Bucket S3 já existe: $bucketName")
                return
            } catch (e: Exception) {
                // Bucket não existe, vamos criar
                logger.info("Bucket S3 não encontrado, tentando criar: $bucketName")
            }
            
            // Criar o bucket
            val createBucketRequest = CreateBucketRequest.builder()
                .bucket(bucketName)
                .build()
                
            s3Client.createBucket(createBucketRequest)
            logger.info("Bucket S3 criado com sucesso: $bucketName")
        } catch (e: BucketAlreadyExistsException) {
            logger.info("Bucket S3 já existe (outro proprietário): $bucketName")
        } catch (e: BucketAlreadyOwnedByYouException) {
            logger.info("Bucket S3 já existe (mesmo proprietário): $bucketName")
        } catch (e: Exception) {
            // No ambiente de desenvolvimento com LocalStack, alguns erros podem ocorrer 
            // mas não afetam a funcionalidade
            if (endpoint?.contains("localhost") == true || endpoint?.contains("127.0.0.1") == true) {
                logger.warn("Erro ao verificar/criar bucket S3 no LocalStack. Isso é normal no ambiente de desenvolvimento: ${e.message}")
            } else {
                logger.error("Erro ao criar bucket S3 $bucketName: ${e.message}", e)
            }
        }
    }
} 