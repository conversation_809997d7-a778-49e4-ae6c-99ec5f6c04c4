package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.Cnae

/**
 * Interface para o repositório de CNAEs.
 */
interface CnaeRepository {
    /**
     * Salva um CNAE no banco de dados.
     *
     * @param cnae CNAE a ser salvo
     * @return CNAE salvo
     */
    fun save(cnae: Cnae): Cnae

    /**
     * Busca um CNAE pelo seu ID.
     *
     * @param id ID do CNAE
     * @return CNAE se encontrado, ou null
     */
    fun findById(id: Long): Cnae?

    /**
     * Busca um CNAE pelo seu UUID.
     *
     * @param uuid UUID do CNAE
     * @return CNAE se encontrado, ou null
     */
    fun findByUuid(uuid: String): Cnae?

    /**
     * Busca um CNAE pelo seu código.
     *
     * @param code Código do CNAE
     * @return CNAE se encontrado, ou null
     */
    fun findByCode(code: String): Cnae?

    /**
     * Busca todos os CNAEs.
     *
     * @return Lista de CNAEs
     */
    fun findAll(): List<Cnae>
} 