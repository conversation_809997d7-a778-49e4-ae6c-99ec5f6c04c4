package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp

import io.ktor.client.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

class TempWhatsAppMessageSender(private val accessToken: String) {

    private val client = HttpClient {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                encodeDefaults = true
            })
        }
    }

    suspend fun sendTemplateMessage(to: String, templateName: String, languageCode: String = "en_US"): HttpResponse {
        val url = "https://graph.facebook.com/v22.0/***************/messages"

        val payload = TemplateMessagePayload(
            messagingProduct = "whatsapp",
            to = to,
            type = "template",
            template = Template(
                name = templateName,
                language = Language(code = languageCode)
            )
        )

        val response: HttpResponse = client.post(url) {
            contentType(ContentType.Application.Json)
            bearerAuth(accessToken)
            setBody(payload)
        }

        return response
    }

    /**
     * Sends a custom text message to a WhatsApp user.
     *
     * @param to Recipient's phone number in international format.
     * @param message The text message to be sent.
     * @return HttpResponse from the API.
     */
    suspend fun sendTextMessage(to: String, message: String): HttpResponse {
        val url = "https://graph.facebook.com/v22.0/***************/messages"

        val payload = TextMessagePayload(
            messagingProduct = "whatsapp",
            to = to,
            type = "text",
            text = Text(body = message)
        )

        return client.post(url) {
            contentType(ContentType.Application.Json)
            bearerAuth(accessToken)
            setBody(payload)
        }
    }

    @Serializable
    data class TemplateMessagePayload(
        val messagingProduct: String,
        val to: String,
        val type: String,
        val template: Template
    )

    @Serializable
    data class Template(
        val name: String,
        val language: Language
    )

    @Serializable
    data class Language(
        val code: String
    )

    @Serializable
    data class TextMessagePayload(
        val messagingProduct: String,
        val to: String,
        val type: String,
        val text: Text
    )

    @Serializable
    data class Text(
        val body: String
    )
}
