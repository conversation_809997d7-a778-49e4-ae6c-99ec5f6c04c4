package br.com.mecontaai.api.accounting.presentation.dto.flows

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Classe para mapear o JSON criptografado recebido
 */
data class EncryptedFlowRequest(
    @JsonProperty("encrypted_flow_data") val encryptedFlowData: String,
    @JsonProperty("encrypted_aes_key") val encryptedAesKey: String,
    @JsonProperty("initial_vector") val initialVector: String,
)
