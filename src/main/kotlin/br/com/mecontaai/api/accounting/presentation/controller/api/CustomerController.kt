package br.com.mecontaai.api.accounting.presentation.controller.api

import br.com.mecontaai.api.accounting.application.usecase.impl.CreateCustomerUseCase
import br.com.mecontaai.api.accounting.domain.exception.CustomerAlreadyExistsException
import br.com.mecontaai.api.accounting.presentation.dto.CustomerFullResponseDto
import br.com.mecontaai.api.accounting.presentation.dto.CreateCustomerRequestDto
import br.com.mecontaai.api.accounting.presentation.mapper.CustomerMapper
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.server.ResponseStatusException

/**
 * Controller para endpoints de cliente.
 */
@RestController
@RequestMapping("/api/v1/customers")
class CustomerController(
    private val createCustomerUseCase: CreateCustomerUseCase,
    private val customerMapper: CustomerMapper
) {
    /**
     * Endpoint para criar um novo cliente.
     *
     * @param requestDto DTO com os dados do cliente a ser criado
     * @return DTO com os dados do cliente criado
     */
    @PostMapping
    fun createCustomer(@Valid @RequestBody requestDto: CreateCustomerRequestDto): ResponseEntity<CustomerFullResponseDto> {
        try {
            // Converte o DTO de requisição para o DTO de aplicação
            val createCustomerDto = customerMapper.toCreateCustomerDto(requestDto)
            
            // Executa o caso de uso
            val customerDto = createCustomerUseCase.execute(createCustomerDto)
            
            // Converte o DTO de aplicação para o DTO de resposta
            val responseDto = customerMapper.toCustomerFullResponseDto(customerDto)
            
            // Retorna com status 201 (Created)
            return ResponseEntity.status(HttpStatus.CREATED).body(responseDto)
        } catch (e: CustomerAlreadyExistsException) {
            throw ResponseStatusException(HttpStatus.CONFLICT, e.message)
        } catch (e: IllegalArgumentException) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, e.message)
        } catch (e: Exception) {
            throw ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Erro ao criar cliente: ${e.message}")
        }
    }
} 