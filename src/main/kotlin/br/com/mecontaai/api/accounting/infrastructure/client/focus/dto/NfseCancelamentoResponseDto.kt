package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * Response DTO for NFSe cancellation.
 *
 * This DTO can represent either a successful cancellation
 * or a failure with error details.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class NfseCancelamentoResponseDto(
    /**
     * Current status of the NFSe:
     * - "cancelado": Successfully canceled
     * - "erro_cancelamento": Cancellation failed
     */
    val status: String,
    
    /**
     * List of errors (only if status is erro_cancelamento)
     */
    val erros: List<FocusErrorDetailDto>? = null
)