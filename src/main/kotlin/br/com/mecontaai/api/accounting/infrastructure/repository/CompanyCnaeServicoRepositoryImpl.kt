package br.com.mecontaai.api.accounting.infrastructure.repository

import br.com.mecontaai.api.accounting.domain.entity.CompanyCnaeServico
import br.com.mecontaai.api.accounting.domain.repository.CompanyCnaeServicoRepository
import br.com.mecontaai.api.accounting.infrastructure.database.model.CompanyCnaeServicoEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CnaeEntityRepository
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CodigoServicoMunicipalEntityRepository
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CompanyCnaeServicoEntityRepository
import br.com.mecontaai.api.accounting.infrastructure.database.repository.CompanyEntityRepository
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Implementação do repositório de associações entre empresas, CNAEs e códigos de serviço municipais.
 */
@Repository
class CompanyCnaeServicoRepositoryImpl(
    private val companyCnaeServicoEntityRepository: CompanyCnaeServicoEntityRepository,
    private val companyEntityRepository: CompanyEntityRepository,
    private val cnaeEntityRepository: CnaeEntityRepository,
    private val codigoServicoMunicipalEntityRepository: CodigoServicoMunicipalEntityRepository
) : CompanyCnaeServicoRepository {

    override fun save(companyCnaeServico: CompanyCnaeServico): CompanyCnaeServico {
        val entity = mapToEntity(companyCnaeServico)
        val savedEntity = companyCnaeServicoEntityRepository.save(entity)
        return mapToDomain(savedEntity)
    }

    override fun findById(id: Long): CompanyCnaeServico? {
        return companyCnaeServicoEntityRepository.findById(id)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByUuid(uuid: String): CompanyCnaeServico? {
        return try {
            val uuidObj = UUID.fromString(uuid)
            companyCnaeServicoEntityRepository.findByUuid(uuidObj)
                .map { mapToDomain(it) }
                .orElse(null)
        } catch (e: IllegalArgumentException) {
            null
        }
    }

    override fun findByCompanyId(companyId: Long): List<CompanyCnaeServico> {
        return companyCnaeServicoEntityRepository.findByCompanyId(companyId)
            .map { mapToDomain(it) }
    }

    override fun findPrimaryByCompanyId(companyId: Long): CompanyCnaeServico? {
        return companyCnaeServicoEntityRepository.findPrimaryByCompanyId(companyId)
            .map { mapToDomain(it) }
            .orElse(null)
    }

    override fun findByCnaeId(cnaeId: Long): List<CompanyCnaeServico> {
        return companyCnaeServicoEntityRepository.findByCnaeId(cnaeId)
            .map { mapToDomain(it) }
    }

    override fun findByCodigoServicoId(codigoServicoId: Long): List<CompanyCnaeServico> {
        return companyCnaeServicoEntityRepository.findByCodigoServicoId(codigoServicoId)
            .map { mapToDomain(it) }
    }

    /**
     * Converte entidade de domínio para entidade JPA.
     */
    private fun mapToEntity(companyCnaeServico: CompanyCnaeServico): CompanyCnaeServicoEntity {
        val companyEntity = companyCnaeServico.companyId?.let {
            companyEntityRepository.findById(it).orElseThrow {
                IllegalArgumentException("Company not found with ID: ${companyCnaeServico.companyId}")
            }
        }

        val cnaeEntity = cnaeEntityRepository.findById(companyCnaeServico.cnaeId).orElseThrow {
            IllegalArgumentException("CNAE not found with ID: ${companyCnaeServico.cnaeId}")
        }

        val codigoServicoEntity = codigoServicoMunicipalEntityRepository.findById(companyCnaeServico.codigoServicoId).orElseThrow {
            IllegalArgumentException("Código de Serviço Municipal not found with ID: ${companyCnaeServico.codigoServicoId}")
        }

        val entity = CompanyCnaeServicoEntity(
            company = companyEntity!!,
            cnae = cnaeEntity,
            codigoServico = codigoServicoEntity,
            isPrimary = companyCnaeServico.isPrimary
        )
        
        entity.id = companyCnaeServico.id
        entity.uuid = companyCnaeServico.uuid
        entity.isActive = companyCnaeServico.isActive
        entity.createdAt = companyCnaeServico.createdAt
        entity.updatedAt = companyCnaeServico.updatedAt
        
        return entity
    }

    /**
     * Converte entidade JPA para entidade de domínio.
     */
    private fun mapToDomain(entity: CompanyCnaeServicoEntity): CompanyCnaeServico {
        return CompanyCnaeServico(
            id = entity.id,
            uuid = entity.uuid,
            companyId = entity.company.id,
            cnaeId = entity.cnae.id!!,
            codigoServicoId = entity.codigoServico.id!!,
            isPrimary = entity.isPrimary,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
} 