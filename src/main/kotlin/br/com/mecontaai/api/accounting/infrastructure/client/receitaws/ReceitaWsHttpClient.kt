package br.com.mecontaai.api.accounting.infrastructure.client.receitaws

import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import feign.Feign
import feign.Headers
import feign.Logger
import feign.Param
import feign.RequestLine
import feign.Response
import feign.codec.ErrorDecoder
import feign.jackson.JacksonDecoder
import feign.jackson.JacksonEncoder
import feign.okhttp.OkHttpClient
import feign.slf4j.Slf4jLogger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.io.IOException
import java.nio.charset.StandardCharsets

/**
 * Interface Feign para a API da Receita WS, definindo os endpoints HTTP.
 */
@Headers("Content-Type: application/json")
interface ReceitaWsHttpApi {
    /**
     * Consulta dados de um CNPJ na API da Receita WS.
     *
     * @param cnpj CNPJ a ser consultado (apenas números)
     * @return Resposta com os dados do CNPJ
     */
    @RequestLine("GET /v1/cnpj/{cnpj}")
    fun consultarCnpj(
        @Param("cnpj") cnpj: String,
    ): ReceitaWsResponseDto
}

/**
 * Implementação do ReceitaWsClient usando o cliente HTTP Feign.
 */
@Component
class ReceitaWsHttpClient(
    @Value("\${receitaws.api.url}")
    private val baseUrl: String,
    private val objectMapper: ObjectMapper
) : ReceitaWsClient {

    private val logger = LoggerFactory.getLogger(javaClass)
    private lateinit var httpClient: ReceitaWsHttpApi

    init {
        val objectMapper =
            ObjectMapper()
                .registerModule(JavaTimeModule())
                .registerKotlinModule()

        httpClient =
            Feign
                .builder()
                .client(OkHttpClient())
                .encoder(JacksonEncoder(objectMapper))
                .decoder(JacksonDecoder(objectMapper))
                .logger(Slf4jLogger(this.javaClass.canonicalName))
                .logLevel(Logger.Level.FULL)
                .errorDecoder(ReceitaWsErrorDecoder(objectMapper))
                .target(ReceitaWsHttpApi::class.java, baseUrl)
    }

    override fun consultarCnpj(cnpj: String): ReceitaWsResponseDto {
        // Remove caracteres não numéricos do CNPJ
        val cnpjLimpo = cnpj.replace(Regex("[^0-9]"), "")

        logger.info("🔍 ReceitaWS API - GET /v1/cnpj/$cnpjLimpo")

        return try {
            val response = httpClient.consultarCnpj(cnpjLimpo)

            if (response.status == "ERROR") {
                logger.warn("⚠️ ReceitaWS API - GET /v1/cnpj/$cnpjLimpo [200] - CNPJ não encontrado")
                throw ReceitaWsCnpjNotFoundException(cnpj)
            }

            val responseJson = objectMapper.writeValueAsString(response)
            logger.info("✅ ReceitaWS API - GET /v1/cnpj/$cnpjLimpo [200] - Response: $responseJson")
            response
        } catch (e: ReceitaWsException) {
            if (e !is ReceitaWsCnpjNotFoundException) {
                logger.error("❌ ReceitaWS API - GET /v1/cnpj/$cnpjLimpo [${e.httpStatus}] - Error: ${e.message}")
            }
            throw e
        } catch (e: Exception) {
            logger.error("❌ ReceitaWS API - GET /v1/cnpj/$cnpjLimpo [ERROR] - Exception: ${e.message}", e)
            throw ReceitaWsException("Erro ao consultar CNPJ: ${e.message}", e)
        }
    }
}

/**
 * Decodificador de erro personalizado para a API da Receita WS.
 */
class ReceitaWsErrorDecoder(
    private val objectMapper: ObjectMapper,
) : ErrorDecoder {
    override fun decode(
        methodKey: String,
        response: Response,
    ): Exception {
        val statusCode = response.status()

        // Se não for um erro, não transforma
        if (statusCode < 400) {
            return feign.codec.ErrorDecoder
                .Default()
                .decode(methodKey, response)
        }

        try {
            val body = response.body()
            val bodyString =
                body?.let {
                    String(it.asInputStream().readBytes(), StandardCharsets.UTF_8)
                } ?: ""

            if (bodyString.isNotBlank()) {
                return ReceitaWsException(statusCode, bodyString)
            } else {
                return ReceitaWsException(statusCode, "HTTP error $statusCode without body")
            }
        } catch (e: IOException) {
            return ReceitaWsException(statusCode, "Error parsing error response: ${e.message}")
        }
    }
}
