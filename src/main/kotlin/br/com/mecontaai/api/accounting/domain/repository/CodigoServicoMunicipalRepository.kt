package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.CodigoServicoMunicipal

/**
 * Repositório para entidades CodigoServicoMunicipal.
 */
interface CodigoServicoMunicipalRepository {
    fun save(codigoServicoMunicipal: CodigoServicoMunicipal): CodigoServicoMunicipal
    fun findById(id: Long): CodigoServicoMunicipal?
    fun findByUuid(uuid: String): CodigoServicoMunicipal?
    fun findByCodigoAndCodigoMunicipio(codigo: String, codigoMunicipio: String): CodigoServicoMunicipal?
    fun findByCodigoMunicipio(codigoMunicipio: String): List<CodigoServicoMunicipal>
    fun findAll(): List<CodigoServicoMunicipal>
} 