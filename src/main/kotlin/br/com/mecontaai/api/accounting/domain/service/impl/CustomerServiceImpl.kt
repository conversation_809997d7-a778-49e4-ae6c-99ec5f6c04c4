package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.entity.Customer
import br.com.mecontaai.api.accounting.domain.enum.DocumentType
import br.com.mecontaai.api.accounting.domain.repository.CustomerRepository
import br.com.mecontaai.api.accounting.domain.service.CustomerService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * Implementation of the CustomerService interface.
 */
@Service
class CustomerServiceImpl(
    private val customerRepository: CustomerRepository
) : CustomerService {
    private val logger = LoggerFactory.getLogger(javaClass)

    /**
     * Creates a new customer.
     * For now, this is just a mock implementation that logs and saves the customer.
     *
     * @param customer The customer to create
     * @return The created customer
     */
    @Transactional
    override fun createCustomer(customer: Customer): Customer {
        logger.info("Creating customer: ${customer.name} with document ${customer.document}")
        
        // For now, just mocking some processing
        logger.info("Validating customer data...")
        validateCustomer(customer)
        
        logger.info("Checking if customer already exists...")
        if (!customer.isForeigner) {
            customerRepository.findByDocumentAndCompanyId(customer.document, customer.companyId)?.let {
                logger.info("Customer already exists, returning existing customer")
                return it
            }
        }
        
        logger.info("Saving customer to database...")
        return customerRepository.save(customer)
    }

    override fun findById(id: Long): Customer? {
        return customerRepository.findById(id)
    }

    override fun findByUuid(uuid: UUID): Customer? {
        return customerRepository.findByUuid(uuid)
    }

    override fun findByDocumentAndCompanyId(document: String, companyId: Long): Customer? {
        return customerRepository.findByDocumentAndCompanyId(document, companyId)
    }

    override fun findByCompanyId(companyId: Long): List<Customer> {
        return customerRepository.findByCompanyId(companyId)
    }

    override fun findByCompanyIdAndCustomerName(companyId: Long, customerName: String): List<Customer> {
        logger.info("Searching customers by name: '$customerName' in company: $companyId")
        return customerRepository.findByCompanyId(companyId)
            .filter { customer -> customer.name.contains(customerName, ignoreCase = true) }
    }
    
    /**
     * Validates customer data.
     * For now, just does some basic validation.
     *
     * @param customer The customer to validate
     */
    private fun validateCustomer(customer: Customer) {
        require(customer.name.isNotBlank()) { "Customer name cannot be blank" }

        if (!customer.isForeigner) {
            require(customer.document.isNotBlank()) { "Customer document cannot be blank" }
        }
        
        // Validate document format based on document type
        when (customer.documentType) {
            DocumentType.CPF -> require(customer.document.length == 11) { "CPF must have 11 digits" }
            DocumentType.CNPJ -> require(customer.document.length == 14) { "CNPJ must have 14 digits" }
            DocumentType.FOREIGNER -> {
                // Não precisamos validar o documento de estrangeiros, pois eles não têm CPF ou CNPJ
                logger.info("Customer is a foreigner, skipping document validation for ${customer.name}")
            }
        }
    }
} 