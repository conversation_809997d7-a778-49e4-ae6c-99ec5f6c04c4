package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.CnaeEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

/**
 * JPA repository for CnaeEntity.
 */
@Repository
interface CnaeEntityRepository : JpaRepository<CnaeEntity, Long> {
    /**
     * Find a CNAE by UUID.
     *
     * @param uuid The CNAE UUID
     * @return The CNAE if found, wrapped in Optional
     */
    fun findByUuid(uuid: UUID): Optional<CnaeEntity>

    /**
     * Find a CNAE by code.
     *
     * @param code The CNAE code
     * @return The CNAE if found, wrapped in Optional
     */
    fun findByCode(code: String): Optional<CnaeEntity>

    /**
     * Check if a CNAE with the given code exists.
     *
     * @param code The CNAE code
     * @return true if a CNAE with the code exists, false otherwise
     */
    fun existsByCode(code: String): <PERSON><PERSON><PERSON>
} 