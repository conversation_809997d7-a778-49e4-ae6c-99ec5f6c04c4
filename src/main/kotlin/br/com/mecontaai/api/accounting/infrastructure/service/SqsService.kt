package br.com.mecontaai.api.accounting.infrastructure.service

import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

@Service
class SqsService(
    private val sqsClient: SqsClient,
    private val objectMapper: ObjectMapper
) {
    private val logger = LoggerFactory.getLogger(SqsService::class.java)

    fun sendToQueue(payload: Any, queueName: String) {
        try {
            val message = objectMapper.writeValueAsString(payload)
            val request = SendMessageRequest.builder()
                .queueUrl(queueName)
                .messageBody(message)
                .build()
                
            sqsClient.sendMessage(request)
            logger.info("Mensagem enviada com sucesso para a fila $queueName")
        } catch (e: Exception) {
            logger.error("Erro ao enviar mensagem para a fila $queueName: ${e.message}", e)
            throw e
        }
    }
} 