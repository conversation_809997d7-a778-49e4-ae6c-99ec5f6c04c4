package br.com.mecontaai.api.accounting.presentation.mapper

import br.com.mecontaai.api.accounting.application.dto.CompanyDto
import br.com.mecontaai.api.accounting.application.dto.CreateCompanyDto
import br.com.mecontaai.api.accounting.presentation.dto.CompanyResponseDto
import br.com.mecontaai.api.accounting.presentation.dto.CreateCompanyRequestDto
import org.springframework.stereotype.Component

/**
 * Mapeador para conversão entre DTOs de empresa.
 */
@Component
class CompanyMapper {
    /**
     * Converte um DTO de requisição de criação de empresa para um DTO de aplicação.
     *
     * @param requestDto DTO de requisição
     * @return DTO de aplicação
     */
    fun toCreateCompanyDto(requestDto: CreateCompanyRequestDto): CreateCompanyDto {
        return CreateCompanyDto(
            document = requestDto.document
        )
    }
    
    /**
     * Converte um DTO de aplicação de empresa para um DTO de resposta.
     *
     * @param companyDto DTO de aplicação
     * @return DTO de resposta
     */
    fun toCompanyResponseDto(companyDto: CompanyDto): CompanyResponseDto {
        return CompanyResponseDto(
            id = companyDto.id,
            uuid = companyDto.uuid,
            name = companyDto.name,
            document = companyDto.document,
            naturezaJuridica = companyDto.naturezaJuridica,
            municipalRegistration = companyDto.municipalRegistration,
            stateRegistration = companyDto.stateRegistration,
            simplesNacional = companyDto.simplesNacional,
            codigoMunicipio = companyDto.codigoMunicipio,
            codigoSiafi = companyDto.codigoSiafi,
            codigoIbge = companyDto.codigoIbge,
            focusId = companyDto.focusId,
            isActive = companyDto.isActive,
            createdAt = companyDto.createdAt,
            updatedAt = companyDto.updatedAt
        )
    }
} 