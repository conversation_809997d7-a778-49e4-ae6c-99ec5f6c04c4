package br.com.mecontaai.api.accounting.domain.service

import br.com.mecontaai.api.accounting.domain.model.response.AgentResponse
import br.com.mecontaai.api.accounting.domain.service.llm.chatcompletion.dto.LlmResponse
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Component responsible for extracting text content from LLM responses.
 * Handles different types of response items and provides fallback mechanisms.
 */
@Component
class ResponseTextExtractor {
    private val logger = LoggerFactory.getLogger(ResponseTextExtractor::class.java)

    /**
     * Extracts the main text response from an LLM response.
     *
     * @param llmResponse The LLM response to extract text from
     * @return The extracted text content
     * @throws ResponseExtractionException if no valid response can be extracted
     */
    fun extractResponseText(llmResponse: LlmResponse): String {
        logger.debug("Extracting response text from LLM response with ${llmResponse.agentResponses.size} items")

        if (llmResponse.agentResponses.isEmpty()) {
            throw ResponseExtractionException("LLM response contains no response items")
        }

        // Get the first response as main text
        val firstResponse = llmResponse.agentResponses.first()

        val responseText =
            when (firstResponse) {
                is AgentResponse.TextResponse -> {
                    logger.debug("Extracting from TextResponse")
                    firstResponse.content
                }

                else -> {
                    logger.warn("Unknown response item type: ${firstResponse::class.simpleName}")
                    getDefaultErrorMessage()
                }
            }

        if (responseText.isBlank()) {
            logger.warn("Extracted response text is blank, using fallback")
            return getDefaultErrorMessage()
        }

        logger.debug("Successfully extracted response text (${responseText.length} characters)")
        return responseText
    }

    /**
     * Provides a default error message when extraction fails.
     */
    private fun getDefaultErrorMessage(): String = "Não foi possível processar a resposta"

    /**
     * Validates if the extracted text is suitable for sending to user.
     */
    fun validateResponseText(text: String): Boolean = text.isNotBlank() && text.length <= MAX_RESPONSE_LENGTH

    companion object {
        private const val MAX_RESPONSE_LENGTH = 4096 // WhatsApp message limit
    }
}

/**
 * Exception thrown when response text extraction fails.
 */
class ResponseExtractionException(
    message: String,
    cause: Throwable? = null,
) : Exception(message, cause)
