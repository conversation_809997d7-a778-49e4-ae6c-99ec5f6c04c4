package br.com.mecontaai.api.accounting.domain.factory

import br.com.mecontaai.api.accounting.domain.enum.NaturezaJuridicaEnum
import br.com.mecontaai.api.accounting.domain.strategy.CompanyValidationStrategy
import br.com.mecontaai.api.accounting.domain.strategy.impl.EmpresarioIndividualValidationStrategy
import br.com.mecontaai.api.accounting.domain.strategy.impl.SociedadeEmpresariaLimitadaValidationStrategy

/**
 * Factory para obter a estratégia de validação apropriada baseada na natureza jurídica.
 */
object CompanyValidationStrategyFactory {
    fun getStrategy(naturezaJuridica: NaturezaJuridicaEnum?): CompanyValidationStrategy {
        return when (naturezaJuridica) {
            NaturezaJuridicaEnum.EMPRESARIO_INDIVIDUAL -> EmpresarioIndividualValidationStrategy()
            NaturezaJuridicaEnum.SOCIEDADE_EMPRESARIA_LIMITADA -> SociedadeEmpresariaLimitadaValidationStrategy()
            else -> SociedadeEmpresariaLimitadaValidationStrategy()
        }
    }
}
