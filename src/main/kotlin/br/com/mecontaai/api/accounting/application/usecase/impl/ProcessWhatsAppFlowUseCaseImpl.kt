package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.service.flow.FlowRouter
import br.com.mecontaai.api.accounting.application.usecase.ProcessWhatsAppFlowUseCase
import br.com.mecontaai.api.accounting.infrastructure.crypto.WhatsAppFlowCryptoService
import br.com.mecontaai.api.accounting.presentation.dto.flows.EncryptedFlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ProcessWhatsAppFlowUseCaseImpl(
    private val cryptoService: WhatsAppFlowCryptoService,
    private val flowRouter: FlowRouter,
    private val objectMapper: ObjectMapper,
) : ProcessWhatsAppFlowUseCase {

    private val logger = LoggerFactory.getLogger(ProcessWhatsAppFlowUseCaseImpl::class.java)

    override fun execute(request: EncryptedFlowRequest): String =
        try {
            val aesKey = decryptAESKey(request)
            val flowRequest = decryptFlowData(request, aesKey)
            logDecryptedRequest(flowRequest)
            val flowResponse = routeFlow(flowRequest)
            logFlowResponse(flowResponse)
            val encryptedResponse = encryptResponse(flowResponse, aesKey, request.initialVector)
            logger.info("✅ [WHATSAPP FLOW] Processamento concluído com sucesso")
            encryptedResponse
        } catch (ex: Exception) {
            logger.error("❌ [WHATSAPP FLOW] Erro no processamento do WhatsApp Flow", ex)
            throw FlowProcessingException("Falha no processamento do flow", ex)
        }

    private fun decryptAESKey(request: EncryptedFlowRequest): ByteArray =
        cryptoService.decryptAESKey(request.encryptedAesKey)

    private fun decryptFlowData(request: EncryptedFlowRequest, aesKey: ByteArray): FlowRequest =
        cryptoService.decryptFlowData(
            request.encryptedFlowData,
            request.initialVector,
            aesKey,
        )

    private fun logDecryptedRequest(flowRequest: FlowRequest) {
        try {
            logger.info("📄 WhatsApp Flow Request JSON: ${objectMapper.writeValueAsString(flowRequest)}")
        } catch (e: Exception) {
            logger.warn("⚠️ [WHATSAPP FLOW] Erro ao serializar request para log: ${e.message}")
        }
    }

    private fun routeFlow(flowRequest: FlowRequest): FlowResponse =
        flowRouter.route(flowRequest)

    private fun logFlowResponse(flowResponse: FlowResponse) {
        try {
            logger.info("📄 [WHATSAPP FLOW] Response JSON: ${objectMapper.writeValueAsString(flowResponse)}")
        } catch (e: Exception) {
            logger.warn("⚠️ [WHATSAPP FLOW] Erro ao serializar response para log: ${e.message}")
        }
    }

    private fun encryptResponse(flowResponse: FlowResponse, aesKey: ByteArray, iv: String): String =
        cryptoService.encryptFlowResponse(flowResponse, aesKey, iv)
}

/**
 * Exceção específica para erros no processamento de flows.
 */
class FlowProcessingException(
    message: String,
    cause: Throwable? = null,
) : RuntimeException(message, cause)
