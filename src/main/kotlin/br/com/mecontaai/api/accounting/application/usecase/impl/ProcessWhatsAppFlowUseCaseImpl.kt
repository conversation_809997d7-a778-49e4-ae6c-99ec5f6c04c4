package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.service.flow.FlowService
import br.com.mecontaai.api.accounting.application.usecase.ProcessWhatsAppFlowUseCase
import br.com.mecontaai.api.accounting.infrastructure.crypto.WhatsAppFlowCryptoService
import br.com.mecontaai.api.accounting.presentation.dto.flows.EncryptedFlowRequest
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Implementação do use case para processar WhatsApp Flows.
 *
 * Responsabilidades:
 * - Orquestrar o processo de descriptografia -> processamento -> criptografia
 * - Coordenar entre serviços especializados
 * - Gerenciar logging de alto nível
 * - Tratar exceções e converter para respostas apropriadas
 */
@Service
class ProcessWhatsAppFlowUseCaseImpl(
    private val cryptoService: WhatsAppFlowCryptoService,
    private val flowService: FlowService,
    private val objectMapper: ObjectMapper,
) : ProcessWhatsAppFlowUseCase {
    private val logger = LoggerFactory.getLogger(ProcessWhatsAppFlowUseCaseImpl::class.java)

    override fun execute(request: EncryptedFlowRequest): String =
        try {
            // Log do request criptografado (dados básicos)
            val aesKeyBytes = cryptoService.decryptAESKey(request.encryptedAesKey)
            val flowRequest =
                cryptoService.decryptFlowData(
                    request.encryptedFlowData,
                    request.initialVector,
                    aesKeyBytes,
                )

            // Log do request descriptografado (DADOS SENSÍVEIS)
            try {
                logger.info("📄 WhatsApp Flow Request JSON: ${objectMapper.writeValueAsString(flowRequest)}")
            } catch (e: Exception) {
                logger.warn("⚠️ [WHATSAPP FLOW] Erro ao serializar request para log: ${e.message}")
            }

            val flowResponse = flowService.processFlow(flowRequest)

            // Log do response antes da criptografia (DADOS SENSÍVEIS)
            try {
                logger.info("📄 [WHATSAPP FLOW] Response JSON: ${objectMapper.writeValueAsString(flowResponse)}")
            } catch (e: Exception) {
                logger.warn("⚠️ [WHATSAPP FLOW] Erro ao serializar response para log: ${e.message}")
            }

            val encryptedResponse =
                cryptoService.encryptFlowResponse(
                    flowResponse,
                    aesKeyBytes,
                    request.initialVector,
                )

            // Log do response criptografado (dados básicos)
            logger.info("✅ [WHATSAPP FLOW] Processamento concluído com sucesso")
            encryptedResponse
        } catch (ex: Exception) {
            logger.error("❌ [WHATSAPP FLOW] Erro no processamento do WhatsApp Flow", ex)
            throw FlowProcessingException("Falha no processamento do flow", ex)
        }
}

/**
 * Exceção específica para erros no processamento de flows.
 */
class FlowProcessingException(
    message: String,
    cause: Throwable? = null,
) : RuntimeException(message, cause)
