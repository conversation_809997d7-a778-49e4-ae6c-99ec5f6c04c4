package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.service.flow.FlowService
import br.com.mecontaai.api.accounting.application.usecase.ProcessWhatsAppFlowUseCase
import br.com.mecontaai.api.accounting.infrastructure.crypto.WhatsAppFlowCryptoService
import br.com.mecontaai.api.accounting.presentation.dto.flows.EncryptedFlowRequest
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Implementação do use case para processar WhatsApp Flows.
 *
 * Responsabilidades:
 * - Orquestrar o processo de descriptografia -> processamento -> criptografia
 * - Coordenar entre serviços especializados
 * - Gerenciar logging de alto nível
 * - Tratar exceções e converter para respostas apropriadas
 */
@Service
class ProcessWhatsAppFlowUseCaseImpl(
    private val cryptoService: WhatsAppFlowCryptoService,
    private val flowService: FlowService,
) : ProcessWhatsAppFlowUseCase {
    private val logger = LoggerFactory.getLogger(ProcessWhatsAppFlowUseCaseImpl::class.java)

    override fun execute(request: EncryptedFlowRequest): String =
        try {
            // 1. Descriptografar chave AES
            val aesKeyBytes = cryptoService.decryptAESKey(request.encryptedAesKey)

            // 2. Descriptografar dados do flow
            val flowRequest =
                cryptoService.decryptFlowData(
                    request.encryptedFlowData,
                    request.initialVector,
                    aesKeyBytes,
                )

            // 3. Processar lógica do flow
            val flowResponse = flowService.processFlow(flowRequest)
            val encryptedResponse =
                cryptoService.encryptFlowResponse(
                    flowResponse,
                    aesKeyBytes,
                    request.initialVector,
                )
            encryptedResponse
        } catch (ex: Exception) {
            logger.error("Erro no processamento do WhatsApp Flow", ex)
            throw FlowProcessingException("Falha no processamento do flow", ex)
        }
}

/**
 * Exceção específica para erros no processamento de flows.
 */
class FlowProcessingException(
    message: String,
    cause: Throwable? = null,
) : RuntimeException(message, cause)
