package br.com.mecontaai.api.accounting.presentation.controller.api

import br.com.mecontaai.api.accounting.domain.service.MessageSenderService
import br.com.mecontaai.api.accounting.presentation.dto.MessageRequest
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/messages")
class MessageController(
    private val messageSenderService: MessageSenderService
) {
    
    @PostMapping("/send-text-message")
    fun sendMessage(@RequestBody @Valid request: MessageRequest): ResponseEntity<String> {
        val externalMessageId = messageSenderService.sendTextMessage(request.phoneNumber, request.message)
        // return external MessageId
        return ResponseEntity.ok(externalMessageId)
    }
} 