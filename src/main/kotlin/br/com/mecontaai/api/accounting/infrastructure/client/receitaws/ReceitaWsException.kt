package br.com.mecontaai.api.accounting.infrastructure.client.receitaws

/**
 * Exception lançada para erros relacionados à API da Receita WS.
 */
open class ReceitaWsException : RuntimeException {
    val httpStatus: Int

    /**
     * Cria uma exceção com mensagem
     */
    constructor(message: String) : super(message) {
        this.httpStatus = 0
    }

    /**
     * Cria uma exceção com mensagem e código HTTP
     */
    constructor(httpStatus: Int, message: String) : super(message) {
        this.httpStatus = httpStatus
    }

    /**
     * Cria uma exceção com mensagem e causa
     */
    constructor(message: String, cause: Throwable) : super(message, cause) {
        this.httpStatus = 0
    }
} 