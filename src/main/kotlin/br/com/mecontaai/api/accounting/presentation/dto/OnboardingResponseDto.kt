package br.com.mecontaai.api.accounting.presentation.dto

/**
 * DTO para resposta do processo de onboarding de certificado.
 */
data class OnboardingResponseDto(
    /**
     * Se o processo foi bem-sucedido
     */
    val success: Boolean,
    
    /**
     * Status atual do onboarding
     */
    val status: String,
    
    /**
     * Mensagem descritiva do resultado
     */
    val message: String,
    
    /**
     * ID do certificado criado (se sucesso)
     */
    val certificateId: Long? = null,
    
    /**
     * Dados do certificado criado (se sucesso)
     */
    val certificate: CertificateResponseDto? = null
) 