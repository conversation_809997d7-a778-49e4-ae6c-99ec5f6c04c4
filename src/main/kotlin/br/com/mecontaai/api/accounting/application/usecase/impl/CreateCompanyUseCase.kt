package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.dto.CompanyDto
import br.com.mecontaai.api.accounting.application.dto.CreateCompanyDto
import br.com.mecontaai.api.accounting.application.service.ReceitaWsService
import br.com.mecontaai.api.accounting.application.usecase.ValidateCompanyUseCase
import br.com.mecontaai.api.accounting.domain.entity.Address
import br.com.mecontaai.api.accounting.domain.entity.Cnae
import br.com.mecontaai.api.accounting.domain.entity.CodigoServicoMunicipal
import br.com.mecontaai.api.accounting.domain.entity.Company
import br.com.mecontaai.api.accounting.domain.entity.CompanyCnaeServico
import br.com.mecontaai.api.accounting.domain.enum.AddressType
import br.com.mecontaai.api.accounting.domain.enum.EntityType
import br.com.mecontaai.api.accounting.domain.exception.CNPJDataNotFoundException
import br.com.mecontaai.api.accounting.domain.exception.CompanyDocumentAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.exception.CompanyValidationException
import br.com.mecontaai.api.accounting.domain.repository.AddressRepository
import br.com.mecontaai.api.accounting.domain.repository.CnaeRepository
import br.com.mecontaai.api.accounting.domain.repository.CodigoServicoMunicipalRepository
import br.com.mecontaai.api.accounting.domain.repository.CompanyCnaeServicoRepository
import br.com.mecontaai.api.accounting.domain.repository.CompanyRepository
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusClient
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusException
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.CnpjConsultaResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto
import br.com.mecontaai.api.accounting.infrastructure.logging.JsonLogger
import br.com.mecontaai.api.accounting.infrastructure.logging.debugJson
import br.com.mecontaai.api.accounting.infrastructure.logging.errorJson
import br.com.mecontaai.api.accounting.infrastructure.logging.infoJson
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/**
 * Composite data class containing full company data from multiple sources.
 */
data class FullCompanyData(
    val focusData: CnpjConsultaResponseDto,
    val receitaWsData: ReceitaWsResponseDto
)

/**
 * Caso de uso para criação de uma empresa a partir do CNPJ.
 */
@Component
class CreateCompanyUseCase(
    private val companyRepository: CompanyRepository,
    private val addressRepository: AddressRepository,
    private val cnaeRepository: CnaeRepository,
    private val companyCnaeServicoRepository: CompanyCnaeServicoRepository,
    private val codigoServicoMunicipalRepository: CodigoServicoMunicipalRepository,
    private val focusClient: FocusClient,
    private val receitaWsService: ReceitaWsService,
    private val validateCompanyUseCase: ValidateCompanyUseCase,
    private val jsonLogger: JsonLogger,
) {

    private val logger = LoggerFactory.getLogger(javaClass)
    /**
     * Cria uma nova empresa a partir do CNPJ.
     *
     * @param dto DTO com o CNPJ da empresa a ser criada
     * @return DTO com os dados da empresa criada
     * @throws CompanyDocumentAlreadyExistsException se já existir uma empresa com o mesmo CNPJ
     * @throws CNPJDataNotFoundException se o CNPJ não for encontrado na API Focus
     * @throws CompanyValidationException se a validação do CNPJ falhar
     */
    @Transactional
    fun execute(dto: CreateCompanyDto): CompanyDto {
        logger.info("Iniciando criação de empresa para CNPJ: ${dto.document}")

        try {
            // Step 1: Verificar duplicação
            validateCNPJUniqueness(dto.document)

            // Step 2: Obter dados completos do CNPJ
            val fullCompanyData = fetchFullCompanyData(dto.document)

            // Step 3: Validar empresa
            performCompanyValidation(dto.document)

            // Step 4: Criar e salvar empresa
            val savedCompany = createAndSaveCompany(fullCompanyData)

            // Step 5: Criar endereço
            createCompanyAddress(savedCompany, fullCompanyData)

            // Step 6: Configurar CNAE e serviços
            setupCompanyServices(savedCompany, fullCompanyData)

            // Step 7: Converter para DTO de resposta
            val result = mapToCompanyDto(savedCompany)

            logger.infoJson("Empresa criada com sucesso:", result, jsonLogger)
            return result

        } catch (e: Exception) {
            logger.errorJson("Erro ao criar empresa para CNPJ: ${dto.document}",
                mapOf("error" to e.message, "cnpj" to dto.document), jsonLogger, e)
            throw e
        }
    }

    /**
     * Step 1: Valida se o cnpj não existe no sistema.
     */
    private fun validateCNPJUniqueness(cnpj: String) {
        logger.debug("Verificando unicidade do CNPJ: $cnpj")

        if (companyRepository.existsByDocument(cnpj)) {
            logger.warn("Tentativa de criar empresa com CNPJ já existente: $cnpj")
            throw CompanyDocumentAlreadyExistsException(cnpj)
        }
    }

    /**
     * Step 2: Executa validação do CNPJ usando o caso de uso específico.
     */
    private fun performCompanyValidation(document: String) =
        validateCompanyUseCase.execute(document).also { validationResult ->
            logger.infoJson("Resultado da validação do CNPJ:", validationResult, jsonLogger)

            if (!validationResult.isValid) {
                val errorMessage = validationResult.errorMessage ?: "Erro de validação desconhecido"
                logger.warn("Validação do CNPJ falhou: $document - $errorMessage")
                throw CompanyValidationException(errorMessage)
            }
        }

    /**
     * Step 3: Busca dados completos do CNPJ usando Focus e ReceitaWS.
     */
    private fun fetchFullCompanyData(document: String): FullCompanyData {
        logger.debug("Consultando dados completos do CNPJ: $document")

        try {
            // Buscar dados do Focus
            logger.debug("Consultando dados do CNPJ na API Focus: $document")
            val focusData = focusClient.consultarCnpj(document)
            logger.debugJson("Dados do CNPJ obtidos da API Focus:", focusData, jsonLogger)

            // Buscar dados da ReceitaWS
            logger.debug("Consultando dados do CNPJ na ReceitaWS: $document")
            val receitaWsData = receitaWsService.getCnpjData(document)
            logger.debugJson("Dados do CNPJ obtidos da ReceitaWS:", receitaWsData, jsonLogger)

            return FullCompanyData(
                focusData = focusData,
                receitaWsData = receitaWsData
            ).also { fullData ->
                logger.debugJson("Dados completos do CNPJ obtidos:", fullData, jsonLogger)
            }

        } catch (e: FocusException) {
            logger.errorJson("Erro da API Focus ao consultar CNPJ:",
                mapOf("cnpj" to document, "error" to e.message), jsonLogger, e)
            throw CNPJDataNotFoundException(document)
        } catch (e: Exception) {
            logger.errorJson("Erro inesperado ao consultar CNPJ:",
                mapOf("cnpj" to document, "error" to e.message), jsonLogger, e)
            throw CNPJDataNotFoundException(document)
        }
    }

    /**
     * Step 4: Cria e salva a entidade Company.
     */
    private fun createAndSaveCompany(fullCompanyData: FullCompanyData): Company {
        logger.debug("Criando entidade Company from FullCompanyData: {}", fullCompanyData)

        val focusData = fullCompanyData.focusData
        val company = Company(
            name = focusData.razaoSocial,
            document = focusData.cnpj,
            simplesNacional = focusData.isOptanteSimplesNacional,
            codigoMunicipio = focusData.endereco.codigoMunicipio,
            codigoSiafi = focusData.endereco.codigoSiafi,
            codigoIbge = focusData.endereco.codigoIbge,
        )

        return companyRepository.save(company).also { savedCompany ->
            logger.infoJson("Empresa salva no banco de dados:",
                mapOf("id" to savedCompany.id, "name" to savedCompany.name, "document" to savedCompany.document),
                jsonLogger)
        }
    }



    /**
     * Step 5: Cria e salva o endereço da empresa.
     */
    private fun createCompanyAddress(company: Company, fullCompanyData: FullCompanyData) {
        logger.debug("Criando endereço para empresa ID: ${company.id}")

        val endereco = fullCompanyData.focusData.endereco

        val address = Address(
            entityType = EntityType.COMPANY,
            entityId = company.id!!,
            street = endereco.logradouro,
            number = endereco.numero,
            complement = endereco.complemento.takeIf { it.isNotBlank() },
            neighborhood = endereco.bairro,
            city = endereco.nomeMunicipio,
            state = endereco.uf,
            zipCode = endereco.cep,
            isPrimary = true,
            addressType = AddressType.FISCAL,
        )

        addressRepository.save(address)
        logger.debugJson("Endereço criado para empresa:",
            mapOf("companyId" to company.id, "address" to address), jsonLogger)
    }

    /**
     * Step 6: Configura CNAE e serviços municipais para a empresa.
     */
    private fun setupCompanyServices(company: Company, fullCompanyData: FullCompanyData) {
        logger.debug("Configurando CNAE e serviços para empresa ID: ${company.id}")

        val cnaePrincipal = fullCompanyData.focusData.cnaePrincipal
        if (cnaePrincipal.isBlank()) {
            logger.warn("CNAE principal não encontrado para empresa: ${company.document}")
            return
        }

        // Busca ou cria CNAE
        val cnae = findOrCreateCnae(cnaePrincipal)

        // Busca ou cria código de serviço municipal
        val codigoMunicipio = company.codigoMunicipio ?: ""
        val codigoServico = getOrCreateDefaultCodigoServico(codigoMunicipio)

        // Associa CNAE e código de serviço à empresa
        val companyCnaeServico = CompanyCnaeServico(
            companyId = company.id,
            cnaeId = cnae.id!!,
            codigoServicoId = codigoServico.id!!,
            isPrimary = true,
        )

        companyCnaeServicoRepository.save(companyCnaeServico)
        logger.debugJson("Serviços configurados para empresa:",
            mapOf("companyId" to company.id, "cnaeId" to cnae.id, "codigoServicoId" to codigoServico.id),
            jsonLogger)
    }

    /**
     * Busca ou cria um CNAE.
     */
    private fun findOrCreateCnae(cnaePrincipal: String): Cnae {
        logger.debug("Buscando CNAE: $cnaePrincipal")

        return cnaeRepository.findByCode(cnaePrincipal) ?: run {
            logger.info("CNAE não encontrado, criando novo: $cnaePrincipal")
            val newCnae = Cnae(
                code = cnaePrincipal,
                description = "CNAE $cnaePrincipal", // Descrição temporária
            )
            cnaeRepository.save(newCnae)
        }
    }

    /**
     * Step 7: Converte Company para CompanyDto.
     */
    private fun mapToCompanyDto(company: Company): CompanyDto {
        return CompanyDto(
            id = company.id,
            uuid = company.uuid,
            name = company.name,
            document = company.document,
            municipalRegistration = company.municipalRegistration,
            stateRegistration = company.stateRegistration,
            simplesNacional = company.simplesNacional,
            codigoMunicipio = company.codigoMunicipio,
            codigoSiafi = company.codigoSiafi,
            codigoIbge = company.codigoIbge,
            naturezaJuridica = company.naturezaJuridica,
            isActive = company.isActive,
            createdAt = company.createdAt,
            updatedAt = company.updatedAt,
            focusId = company.focusId ?: "",
        )
    }

    /**
     * Busca ou cria um código de serviço municipal padrão para o município informado.
     */
    private fun getOrCreateDefaultCodigoServico(codigoMunicipio: String): CodigoServicoMunicipal {
        logger.debug("Buscando código de serviço municipal para: $codigoMunicipio")

        // Tenta encontrar um código de serviço qualquer para o município
        val existingCodigos = codigoServicoMunicipalRepository.findByCodigoMunicipio(codigoMunicipio)
        if (existingCodigos.isNotEmpty()) {
            logger.debug("Código de serviço encontrado para município: $codigoMunicipio")
            return existingCodigos.first()
        }

        // Se não existir nenhum, cria um código padrão
        logger.info("Criando código de serviço padrão para município: $codigoMunicipio")
        val codigoServico = CodigoServicoMunicipal(
            codigo = "0000", // Código genérico
            descricao = "Serviço Padrão - Município $codigoMunicipio",
            codigoMunicipio = codigoMunicipio,
        )

        return codigoServicoMunicipalRepository.save(codigoServico)
    }
}
