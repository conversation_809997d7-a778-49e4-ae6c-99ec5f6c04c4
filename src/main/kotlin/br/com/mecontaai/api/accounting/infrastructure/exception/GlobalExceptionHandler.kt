package br.com.mecontaai.api.accounting.infrastructure.exception

import br.com.mecontaai.api.accounting.domain.exception.CNPJDataNotFoundException
import br.com.mecontaai.api.accounting.domain.exception.DocumentAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.exception.EmailAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.exception.UserException
import br.com.mecontaai.api.accounting.domain.exception.UserNotFoundException
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.ReceitaWsCnpjNotFoundException
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.ReceitaWsException
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import java.time.LocalDateTime

/**
 * Global exception handler for the application.
 */
@ControllerAdvice
class GlobalExceptionHandler {

    /**
     * Handle validation exceptions.
     */
    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleValidationExceptions(ex: MethodArgumentNotValidException): ResponseEntity<ErrorResponse> {
        val errors = ex.bindingResult.fieldErrors.associate { it.field to (it.defaultMessage ?: "Erro de validação") }
        
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.BAD_REQUEST.value(),
            error = "Erro de validação",
            message = "Há erros de validação nos campos informados",
            details = errors
        )
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse)
    }

    /**
     * Handle user not found exceptions.
     */
    @ExceptionHandler(UserNotFoundException::class)
    fun handleUserNotFoundException(ex: UserNotFoundException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.NOT_FOUND.value(),
            error = "Usuário não encontrado",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse)
    }

    /**
     * Handle email already exists exceptions.
     */
    @ExceptionHandler(EmailAlreadyExistsException::class)
    fun handleEmailAlreadyExistsException(ex: EmailAlreadyExistsException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.CONFLICT.value(),
            error = "Email já cadastrado",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse)
    }
    
    /**
     * Handle document already exists exceptions.
     */
    @ExceptionHandler(DocumentAlreadyExistsException::class)
    fun handleDocumentAlreadyExistsException(ex: DocumentAlreadyExistsException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.CONFLICT.value(),
            error = "Documento já cadastrado",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse)
    }

    /**
     * Handle generic user exceptions.
     */
    @ExceptionHandler(UserException::class)
    fun handleUserException(ex: UserException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.BAD_REQUEST.value(),
            error = "Erro relacionado ao usuário",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse)
    }

    /**
     * Handle CNPJ not found exceptions from Receita WS.
     */
    @ExceptionHandler(ReceitaWsCnpjNotFoundException::class)
    fun handleReceitaWsCnpjNotFoundException(ex: ReceitaWsCnpjNotFoundException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.NOT_FOUND.value(),
            error = "CNPJ não encontrado",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse)
    }

    /**
     * Handle generic Receita WS exceptions.
     */
    @ExceptionHandler(ReceitaWsException::class)
    fun handleReceitaWsException(ex: ReceitaWsException): ResponseEntity<ErrorResponse> {
        val status = if (ex.httpStatus > 0) HttpStatus.valueOf(ex.httpStatus) else HttpStatus.INTERNAL_SERVER_ERROR
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = status.value(),
            error = "Erro na Receita WS",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(status).body(errorResponse)
    }

    /**
     * Handle CNPJ data not found exceptions.
     */
    @ExceptionHandler(CNPJDataNotFoundException::class)
    fun handleCNPJDataNotFoundException(ex: CNPJDataNotFoundException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.NOT_FOUND.value(),
            error = "Dados do CNPJ não encontrados",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse)
    }

    /**
     * Handle all other exceptions.
     */
    @ExceptionHandler(Exception::class)
    fun handleGenericException(ex: Exception): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.INTERNAL_SERVER_ERROR.value(),
            error = "Erro interno do servidor",
            message = "Ocorreu um erro inesperado. Por favor, tente novamente mais tarde.",
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
    }
}

/**
 * Standard error response.
 */
data class ErrorResponse(
    val timestamp: LocalDateTime,
    val status: Int,
    val error: String,
    val message: String?,
    val details: Any?
) 