package br.com.mecontaai.api.accounting.infrastructure.exception

import br.com.mecontaai.api.accounting.domain.exception.CNPJDataNotFoundException
import br.com.mecontaai.api.accounting.domain.exception.CompanyDocumentAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.exception.CompanyNotFoundException
import br.com.mecontaai.api.accounting.domain.exception.CompanyValidationException
import br.com.mecontaai.api.accounting.domain.exception.CustomerAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.exception.CustomerNotFoundException
import br.com.mecontaai.api.accounting.domain.exception.DocumentAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.exception.EmailAlreadyExistsException
import br.com.mecontaai.api.accounting.domain.exception.NfseException
import br.com.mecontaai.api.accounting.domain.exception.UserException
import br.com.mecontaai.api.accounting.domain.exception.UserNotFoundException
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusException
import br.com.mecontaai.api.accounting.infrastructure.client.infosimples.InfoSimplesException
import br.com.mecontaai.api.accounting.infrastructure.client.infosimples.InfoSimplesCnpjNotFoundException
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.ReceitaWsCnpjNotFoundException
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.ReceitaWsException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import java.time.LocalDateTime

/**
 * Global exception handler for the application.
 *
 * This class handles all exceptions thrown by controllers and provides
 * consistent error responses following REST API best practices.
 */
@ControllerAdvice
class GlobalExceptionHandler {

    private val logger = LoggerFactory.getLogger(javaClass)

    /**
     * Handle validation exceptions.
     */
    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleValidationExceptions(ex: MethodArgumentNotValidException): ResponseEntity<ErrorResponse> {
        val errors = ex.bindingResult.fieldErrors.associate { it.field to (it.defaultMessage ?: "Erro de validação") }
        
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.BAD_REQUEST.value(),
            error = "Erro de validação",
            message = "Há erros de validação nos campos informados",
            details = errors
        )
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse)
    }

    /**
     * Handle user not found exceptions.
     */
    @ExceptionHandler(UserNotFoundException::class)
    fun handleUserNotFoundException(ex: UserNotFoundException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.NOT_FOUND.value(),
            error = "Usuário não encontrado",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse)
    }

    /**
     * Handle email already exists exceptions.
     */
    @ExceptionHandler(EmailAlreadyExistsException::class)
    fun handleEmailAlreadyExistsException(ex: EmailAlreadyExistsException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.CONFLICT.value(),
            error = "Email já cadastrado",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse)
    }
    
    /**
     * Handle document already exists exceptions.
     */
    @ExceptionHandler(DocumentAlreadyExistsException::class)
    fun handleDocumentAlreadyExistsException(ex: DocumentAlreadyExistsException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.CONFLICT.value(),
            error = "Documento já cadastrado",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse)
    }

    /**
     * Handle generic user exceptions.
     */
    @ExceptionHandler(UserException::class)
    fun handleUserException(ex: UserException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.BAD_REQUEST.value(),
            error = "Erro relacionado ao usuário",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse)
    }

    /**
     * Handle CNPJ not found exceptions from Receita WS.
     */
    @ExceptionHandler(ReceitaWsCnpjNotFoundException::class)
    fun handleReceitaWsCnpjNotFoundException(ex: ReceitaWsCnpjNotFoundException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.NOT_FOUND.value(),
            error = "CNPJ não encontrado",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse)
    }

    /**
     * Handle generic Receita WS exceptions.
     */
    @ExceptionHandler(ReceitaWsException::class)
    fun handleReceitaWsException(ex: ReceitaWsException): ResponseEntity<ErrorResponse> {
        val status = if (ex.httpStatus > 0) HttpStatus.valueOf(ex.httpStatus) else HttpStatus.INTERNAL_SERVER_ERROR
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = status.value(),
            error = "Erro na Receita WS",
            message = ex.message,
            details = null
        )
        
        return ResponseEntity.status(status).body(errorResponse)
    }

    /**
     * Handle CNPJ data not found exceptions.
     */
    @ExceptionHandler(CNPJDataNotFoundException::class)
    fun handleCNPJDataNotFoundException(ex: CNPJDataNotFoundException): ResponseEntity<ErrorResponse> {
        logger.warn("CNPJ data not found: {}", ex.message)
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.NOT_FOUND.value(),
            error = "Dados do CNPJ não encontrados",
            message = ex.message,
            details = null
        )

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse)
    }

    /**
     * Handle company document already exists exceptions.
     */
    @ExceptionHandler(CompanyDocumentAlreadyExistsException::class)
    fun handleCompanyDocumentAlreadyExistsException(ex: CompanyDocumentAlreadyExistsException): ResponseEntity<ErrorResponse> {
        logger.warn("Company document already exists: {}", ex.message)
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.CONFLICT.value(),
            error = "Documento da empresa já existe",
            message = ex.message,
            details = null
        )

        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse)
    }

    /**
     * Handle company not found exceptions.
     */
    @ExceptionHandler(CompanyNotFoundException::class)
    fun handleCompanyNotFoundException(ex: CompanyNotFoundException): ResponseEntity<ErrorResponse> {
        logger.warn("Company not found: {}", ex.message)
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.NOT_FOUND.value(),
            error = "Empresa não encontrada",
            message = ex.message,
            details = null
        )

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse)
    }

    /**
     * Handle company validation exceptions.
     */
    @ExceptionHandler(CompanyValidationException::class)
    fun handleCompanyValidationException(ex: CompanyValidationException): ResponseEntity<ErrorResponse> {
        logger.warn("Company validation failed: {}", ex.message)
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.BAD_REQUEST.value(),
            error = "Erro de validação da empresa",
            message = ex.message,
            details = null
        )

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse)
    }

    /**
     * Handle customer not found exceptions.
     */
    @ExceptionHandler(CustomerNotFoundException::class)
    fun handleCustomerNotFoundException(ex: CustomerNotFoundException): ResponseEntity<ErrorResponse> {
        logger.warn("Customer not found: {}", ex.message)
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.NOT_FOUND.value(),
            error = "Cliente não encontrado",
            message = ex.message,
            details = null
        )

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse)
    }

    /**
     * Handle customer already exists exceptions.
     */
    @ExceptionHandler(CustomerAlreadyExistsException::class)
    fun handleCustomerAlreadyExistsException(ex: CustomerAlreadyExistsException): ResponseEntity<ErrorResponse> {
        logger.warn("Customer already exists: {}", ex.message)
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.CONFLICT.value(),
            error = "Cliente já existe",
            message = ex.message,
            details = null
        )

        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse)
    }

    /**
     * Handle NFSe exceptions.
     */
    @ExceptionHandler(NfseException::class)
    fun handleNfseException(ex: NfseException): ResponseEntity<ErrorResponse> {
        logger.warn("NFSe error: {}", ex.message)
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.BAD_REQUEST.value(),
            error = "Erro na NFSe",
            message = ex.message,
            details = null
        )

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse)
    }

    /**
     * Handle Focus API exceptions.
     */
    @ExceptionHandler(FocusException::class)
    fun handleFocusException(ex: FocusException): ResponseEntity<ErrorResponse> {
        logger.error("Focus API error: Status {}, Code: {}, Message: {}", ex.httpStatus, ex.errorCode, ex.message)

        val httpStatus = when (ex.httpStatus) {
            400 -> HttpStatus.BAD_REQUEST
            401, 403 -> HttpStatus.FORBIDDEN
            404 -> HttpStatus.NOT_FOUND
            422 -> HttpStatus.UNPROCESSABLE_ENTITY
            429 -> HttpStatus.TOO_MANY_REQUESTS
            in 500..599 -> HttpStatus.INTERNAL_SERVER_ERROR
            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }

        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = httpStatus.value(),
            error = "Erro na API Focus",
            message = ex.message,
            details = mapOf(
                "errorCode" to ex.errorCode,
                "httpStatus" to ex.httpStatus
            )
        )

        return ResponseEntity.status(httpStatus).body(errorResponse)
    }

    /**
     * Handle InfoSimples CNPJ not found exceptions.
     */
    @ExceptionHandler(InfoSimplesCnpjNotFoundException::class)
    fun handleInfoSimplesCnpjNotFoundException(ex: InfoSimplesCnpjNotFoundException): ResponseEntity<ErrorResponse> {
        logger.warn("InfoSimples CNPJ not found: {}", ex.message)
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.NOT_FOUND.value(),
            error = "CNPJ não encontrado no InfoSimples",
            message = ex.message,
            details = null
        )

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse)
    }

    /**
     * Handle InfoSimples API exceptions.
     */
    @ExceptionHandler(InfoSimplesException::class)
    fun handleInfoSimplesException(ex: InfoSimplesException): ResponseEntity<ErrorResponse> {
        logger.error("InfoSimples API error: Status {}, Code: {}, Message: {}", ex.httpStatus, ex.errorCode, ex.message)

        val httpStatus = when (ex.httpStatus) {
            400 -> HttpStatus.BAD_REQUEST
            401, 403 -> HttpStatus.FORBIDDEN
            404 -> HttpStatus.NOT_FOUND
            429 -> HttpStatus.TOO_MANY_REQUESTS
            in 500..599 -> HttpStatus.INTERNAL_SERVER_ERROR
            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }

        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = httpStatus.value(),
            error = "Erro na API InfoSimples",
            message = ex.message,
            details = mapOf(
                "errorCode" to ex.errorCode,
                "httpStatus" to ex.httpStatus
            )
        )

        return ResponseEntity.status(httpStatus).body(errorResponse)
    }

    /**
     * Handle illegal argument exceptions.
     */
    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(ex: IllegalArgumentException): ResponseEntity<ErrorResponse> {
        logger.warn("Illegal argument: {}", ex.message)
        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.BAD_REQUEST.value(),
            error = "Argumento inválido",
            message = ex.message,
            details = null
        )

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse)
    }

    /**
     * Handle all other exceptions.
     * This is the fallback handler for any unhandled exceptions.
     */
    @ExceptionHandler(Exception::class)
    fun handleGenericException(ex: Exception): ResponseEntity<ErrorResponse> {
        logger.error("Unhandled exception occurred: {}", ex.message, ex)

        val errorResponse = ErrorResponse(
            timestamp = LocalDateTime.now(),
            status = HttpStatus.INTERNAL_SERVER_ERROR.value(),
            error = "Erro interno do servidor",
            message = "Ocorreu um erro inesperado. Por favor, tente novamente mais tarde.",
            details = null
        )

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
    }
}

/**
 * Standard error response.
 */
data class ErrorResponse(
    val timestamp: LocalDateTime,
    val status: Int,
    val error: String,
    val message: String?,
    val details: Any?
) 