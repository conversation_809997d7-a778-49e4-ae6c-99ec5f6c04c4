package br.com.mecontaai.api.accounting.application.service.flow

import br.com.mecontaai.api.accounting.domain.service.flow.FlowHandler
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Routes flow requests to the appropriate flow handler based on flow_id.
 */
@Service
class FlowRouter(
    private val handlers: List<FlowHandler>
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val handlerMap = handlers.associateBy { it.flowId }
    
    init {
        logger.info("🔄 FlowRouter initialized with ${handlers.size} handlers:")
        handlers.forEach { handler ->
            logger.info("  - ${handler.flowId} -> ${handler.javaClass.simpleName}")
        }
    }
    
    /**
     * Route a flow request to the appropriate handler
     */
    fun route(request: FlowRequest): FlowResponse {
        val flowId = extractFlowId(request)
        logger.info("🎯 Routing request to flow: $flowId")
        
        val handler = handlerMap[flowId] 
            ?: throw IllegalArgumentException("Unknown flow: $flowId. Available flows: ${handlerMap.keys}")
            
        return handler.handle(request)
    }
    
    /**
     * Extract flow ID from request.
     * Priority: flow_id field > flow_token > screen prefix > default to "common"
     */
    private fun extractFlowId(request: FlowRequest): String {
        // 1. Check explicit flow_id field (new approach)
        request.flowId?.let { 
            logger.debug("Flow ID from flow_id field: $it")
            return it 
        }
    }
}
