package br.com.mecontaai.api.accounting.application.service.flow

import br.com.mecontaai.api.accounting.domain.service.flow.FlowHandler
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Routes flow requests to the appropriate flow handler based on flow_id.
 */
@Service
class FlowRouter(
    handlers: List<FlowHandler>
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val handlerMap = handlers.associateBy { it.flowId }
    
    init {
        logger.info("🔄 FlowRouter initialized with ${handlers.size} handlers:")
        var count = 1
        handlers.forEach { handler ->
            logger.info("$count  - ${handler.flowId} -> ${handler.javaClass.simpleName}")
            count += 1
        }
    }
    
    /**
     * Route a flow request to the appropriate handler
     */
    fun route(request: FlowRequest): FlowResponse {
        // Handle ping action directly since all flows use the same endpoint
        if (request.action.lowercase() == "ping") {
            logger.info("🏓 Handling ping action directly")
            return FlowResponse(data = mapOf("status" to "active"))
        }

        val flowId = request.data?.get("flow_id")?.toString()
        logger.info("🎯 Routing request to flow: $flowId")

        // Require explicit flow_id for data_exchange actions
        if (flowId.isNullOrBlank()) {
            throw IllegalArgumentException("No flow_id provided in request.data. All flows must specify flow_id in data.")
        }

        val handler = handlerMap[flowId]
            ?: throw IllegalArgumentException("Unknown flow: $flowId. Available flows: ${handlerMap.keys}")

        return handler.handle(request)
    }
}
