package br.com.mecontaai.api.accounting.application.service

import br.com.mecontaai.api.accounting.domain.exception.CNPJDataNotFoundException
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.ReceitaWsClient
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.ReceitaWsException
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto
import org.springframework.stereotype.Service

/**
 * Serviço para consulta de CNPJs na Receita WS.
 */
@Service
class ReceitaWsService(
    private val receitaWsClient: ReceitaWsClient
) {
    /**
     * Consulta dados de um CNPJ na Receita WS.
     *
     * @param cnpj CNPJ a ser consultado (com ou sem formatação)
     * @return Dados do CNPJ consultado
     */
    fun consultarCnpj(cnpj: String): ReceitaWsResponseDto {
        return receitaWsClient.consultarCnpj(cnpj)
    }
} 