package br.com.mecontaai.api.accounting.infrastructure.webhook.processor

import br.com.mecontaai.api.accounting.domain.service.ChatService
import br.com.mecontaai.api.accounting.domain.service.MessageManagerService
import br.com.mecontaai.api.accounting.domain.service.callback.ButtonCallbackService
import br.com.mecontaai.api.accounting.presentation.dto.webhook.MetaWebhookPayload
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class ButtonWebhookProcessor(
    private val buttonCallbackService: ButtonCallbackService,
    private val chatService: ChatService,
    private val messageManagerService: MessageManagerService
) {

    private val logger = LoggerFactory.getLogger(javaClass)

    fun processButtonClick(message: MetaWebhookPayload.Message, metaPhoneNumberId: String) {
        logger.info("🔘 Processando clique de botão")
        logger.info("📱 From: ${message.from}")
        logger.info("📞 Meta Phone Number ID: $metaPhoneNumberId")
        logger.info("📋 Message: $message")

        try {
            // Extrai o payload do webhook
            val payload = extractPayloadFromWebhook(message)
            if (payload == null) {
                logger.warn("⚠️ Payload não encontrado no webhook")
                return
            }

            logger.info("📦 Payload extraído: $payload")

            // Parse do payload (formato: callbackId:clickedValue)
            val (callbackId, clickedValue) = parsePayload(payload)
            logger.info("🆔 Callback ID: $callbackId")
            logger.info("👆 Clicked Value: $clickedValue")

            // Busca ou cria o chat
            val chat = chatService.findOrCreateChat(
                userPhoneNumber = message.from ?: "",
                metaPhoneNumberId = metaPhoneNumberId
            )

            logger.info("💬 Chat encontrado: ${chat.uuid}")

            // Executa o callback
            val result = buttonCallbackService.executeCallback(callbackId, clickedValue, chat)

            if (result != null) {
                logger.info("✅ Callback executado com sucesso")
                
                // Envia a resposta para o usuário
                val responses = result.agentResponses ?: emptyList()
                if (responses.isNotEmpty()) {
                    messageManagerService.sendAgentResponses(chat, responses)
                    logger.info("📤 ${responses.size} resposta(s) enviada(s) para o usuário")
                } else {
                    logger.warn("⚠️ Nenhuma resposta para enviar")
                }
            } else {
                logger.warn("⚠️ Callback não encontrado ou expirado: $callbackId")
                
                // Envia mensagem de fallback
                messageManagerService.sendAgentResponse(
                    chat,
                    br.com.mecontaai.api.accounting.domain.model.response.AgentResponse.TextResponse(
                        "Esta ação não está mais disponível."
                    )
                )
            }

        } catch (e: Exception) {
            logger.error("❌ Erro ao processar clique de botão", e)
        }
    }

    private fun extractPayloadFromWebhook(message: MetaWebhookPayload.Message): String? {
        // Extrai o payload do botão clicado
        // Para botões interativos, o payload está em interactive.button_reply.id
        val buttonReply = message.interactive?.buttonReply
        if (buttonReply?.id != null) {
            logger.info("📦 Payload encontrado em button_reply.id: ${buttonReply.id}")
            return buttonReply.id
        }

        // Para botões simples, o payload está em button.payload
        val buttonPayload = message.button?.payload
        if (buttonPayload != null) {
            logger.info("📦 Payload encontrado em button.payload: $buttonPayload")
            return buttonPayload
        }

        // Fallback: usar texto da mensagem para teste
        val textPayload = message.text?.body
        if (textPayload != null) {
            logger.info("📦 Usando texto como payload para teste: $textPayload")
            return textPayload
        }

        logger.warn("⚠️ Nenhum payload encontrado na mensagem")
        return null
    }

    private fun parsePayload(payload: String): Pair<String, String> {
        val parts = payload.split(":")
        if (parts.size != 2) {
            logger.error("❌ Formato de payload inválido: $payload")
            throw IllegalArgumentException("Formato de payload inválido: $payload")
        }
        return Pair(parts[0], parts[1])
    }
}
