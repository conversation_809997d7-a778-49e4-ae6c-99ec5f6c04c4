package br.com.mecontaai.api.accounting.application.usecase

/**
 * Interface para o caso de uso de enviar NFS-e por email.
 */
interface EnviarEmailNfseUseCase {
    
    /**
     * Envia uma NFS-e por email.
     *
     * @param request dados para envio por email
     */
    fun execute(request: EnviarEmailNfseRequest)
}

/**
 * Request para envio de NFS-e por email.
 */
data class EnviarEmailNfseRequest(
    /**
     * ID da empresa
     */
    val companyId: Long,
    
    /**
     * Referência única da NFS-e
     */
    val reference: String,
    
    /**
     * Lista de emails para envio
     */
    val emails: List<String>
) 