package br.com.mecontaai.api.accounting.infrastructure.client.focus.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.OffsetDateTime

/**
 * Response DTO for querying the status of an NFSe.
 *
 * This structure covers the various states an NFSe can be in,
 * including processing, authorized, error, or canceled.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class NfseStatusResponseDto(
    /**
     * CNPJ of the service provider
     */
    val cnpj_prestador: String,

    /**
     * Reference identifier provided in the creation request
     */
    val ref: String,

    /**
     * RPS (Recibo Provisório de Serviço) number
     */
    val numero_rps: String? = null,

    /**
     * RPS series
     */
    val serie_rps: String? = null,

    /**
     * RPS type
     */
    val tipo_rps: String? = null,

    /**
     * Current status of the NFSe:
     * - "processando_autorizacao": Still being processed
     * - "autorizado": Successfully authorized
     * - "erro_autorizacao": Authorization failed
     * - "cancelado": Successfully canceled
     * - "erro_cancelamento": Cancellation failed
     * - "denegado": Denied by SEFAZ
     */
    val status: String,

    /**
     * NFSe number (only available if authorized)
     */
    val numero: String? = null,

    /**
     * Verification code (only available if authorized)
     */
    val codigo_verificacao: String? = null,

    /**
     * Issue date and time (only available if authorized)
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    val data_emissao: OffsetDateTime? = null,

    /**
     * URL to view the NFSe (only available if authorized)
     */
    val url: String? = null,

    /**
     * Path to the NFSe XML file (only available if authorized)
     */
    val caminho_xml_nota_fiscal: String? = null,

    /**
     * URL to download the DANFSE PDF (only available if authorized)
     */
    val url_danfse: String? = null,

    /**
     * Path to the cancellation XML file (only if canceled)
     */
    val caminho_xml_cancelamento: String? = null,

    /**
     * List of errors (only if status is erro_autorizacao or erro_cancelamento)
     */
    val erros: List<FocusErrorDetailDto>? = null,

    /**
     * Error code for single error responses
     */
    val codigo: String? = null,

    /**
     * Error message for single error responses
     */
    val mensagem: String? = null
)