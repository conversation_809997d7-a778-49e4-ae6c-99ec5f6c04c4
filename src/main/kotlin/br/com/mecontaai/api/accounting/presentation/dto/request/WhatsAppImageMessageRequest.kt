package br.com.mecontaai.api.accounting.presentation.dto.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern

/**
 * DTO para requisição de envio de mensagem com imagem via WhatsApp.
 */
data class WhatsAppImageMessageRequest(
    @field:NotBlank(message = "O número do destinatário é obrigatório")
    @field:Pattern(regexp = "^\\+[1-9]\\d{1,14}$", message = "O número deve estar no formato internacional (ex: +*************)")
    val to: String,

    @field:NotBlank(message = "O ID do número do WhatsApp é obrigatório")
    val metaPhoneNumberId: String,

    val image: Image? = null
) {
    data class Image(
        val id: String? = null,
        val link: String? = null,
        val caption: String? = null
    )
} 