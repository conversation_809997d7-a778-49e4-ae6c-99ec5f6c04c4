package br.com.mecontaai.api.accounting.domain.service.impl

import br.com.mecontaai.api.accounting.domain.entity.User
import br.com.mecontaai.api.accounting.domain.service.UserService
import br.com.mecontaai.api.accounting.infrastructure.database.model.UserEntity
import br.com.mecontaai.api.accounting.infrastructure.database.repository.UserEntityRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Implementation of UserService.
 */
@Service
class UserServiceImpl(
    private val userEntityRepository: UserEntityRepository
) : UserService {
    
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun findById(id: Long): User? {
        logger.debug("Buscando user por ID: $id")
        
        return userEntityRepository.findById(id)
            .map { mapToUser(it) }
            .orElse(null)
    }

    /**
     * Mapeia UserEntity para User.
     */
    private fun mapToUser(entity: UserEntity): User {
        return User(
            id = entity.id,
            name = entity.name,
            email = entity.email,
            password = entity.password,
            document = entity.document
        )
    }
} 