package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*
import java.time.LocalDate

/**
 * Entity class representing a digital certificate in the database.
 */
@Entity
@Table(name = "certificates")
class CertificateEntity(
    @Column(name = "company_id", nullable = false)
    val companyId: Long,

    @Column(name = "expiration_date", nullable = false)
    val expirationDate: LocalDate,

    @Column(name = "blob_uuid", nullable = false)
    val blobUuid: String? = null,

    @Column(name = "password")
    val password: String? = null
) : BaseEntity() {

    /**
     * Verifica se o certificado está válido (não expirado).
     *
     * @return true se o certificado estiver válido, false caso contrário
     */
    fun isValid(): Boolean {
        return expirationDate.isAfter(LocalDate.now()) || expirationDate.isEqual(LocalDate.now())
    }

    /**
     * Verifica se o certificado está próximo do vencimento (30 dias).
     *
     * @return true se o certificado vencer em até 30 dias, false caso contrário
     */
    fun isExpiringSoon(): Boolean {
        val thirtyDaysFromNow = LocalDate.now().plusDays(30)
        return expirationDate.isBefore(thirtyDaysFromNow) || expirationDate.isEqual(thirtyDaysFromNow)
    }

    /**
     * Desativa o certificado.
     */
    fun deactivate() {
        this.isActive = false
    }

    /**
     * Ativa o certificado.
     */
    fun activate() {
        this.isActive = true
    }
} 