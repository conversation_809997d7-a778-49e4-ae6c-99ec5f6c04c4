package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.Customer
import java.util.UUID

/**
 * Repository interface for Customer operations.
 */
interface CustomerRepository {

    /**
     * Save a customer to the database.
     *
     * @param customer The customer to save
     * @return The saved customer with generated ID
     */
    fun save(customer: Customer): Customer

    /**
     * Find a customer by ID.
     *
     * @param id The customer ID
     * @return The customer if found, null otherwise
     */
    fun findById(id: Long): Customer?

    /**
     * Find a customer by UUID.
     *
     * @param uuid The customer UUID
     * @return The customer if found, null otherwise
     */
    fun findByUuid(uuid: UUID): Customer?

    /**
     * Find a customer by document (CPF or CNPJ).
     *
     * @param document The document number
     * @return The customer if found, null otherwise
     */
    fun findByDocument(document: String): Customer?

    /**
     * Find a customer by document and company ID.
     *
     * @param document The document number
     * @param companyId The company ID
     * @return The customer if found, null otherwise
     */
    fun findByDocumentAndCompanyId(document: String, companyId: Long): Customer?

    /**
     * Find all customers for a specific company.
     *
     * @param companyId The company ID
     * @return List of customers belonging to the company
     */
    fun findByCompanyId(companyId: Long): List<Customer>

    /**
     * Finds all customers.
     *
     * @return List of all customers
     */
    fun findAll(): List<Customer>
}