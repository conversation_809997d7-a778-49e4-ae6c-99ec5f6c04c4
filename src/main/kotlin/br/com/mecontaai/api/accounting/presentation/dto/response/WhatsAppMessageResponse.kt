package br.com.mecontaai.api.accounting.presentation.dto.response

data class WhatsAppMessageResponse(
    val messagingProduct: String,
    val contacts: List<WhatsAppContactResponse>,
    val messages: List<WhatsAppMessageResponse>
) {
    data class WhatsAppContactResponse(
        val input: String,
        val waId: String
    )

    data class WhatsAppMessageResponse(
        val id: String
    )
} 