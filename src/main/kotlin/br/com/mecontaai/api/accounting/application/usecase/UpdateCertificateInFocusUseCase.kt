package br.com.mecontaai.api.accounting.application.usecase

import br.com.mecontaai.api.accounting.domain.entity.Certificate
import br.com.mecontaai.api.accounting.domain.service.CertificateService
import br.com.mecontaai.api.accounting.domain.service.CompanyService
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusClient
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusUpdateCertificateRequestDto
import br.com.mecontaai.api.accounting.infrastructure.client.focus.dto.FocusUpdateCertificateResponseDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class UpdateCertificateInFocusUseCase(
    private val focusClient: FocusClient,
    private val companyService: CompanyService,
    private val certificateService: CertificateService,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    @Transactional
    fun execute(
        companyId: Long,
        certificateBlobUuid: String,
    ): FocusUpdateCertificateResponseDto {
        logger.info("Iniciando atualização de certificado na Focus. companyId={}, certificateBlobUuid={}", companyId, certificateBlobUuid)

        logger.info("Buscando empresa no banco de dados")
        val company =
            companyService.findById(companyId)
                ?: throw IllegalArgumentException("Empresa não encontrada com ID: $companyId")
        logger.info("Empresa encontrada. name={}, document={}", company.name, company.document)

        logger.info("Buscando certificado")
        val certificate =
            certificateService.findByBlobUuid(certificateBlobUuid)
                ?: throw IllegalArgumentException("Certificado não encontrado com UUID: $certificateBlobUuid")

        logger.info("Verificando se a empresa tem ID da Focus")
        val focusId =
            company.focusId
                ?: throw IllegalArgumentException("Empresa não possui ID da Focus: $companyId")
        logger.info("ID da Focus encontrado: {}", focusId)

        logger.info("Mapeando dados para o DTO da Focus")
        val request = mapCertificateToFocusRequest(certificate)
        logger.info("DTO da Focus criado")

        logger.info("Enviando requisição para atualizar certificado na Focus")
        val response = updateCertificateInFocus(focusId, request)
        logger.info("Certificado atualizado na Focus com sucesso. Response: {}", response)

        return response
    }

    private fun updateCertificateInFocus(
        focusId: String,
        request: FocusUpdateCertificateRequestDto,
    ): FocusUpdateCertificateResponseDto =
        try {
            logger.info("Tentando atualizar certificado na Focus. focusId={}", focusId)
            focusClient.updateCertificate(focusId, request)
        } catch (e: Exception) {
            logger.error("Erro ao atualizar certificado na Focus. focusId={}, erro={}", focusId, e.message, e)
            throw RuntimeException("Erro ao atualizar certificado na Focus: ${e.message}", e)
        }

    private fun mapCertificateToFocusRequest(certificate: Certificate): FocusUpdateCertificateRequestDto {
        logger.info("Iniciando mapeamento para DTO da Focus")

        val fileContent = certificateService.getFile(certificate.id!!)
        logger.info("Arquivo do certificado obtido com sucesso")

        return FocusUpdateCertificateRequestDto(
            arquivo_certificado_base64 = Base64.getEncoder().encodeToString(fileContent),
            senha_certificado = certificate.password!!,
        ).also {
            logger.info("DTO da Focus mapeado com sucesso")
        }
    }
}
