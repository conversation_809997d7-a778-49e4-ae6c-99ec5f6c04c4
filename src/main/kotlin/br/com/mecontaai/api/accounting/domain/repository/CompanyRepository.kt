package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.Company
import java.util.UUID

/**
 * Interface para o repositório de empresas.
 */
interface CompanyRepository {
    /**
     * Salva uma empresa no banco de dados.
     *
     * @param company Empresa a ser salva
     * @return Empresa salva
     */
    fun save(company: Company): Company

    /**
     * Busca uma empresa pelo seu ID.
     *
     * @param id ID da empresa
     * @return Empresa se encontrada, ou null
     */
    fun findById(id: Long): Company?

    /**
     * Busca uma empresa pelo seu UUID.
     *
     * @param uuid UUID da empresa
     * @return Empresa se encontrada, ou null
     */
    fun findByUuid(uuid: UUID): Company?

    /**
     * Busca uma empresa pelo seu documento (CNPJ).
     *
     * @param document Documento da empresa (CNPJ)
     * @return Empresa se encontrada, ou null
     */
    fun findByDocument(document: String): Company?

    /**
     * Verifica se existe uma empresa com o documento informado.
     *
     * @param document Documento da empresa (CNPJ)
     * @return true se existir, false caso contrário
     */
    fun existsByDocument(document: String): Boolean

    /**
     * Busca todas as empresas.
     *
     * @return Lista de empresas
     */
    fun findAll(): List<Company>
} 