package br.com.mecontaai.api.accounting.domain.repository

import br.com.mecontaai.api.accounting.domain.entity.Address
import br.com.mecontaai.api.accounting.domain.enum.EntityType

/**
 * Interface para o repositório de endereços.
 */
interface AddressRepository {
    /**
     * Salva um endereço no banco de dados.
     *
     * @param address Endereço a ser salvo
     * @return Endereço salvo
     */
    fun save(address: Address): Address

    /**
     * Busca um endereço pelo seu ID.
     *
     * @param id ID do endereço
     * @return Endereço se encontrado, ou null
     */
    fun findById(id: Long): Address?

    /**
     * Busca um endereço pelo seu UUID.
     *
     * @param uuid UUID do endereço
     * @return Endereço se encontrado, ou null
     */
    fun findByUuid(uuid: String): Address?

    /**
     * Busca endereços pelo tipo da entidade e ID da entidade.
     *
     * @param entityType Tipo da entidade (ex: COMPANY, USER)
     * @param entityId ID da entidade
     * @return Lista de endereços encontrados
     */
    fun findByEntityTypeAndEntityId(entityType: EntityType, entityId: Long): List<Address>

    /**
     * Busca o endereço principal de uma entidade.
     *
     * @param entityType Tipo da entidade
     * @param entityId ID da entidade
     * @return Endereço principal se encontrado, ou null
     */
    fun findPrimaryByEntityTypeAndEntityId(entityType: EntityType, entityId: Long): Address?
} 