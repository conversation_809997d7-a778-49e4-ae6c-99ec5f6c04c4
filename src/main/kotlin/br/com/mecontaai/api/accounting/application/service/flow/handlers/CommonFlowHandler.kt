package br.com.mecontaai.api.accounting.application.service.flow.handlers

import br.com.mecontaai.api.accounting.domain.service.flow.FlowHandler
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowRequest
import br.com.mecontaai.api.accounting.presentation.dto.flows.FlowResponse
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Handler for common flow actions that don't belong to a specific flow
 */
@Component
class CommonFlowHandler : FlowHandler {
    
    private val logger = LoggerFactory.getLogger(javaClass)
    override val flowId = "common"
    
    override fun handle(request: FlowRequest): FlowResponse {
        logger.info("🔧 CommonFlowHandler processing action: ${request.action}")
        
        return when (request.action.lowercase()) {
            "ping" -> handlePing()
            else -> FlowResponse(
                screen = "ERROR",
                data = mapOf("error" to "Unknown action: ${request.action}")
            )
        }
    }
    
    private fun handlePing(): FlowResponse {
        logger.info("🏓 Handling ping for common flow")
        // Meta expects exactly: {"data": {"status": "active"}}
        return FlowResponse(
            screen = null, // Meta doesn't expect screen for ping
            data = mapOf("status" to "active")
        )
    }
}
