package br.com.mecontaai.api.accounting.domain.service.step

import br.com.mecontaai.api.accounting.domain.model.ChatMessageEvent
import br.com.mecontaai.api.accounting.domain.service.ChatMessageService
import br.com.mecontaai.api.accounting.domain.service.ContextChatMessageService
import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.database.model.ContextChatMessage
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Step responsible for persisting the chat message and its context.
 * This step handles message storage and context management.
 */
@Component
class MessagePersistenceStep(
    private val chatMessageService: ChatMessageService,
    private val contextChatMessageService: ContextChatMessageService,
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(MessagePersistenceStep::class.java)

    /**
     * Persists the chat message and its context.
     *
     * @param chatMessageEvent The chat message event to persist
     * @param chat The chat entity where the message belongs
     */
    fun execute(
        chatMessageEvent: ChatMessageEvent,
        chat: ChatEntity,
    ) {
        logger.debug("Persisting message: ${chatMessageEvent.externalMessageId}")

        // Save the message
        val savedMessage = chatMessageService.saveMessage(chatMessageEvent, chat)
        logger.info("Mensagem salva com ID ${savedMessage.id} e UUID ${savedMessage.uuid}")

        // Create JSON content for the message
        val messageContent =
            objectMapper.createObjectNode().apply {
                put("role", "user")
                put("content", chatMessageEvent.message ?: "[Mensagem sem texto]")
            }

        // Save the user message context
        val contextMessage =
            ContextChatMessage(
                chatId = chat.id!!,
                role = "user",
                content = objectMapper.writeValueAsString(messageContent),
                externalMessageId = chatMessageEvent.externalMessageId,
            )

        contextChatMessageService.save(contextMessage)
        logger.info("Contexto da mensagem do usuário salvo com UUID ${contextMessage.uuid}")
    }
}
