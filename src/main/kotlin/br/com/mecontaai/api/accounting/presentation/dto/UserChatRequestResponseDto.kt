package br.com.mecontaai.api.accounting.presentation.dto

import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import java.util.UUID

/**
 * DTO for adding user to chat requests.
 */
data class AddUserToChatRequestDto(
    /**
     * User's ID.
     */
    @field:NotNull(message = "User ID is required")
    @field:Positive(message = "User ID must be positive")
    val userId: Long,
    
    /**
     * Chat's ID.
     */
    @field:NotNull(message = "Chat ID is required")
    @field:Positive(message = "Chat ID must be positive")
    val chatId: Long
)

/**
 * DTO for adding company to chat requests.
 */
data class AddCompanyToChatRequestDto(
    /**
     * User's ID.
     */
    @field:NotNull(message = "User ID is required")
    @field:Positive(message = "User ID must be positive")
    val userId: Long,
    
    /**
     * Chat's ID.
     */
    @field:NotNull(message = "Chat ID is required")
    @field:Positive(message = "Chat ID must be positive")
    val chatId: Long,
    
    /**
     * Company's ID.
     */
    @field:NotNull(message = "Company ID is required")
    @field:Positive(message = "Company ID must be positive")
    val companyId: Long
)

/**
 * DTO for user chat responses.
 */
data class UserChatResponseDto(
    /**
     * UserChat relationship UUID.
     */
    val uuid: UUID,
    
    /**
     * Message indicating the operation result.
     */
    val message: String
) 