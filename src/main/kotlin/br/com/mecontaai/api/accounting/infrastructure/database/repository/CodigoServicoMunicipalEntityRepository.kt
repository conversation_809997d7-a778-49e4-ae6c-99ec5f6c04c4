package br.com.mecontaai.api.accounting.infrastructure.database.repository

import br.com.mecontaai.api.accounting.infrastructure.database.model.CodigoServicoMunicipalEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

/**
 * JPA repository for CodigoServicoMunicipalEntity.
 */
@Repository
interface CodigoServicoMunicipalEntityRepository : JpaRepository<CodigoServicoMunicipalEntity, Long> {
    /**
     * Find a service code by UUID.
     *
     * @param uuid The UUID to search for
     * @return The service code entity wrapped in an Optional
     */
    fun findByUuid(uuid: UUID): Optional<CodigoServicoMunicipalEntity>

    /**
     * Find a service code by code and municipality code.
     *
     * @param codigo The service code
     * @param codigoMunicipio The municipality code
     * @return The service code entity wrapped in an Optional
     */
    fun findByCodigoAndCodigoMunicipio(codigo: String, codigoMunicipio: String): Optional<CodigoServicoMunicipalEntity>

    /**
     * Find all service codes for a municipality.
     *
     * @param codigoMunicipio The municipality code
     * @return A list of service code entities
     */
    fun findByCodigoMunicipio(codigoMunicipio: String): List<CodigoServicoMunicipalEntity>
} 