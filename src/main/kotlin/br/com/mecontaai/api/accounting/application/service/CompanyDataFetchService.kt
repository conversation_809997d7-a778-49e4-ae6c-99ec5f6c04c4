package br.com.mecontaai.api.accounting.application.service

import br.com.mecontaai.api.accounting.application.dto.FullCompanyDataDto
import br.com.mecontaai.api.accounting.domain.exception.CNPJDataNotFoundException
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusClient
import br.com.mecontaai.api.accounting.infrastructure.client.focus.FocusException
import br.com.mecontaai.api.accounting.infrastructure.logging.JsonLogger
import br.com.mecontaai.api.accounting.infrastructure.logging.debugJson
import br.com.mecontaai.api.accounting.infrastructure.logging.errorJson
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Service responsible for fetching complete company data from multiple third-party API providers.
 * 
 * This service abstracts the complexity of calling multiple APIs (Focus and ReceitaWS) and
 * provides a unified interface for retrieving comprehensive company information.
 * It can be reused across different parts of the application that need full company data.
 */
@Service
class CompanyDataFetchService(
    private val focusClient: FocusClient,
    private val receitaWsService: ReceitaWsService,
    private val jsonLogger: JsonLogger,
) {

    private val logger = LoggerFactory.getLogger(javaClass)

    /**
     * Fetches complete company data from multiple sources (Focus API and ReceitaWS API).
     * 
     * This method calls both APIs sequentially and combines their responses into a single
     * composite object. Both API calls must succeed for the method to return successfully.
     * 
     * @param cnpj The CNPJ to fetch data for (can include formatting)
     * @return FullCompanyDataDto containing raw responses from both APIs
     * @throws CNPJDataNotFoundException if either API fails or CNPJ is not found
     */
    fun fetchFullCompanyData(cnpj: String): FullCompanyDataDto {
        logger.debug("Iniciando busca de dados completos do CNPJ: $cnpj")
        
        try {
            // Fetch data from Focus API
            logger.debug("Consultando dados do CNPJ na API Focus: $cnpj")
            val focusData = focusClient.consultarCnpj(cnpj)
            logger.debugJson("Dados do CNPJ obtidos da API Focus:", focusData, jsonLogger)
            
            // Fetch data from ReceitaWS API
            logger.debug("Consultando dados do CNPJ na ReceitaWS: $cnpj")
            val receitaWsData = receitaWsService.consultarCnpj(cnpj)
            logger.debugJson("Dados do CNPJ obtidos da ReceitaWS:", receitaWsData, jsonLogger)
            
            // Combine both responses
            val fullCompanyData = FullCompanyDataDto(
                cnpj = cnpj,
                focusData = focusData,
                receitaWsData = receitaWsData
            )
            
            logger.debugJson("Dados completos do CNPJ obtidos com sucesso:", fullCompanyData, jsonLogger)
            return fullCompanyData
            
        } catch (e: FocusException) {
            logger.errorJson("Erro da API Focus ao consultar CNPJ:",
                mapOf("cnpj" to cnpj, "error" to e.message), jsonLogger, e)
            throw CNPJDataNotFoundException(cnpj)
        } catch (e: Exception) {
            logger.errorJson("Erro inesperado ao consultar dados completos do CNPJ:",
                mapOf("cnpj" to cnpj, "error" to e.message), jsonLogger, e)
            throw CNPJDataNotFoundException(cnpj)
        }
    }
}
