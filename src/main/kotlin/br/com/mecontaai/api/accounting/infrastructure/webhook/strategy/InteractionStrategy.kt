package br.com.mecontaai.api.accounting.infrastructure.webhook.strategy

import br.com.mecontaai.api.accounting.infrastructure.database.model.ChatEntity
import br.com.mecontaai.api.accounting.infrastructure.webhook.model.InteractivePayload
import br.com.mecontaai.api.accounting.infrastructure.webhook.model.ProcessingResult

/**
 * Interface para estratégias de processamento de interações do Meta/WhatsApp.
 * Cada tipo de interação (botões, listas, flows) deve implementar esta interface.
 */
interface InteractionStrategy {
    
    /**
     * Verifica se esta estratégia pode processar o tipo de interação.
     */
    fun canHandle(interactionType: String): Boolean
    
    /**
     * Processa a interação e retorna o resultado.
     */
    fun process(interaction: InteractivePayload, chat: ChatEntity): ProcessingResult
    
    /**
     * Retorna o tipo de interação que esta estratégia processa.
     */
    fun getInteractionType(): String
}
