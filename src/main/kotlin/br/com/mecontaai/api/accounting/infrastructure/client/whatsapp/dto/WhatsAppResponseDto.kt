package br.com.mecontaai.api.accounting.infrastructure.client.whatsapp.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class WhatsAppResponseDto(
    @JsonProperty("messaging_product")
    val messagingProduct: String,
    val contacts: List<WhatsAppContactResponseDto>,
    val messages: List<WhatsAppMessageResponseDto>
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class WhatsAppContactResponseDto(
    val input: String,
    @JsonProperty("wa_id")
    val waId: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class WhatsAppMessageResponseDto(
    val id: String
) 