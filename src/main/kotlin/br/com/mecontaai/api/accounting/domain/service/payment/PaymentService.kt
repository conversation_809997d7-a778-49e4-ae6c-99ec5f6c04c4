package br.com.mecontaai.api.accounting.domain.service.payment

import java.math.BigDecimal

/**
 * Serviço de domínio para operações de pagamento.
 * Abstrai a implementação específica do provedor de pagamento (Stripe, PagSeguro, etc.)
 */
interface PaymentService {
    
    /**
     * Cria um link de pagamento para um plano de assinatura.
     * 
     * @param planId ID do plano
     * @param planName Nome do plano
     * @param amount Valor do pagamento
     * @param currency Moeda (padrão: BRL)
     * @param customerEmail Email do cliente
     * @param customerName Nome do cliente (opcional)
     * @param successUrl URL de sucesso após pagamento
     * @param cancelUrl URL de cancelamento
     * @return PaymentLinkResult com o link e informações do pagamento
     */
    fun createSubscriptionPaymentLink(
        planId: String,
        planName: String,
        amount: BigDecimal,
        currency: String = "BRL",
        customerEmail: String,
        customerName: String? = null,
        successUrl: String? = null,
        cancelUrl: String? = null
    ): PaymentLinkResult
    
    /**
     * Cria um link de pagamento único (não recorrente).
     * 
     * @param description Descrição do pagamento
     * @param amount Valor do pagamento
     * @param currency Moeda (padrão: BRL)
     * @param customerEmail Email do cliente
     * @param customerName Nome do cliente (opcional)
     * @param successUrl URL de sucesso após pagamento
     * @param cancelUrl URL de cancelamento
     * @return PaymentLinkResult com o link e informações do pagamento
     */
    fun createOneTimePaymentLink(
        description: String,
        amount: BigDecimal,
        currency: String = "BRL",
        customerEmail: String,
        customerName: String? = null,
        successUrl: String? = null,
        cancelUrl: String? = null
    ): PaymentLinkResult
    
    /**
     * Verifica o status de um pagamento.
     * 
     * @param paymentId ID do pagamento
     * @return PaymentStatus com informações do status
     */
    fun getPaymentStatus(paymentId: String): PaymentStatus
}

/**
 * Resultado da criação de um link de pagamento.
 */
data class PaymentLinkResult(
    val paymentUrl: String,
    val paymentId: String,
    val expiresAt: String? = null,
    val success: Boolean = true,
    val errorMessage: String? = null
)

/**
 * Status de um pagamento.
 */
data class PaymentStatus(
    val paymentId: String,
    val status: PaymentStatusEnum,
    val amount: BigDecimal? = null,
    val currency: String? = null,
    val paidAt: String? = null,
    val errorMessage: String? = null
)

/**
 * Enum para status de pagamento.
 */
enum class PaymentStatusEnum {
    PENDING,
    PROCESSING,
    SUCCEEDED,
    FAILED,
    CANCELED,
    EXPIRED
}
