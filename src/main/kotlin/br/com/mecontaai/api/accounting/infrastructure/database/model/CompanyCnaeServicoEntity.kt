package br.com.mecontaai.api.accounting.infrastructure.database.model

import jakarta.persistence.*

/**
 * Entity class representing a company-CNAE-service code association in the database.
 */
@Entity
@Table(name = "companies_cnaes_servicos")
class CompanyCnaeServicoEntity(
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id", nullable = false)
    val company: CompanyEntity,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cnae_id", nullable = false)
    val cnae: CnaeEntity,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "codigo_servico_id", nullable = false)
    val codigoServico: CodigoServicoMunicipalEntity,

    @Column(name = "is_primary")
    val isPrimary: Boolean = false
) : BaseEntity()