package br.com.mecontaai.api.infrastructure.client.whatsapp.dto

import com.fasterxml.jackson.annotation.JsonProperty

data class WhatsAppInteractiveMessageDto(
    @JsonProperty("messaging_product")
    val messagingProduct: String = "whatsapp",
    @JsonProperty("recipient_type")
    val recipientType: String = "individual",
    val to: String,
    val type: String = "interactive",
    val interactive: Interactive,
) {
    data class Interactive(
        val type: String, // Exemplo: "button"
        val header: Header?,
        val body: Body,
        val action: Action,
    )

    data class Header(
        val type: String, // Exemplo: "text"
        val text: String?, // Texto ou conteúdo do cabeçalho (opcional)
    )

    data class Body(
        val text: String, // Corpo da mensagem (obrigatório)
    )

    data class Action(
        val buttons: List<Button>? = null, // Botões interativos (para tipo "button")
        val button: String? = null, // Texto do botão da lista (para tipo "list")
        val sections: List<Section>? = null, // Seções da lista (para tipo "list")
    )

    data class Button(
        val type: String = "reply", // Tipo de botão (sempre "reply")
        val reply: Reply,
    )

    data class Reply(
        val id: String, // ID único do botão
        val title: String, // Texto do botão exibido para o usuário
    )

    // Classes para suporte a listas
    data class Section(
        val title: String? = null, // Título da seção (opcional)
        val rows: List<Row>, // Linhas da seção
    )

    data class Row(
        val id: String, // ID único da linha
        val title: String, // Título da linha
        val description: String? = null, // Descrição da linha (opcional)
    )
}
