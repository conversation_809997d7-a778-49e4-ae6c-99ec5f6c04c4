package br.com.mecontaai.api.application.usecase.impl

import br.com.mecontaai.api.accounting.application.usecase.impl.ValidateCompanyUseCaseImpl
import br.com.mecontaai.api.accounting.application.service.ReceitaWsService
import br.com.mecontaai.api.accounting.domain.enum.CompanyValidationErrorType
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.AtividadeDto
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.QsaDto
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.SimplesDto
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ValidateCompanyUseCaseImplTest {

    @Mock
    private lateinit var receitaWsService: ReceitaWsService

    @InjectMocks
    private lateinit var validateCompanyUseCase: ValidateCompanyUseCaseImpl

    @Test
    fun `deve validar CNPJ com sucesso quando atender a todos os requisitos`() {
        // Arrange
        val cnpj = "**************"
        
        // Criar objeto de resposta com todos os dados necessários
        val simplesMock = Mockito.mock(SimplesDto::class.java)
        Mockito.`when`(simplesMock.optante).thenReturn(true)
        
        val atividadeMock = Mockito.mock(AtividadeDto::class.java)
        Mockito.`when`(atividadeMock.code).thenReturn("1234")
        Mockito.`when`(atividadeMock.text).thenReturn("ATIVIDADE TESTE")
        
        val qsaDto = Mockito.mock(QsaDto::class.java)
        
        val responseMock = Mockito.mock(ReceitaWsResponseDto::class.java)
        Mockito.`when`(responseMock.situacao).thenReturn("ATIVA")
        Mockito.`when`(responseMock.cnpj).thenReturn(cnpj)
        Mockito.`when`(responseMock.nome).thenReturn("EMPRESA TESTE LTDA")
        Mockito.`when`(responseMock.simples).thenReturn(simplesMock)
        Mockito.`when`(responseMock.qsa).thenReturn(listOf(qsaDto))
        Mockito.`when`(responseMock.atividade_principal).thenReturn(listOf(atividadeMock))
        
        // Mock do service retornando nossa resposta mockada
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertEquals(true, result.isValid)
        assertEquals(null, result.errorType)
        assertEquals(null, result.errorMessage)
    }
    
    @Test
    fun `deve retornar resultado inválido quando empresa não estiver com situação ATIVA`() {
        // Arrange
        val cnpj = "**************"
        
        // Criar objeto de resposta com situação não ativa
        val responseMock = Mockito.mock(ReceitaWsResponseDto::class.java)
        Mockito.`when`(responseMock.situacao).thenReturn("BAIXADA")
        Mockito.`when`(responseMock.cnpj).thenReturn(cnpj)
        
        // Mock do service retornando nossa resposta mockada
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertEquals(false, result.isValid)
        assertEquals(CompanyValidationErrorType.CNPJ_NOT_ACTIVE, result.errorType)
        assertEquals("CNPJ não está ativo: $cnpj", result.errorMessage)
    }
    
    @Test
    fun `deve retornar resultado inválido quando empresa não for optante do Simples Nacional`() {
        // Arrange
        val cnpj = "**************"
        
        // Criar objeto de resposta com situação ativa mas não optante do Simples
        val simplesMock = Mockito.mock(SimplesDto::class.java)
        Mockito.`when`(simplesMock.optante).thenReturn(false)
        
        val responseMock = Mockito.mock(ReceitaWsResponseDto::class.java)
        Mockito.`when`(responseMock.situacao).thenReturn("ATIVA")
        Mockito.`when`(responseMock.cnpj).thenReturn(cnpj)
        Mockito.`when`(responseMock.simples).thenReturn(simplesMock)
        
        // Mock do service retornando nossa resposta mockada
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertEquals(false, result.isValid)
        assertEquals(CompanyValidationErrorType.NOT_SIMPLES_NACIONAL, result.errorType)
        assertEquals("Empresa não é optante do Simples Nacional: $cnpj", result.errorMessage)
    }
    
    @Test
    fun `deve retornar resultado inválido quando empresa não tiver sócios`() {
        // Arrange
        val cnpj = "**************"
        
        // Criar objeto de resposta com situação ativa, simples nacional mas sem sócios
        val simplesMock = Mockito.mock(SimplesDto::class.java)
        Mockito.`when`(simplesMock.optante).thenReturn(true)
        
        val responseMock = Mockito.mock(ReceitaWsResponseDto::class.java)
        Mockito.`when`(responseMock.situacao).thenReturn("ATIVA")
        Mockito.`when`(responseMock.cnpj).thenReturn(cnpj)
        Mockito.`when`(responseMock.simples).thenReturn(simplesMock)
        Mockito.`when`(responseMock.qsa).thenReturn(emptyList())
        
        // Mock do service retornando nossa resposta mockada
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertEquals(false, result.isValid)
        assertEquals(CompanyValidationErrorType.NO_PARTNERS, result.errorType)
    }
    
    @Test
    fun `deve retornar resultado inválido quando empresa tiver mais de um sócio`() {
        // Arrange
        val cnpj = "**************"
        
        // Criar objeto de resposta com situação ativa, simples nacional mas com mais de um sócio
        val simplesMock = Mockito.mock(SimplesDto::class.java)
        Mockito.`when`(simplesMock.optante).thenReturn(true)
        
        val qsaDto1 = Mockito.mock(QsaDto::class.java)
        val qsaDto2 = Mockito.mock(QsaDto::class.java)
        
        val responseMock = Mockito.mock(ReceitaWsResponseDto::class.java)
        Mockito.`when`(responseMock.situacao).thenReturn("ATIVA")
        Mockito.`when`(responseMock.cnpj).thenReturn(cnpj)
        Mockito.`when`(responseMock.simples).thenReturn(simplesMock)
        Mockito.`when`(responseMock.qsa).thenReturn(listOf(qsaDto1, qsaDto2))
        
        // Mock do service retornando nossa resposta mockada
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertEquals(false, result.isValid)
        assertEquals(CompanyValidationErrorType.MULTIPLE_PARTNERS, result.errorType)
    }
} 