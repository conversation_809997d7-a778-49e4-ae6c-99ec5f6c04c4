package br.com.mecontaai.api.accounting.application.usecase.impl

import br.com.mecontaai.api.accounting.application.service.ReceitaWsService
import br.com.mecontaai.api.accounting.application.usecase.impl.ValidateCompanyUseCaseImpl
import br.com.mecontaai.api.accounting.domain.enum.CompanyValidationErrorType
import br.com.mecontaai.api.accounting.domain.enum.NaturezaJuridicaEnum
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.AtividadeDto
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.QsaDto
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.ReceitaWsResponseDto
import br.com.mecontaai.api.accounting.infrastructure.client.receitaws.dto.SimplesDto
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension

/**
 * Testes para validar o padrão de validação baseado na natureza jurídica.
 */
@ExtendWith(MockitoExtension::class)
class ValidateCompanyUseCaseImplPatternTest {

    @Mock
    private lateinit var receitaWsService: ReceitaWsService

    @InjectMocks
    private lateinit var validateCompanyUseCase: ValidateCompanyUseCaseImpl

    @Test
    fun `deve validar EMPRESARIO_INDIVIDUAL com sucesso sem validar QSA`() {
        // Arrange
        val cnpj = "**************"
        
        val simplesMock = SimplesDto(optante = true)
        val atividadeMock = AtividadeDto(code = "1234", text = "ATIVIDADE TESTE")
        
        val responseMock = ReceitaWsResponseDto(
            situacao = "ATIVA",
            cnpj = cnpj,
            nome = "EMPRESA TESTE LTDA",
            simples = simplesMock,
            atividade_principal = listOf(atividadeMock),
            natureza_juridica = "213-5 - Empresário (Individual)",
            qsa = emptyList(), // Empresário Individual pode ter QSA vazio
            cep = "12345678",
            logradouro = "RUA TESTE",
            numero = "123",
            bairro = "BAIRRO TESTE",
            municipio = "CIDADE TESTE",
            uf = "SP"
        )
        
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertTrue(result.isValid)
        assertEquals(1, result.cnpjData?.quantidadeSocios) // Sempre 1 para Empresário Individual
        assertFalse(result.cnpjData?.possuiSocioPessoaJuridica ?: true) // Sempre false para Empresário Individual
        assertEquals(NaturezaJuridicaEnum.EMPRESARIO_INDIVIDUAL, result.cnpjData?.naturezaJuridica)
    }

    @Test
    fun `deve validar SOCIEDADE_EMPRESARIA_LIMITADA com sucesso quando tiver exatamente um sócio PF`() {
        // Arrange
        val cnpj = "**************"
        
        val simplesMock = SimplesDto(optante = true)
        val atividadeMock = AtividadeDto(code = "1234", text = "ATIVIDADE TESTE")
        val qsaDto = QsaDto(nome = "JOÃO DA SILVA", qual = "SÓCIO", nome_rep_legal = null)
        
        val responseMock = ReceitaWsResponseDto(
            situacao = "ATIVA",
            cnpj = cnpj,
            nome = "EMPRESA TESTE LTDA",
            simples = simplesMock,
            atividade_principal = listOf(atividadeMock),
            natureza_juridica = "206-2 - Sociedade Empresária Limitada",
            qsa = listOf(qsaDto),
            cep = "12345678",
            logradouro = "RUA TESTE",
            numero = "123",
            bairro = "BAIRRO TESTE",
            municipio = "CIDADE TESTE",
            uf = "SP"
        )
        
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertTrue(result.isValid)
        assertEquals(1, result.cnpjData?.quantidadeSocios)
        assertFalse(result.cnpjData?.possuiSocioPessoaJuridica ?: true)
        assertEquals(NaturezaJuridicaEnum.SOCIEDADE_EMPRESARIA_LIMITADA, result.cnpjData?.naturezaJuridica)
    }

    @Test
    fun `deve rejeitar SOCIEDADE_EMPRESARIA_LIMITADA quando tiver sócio pessoa jurídica`() {
        // Arrange
        val cnpj = "**************"
        
        val simplesMock = SimplesDto(optante = true)
        val qsaDto = QsaDto(nome = "EMPRESA SÓCIA LTDA", qual = "SÓCIO", nome_rep_legal = "JOÃO REPRESENTANTE")
        
        val responseMock = ReceitaWsResponseDto(
            situacao = "ATIVA",
            cnpj = cnpj,
            nome = "EMPRESA TESTE LTDA",
            simples = simplesMock,
            natureza_juridica = "206-2 - Sociedade Empresária Limitada",
            qsa = listOf(qsaDto),
            cep = "12345678",
            logradouro = "RUA TESTE",
            numero = "123",
            bairro = "BAIRRO TESTE",
            municipio = "CIDADE TESTE",
            uf = "SP"
        )
        
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertFalse(result.isValid)
        assertEquals(CompanyValidationErrorType.NON_INDIVIDUAL_PARTNER, result.errorType)
        assertEquals("Empresa deve ter apenas sócio pessoa física, encontrado pessoa jurídica", result.errorMessage)
    }

    @Test
    fun `deve rejeitar SOCIEDADE_EMPRESARIA_LIMITADA quando tiver múltiplos sócios`() {
        // Arrange
        val cnpj = "**************"
        
        val simplesMock = SimplesDto(optante = true)
        val qsaDto1 = QsaDto(nome = "JOÃO DA SILVA", qual = "SÓCIO", nome_rep_legal = null)
        val qsaDto2 = QsaDto(nome = "MARIA DA SILVA", qual = "SÓCIO", nome_rep_legal = null)
        
        val responseMock = ReceitaWsResponseDto(
            situacao = "ATIVA",
            cnpj = cnpj,
            nome = "EMPRESA TESTE LTDA",
            simples = simplesMock,
            natureza_juridica = "206-2 - Sociedade Empresária Limitada",
            qsa = listOf(qsaDto1, qsaDto2),
            cep = "12345678",
            logradouro = "RUA TESTE",
            numero = "123",
            bairro = "BAIRRO TESTE",
            municipio = "CIDADE TESTE",
            uf = "SP"
        )
        
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertFalse(result.isValid)
        assertEquals(CompanyValidationErrorType.MULTIPLE_PARTNERS, result.errorType)
        assertEquals("Empresa deve ter exatamente um sócio, encontrado: 2", result.errorMessage)
    }

    @Test
    fun `deve usar estratégia padrão para natureza jurídica não mapeada`() {
        // Arrange
        val cnpj = "**************"
        
        val simplesMock = SimplesDto(optante = true)
        val atividadeMock = AtividadeDto(code = "1234", text = "ATIVIDADE TESTE")
        val qsaDto = QsaDto(nome = "JOÃO DA SILVA", qual = "SÓCIO", nome_rep_legal = null)
        
        val responseMock = ReceitaWsResponseDto(
            situacao = "ATIVA",
            cnpj = cnpj,
            nome = "EMPRESA TESTE LTDA",
            simples = simplesMock,
            atividade_principal = listOf(atividadeMock),
            natureza_juridica = "999-9 - Natureza Não Mapeada",
            qsa = listOf(qsaDto),
            cep = "12345678",
            logradouro = "RUA TESTE",
            numero = "123",
            bairro = "BAIRRO TESTE",
            municipio = "CIDADE TESTE",
            uf = "SP"
        )
        
        Mockito.`when`(receitaWsService.getCnpjData(cnpj)).thenReturn(responseMock)
        
        // Act
        val result = validateCompanyUseCase.execute(cnpj)
        
        // Assert
        assertTrue(result.isValid)
        assertEquals(1, result.cnpjData?.quantidadeSocios) // Usa o tamanho real do QSA
        assertFalse(result.cnpjData?.possuiSocioPessoaJuridica ?: true)
        assertEquals(null, result.cnpjData?.naturezaJuridica) // Natureza não mapeada
    }
}
