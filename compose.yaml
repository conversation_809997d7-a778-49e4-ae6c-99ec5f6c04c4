services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./src:/app/src
    environment:
      - SPRING_DATASOURCE_URL=*******************************************
      - SPRING_DATASOURCE_USERNAME=jafai
      - SPRING_DATASOURCE_PASSWORD=jafai1234
      - SPRING_PROFILES_ACTIVE=local
    depends_on:
      - postgres-jafai

  postgres-jafai:
    image: postgres:latest
    environment:
      - POSTGRES_DB=jafai
      - POSTGRES_USER=jafai
      - POSTGRES_PASSWORD=jafai1234
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  localstack:
    image: localstack/localstack:latest
    container_name: mecontai-localstack
    ports:
      - "4566:4566"
    environment:
      - SERVICES=sqs,s3
      - DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - DEBUG=1
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - localstack_data:/var/lib/localstack

volumes:
  postgres_data:
  localstack_data:
