# MeContAI API - Spring Boot Application in Kotlin

This project is a Spring Boot application developed in Kotlin, designed to provide an API service to be used as the core backend for MeContAI. It leverages technologies such as Spring Data JPA for database interactions, Flyway for database migrations, and Docker for containerization.

## Table of Contents

- [Features](#features)
- [Technologies Used](#technologies-used)
- [Clean Architecture Structure](#clean-architecture-structure)
- [Getting Started](#getting-started)
    - [Prerequisites](#prerequisites)
    - [Running the Application](#running-the-application)
        - [Using Docker Compose](#using-docker-compose)
- [Configuration](#configuration)
- [Database Migration](#database-migration)
- [Testing](#testing)

## Features

- RESTful API implementation
- Database integration with PostgreSQL
- Database migration management using Flyway
- Containerization with Docker and Docker Compose
- Hot-reloading support for efficient development

## Technologies Used

- [Kotlin](https://kotlinlang.org/)
- [Spring Boot](https://spring.io/projects/spring-boot)
- [Spring Data JPA](https://spring.io/projects/spring-data-jpa)
- [Flyway](https://flywaydb.org/)
- [PostgreSQL](https://www.postgresql.org/)
- [Docker](https://www.docker.com/)
- [Gradle](https://gradle.org/install/) (optional, as the project includes the Gradle Wrapper)

## Clean Architecture Structure

This project follows Clean Architecture principles, organizing code into concentric layers with clear responsibilities and dependencies flowing inward. Below is an explanation of the project structure:

### Core Layers

#### Domain Layer (src/main/kotlin/br/com/mecontaai/api/domain)
The innermost layer containing the business rules and logic, independent of any external frameworks or technologies.

- **entity/**: Core business entities and value objects that represent the business concepts
- **repository/**: Repository interfaces defining data access contracts (not implementations)
- **usecase/**: Use case interfaces and implementations that orchestrate business logic
- **service/**: Domain services for complex business logic that doesn't naturally fit in entities

#### Application Layer (src/main/kotlin/br/com/mecontaai/api/application)
The layer that orchestrates the flow of data between the outer layers and the domain layer.

- **service/**: Application services that coordinate domain use cases
- **dto/**: Data Transfer Objects for incoming/outgoing application data
- **mapper/**: Mappers to convert between DTOs and domain entities

### Adapter Layers

#### Infrastructure Layer (src/main/kotlin/br/com/mecontaai/api/infrastructure)
External frameworks, tools, and adapters that interact with external resources.

- **persistence/**: Database-related code
  - **entity/**: JPA entity classes for database mapping
  - **repository/**: JPA repository implementations of domain repository interfaces
- **messaging/**: Messaging/streaming implementation (Kafka, RabbitMQ, etc.)
- **client/**: Third-party API clients and adapters
- **config/**: Configuration classes for frameworks and external services

#### Presentation Layer (src/main/kotlin/br/com/mecontaai/api/presentation)
The outermost layer handling HTTP requests, API responses, and user interfaces.

- **controller/**: REST controllers organized by type:
  - **api/**: REST API controllers for business operations
  - **webhook/**: Controllers for receiving webhooks from external providers
  - **integration/**: Controllers for third-party service integrations
  - **flow/**: Controllers and services for WhatsApp Flow handling
- **dto/**: Request/Response DTOs for API endpoints
- **mapper/**: Mappers to convert between presentation DTOs and application DTOs
- **middleware/**: Request filters, interceptors, and exception handlers

### Key Principles

1. **Dependency Rule**: Dependencies only point inward. Outer layers can depend on inner layers, but inner layers cannot depend on outer layers.
2. **Abstractions**: Inner layers define interfaces, outer layers implement them.
3. **Separation of Concerns**: Each layer has a specific responsibility.
4. **Testability**: Business logic can be tested independently of frameworks and UI.

### Where to Put Common Components

- **Incoming Webhooks**: Create webhook handlers in `presentation/controller/webhook`
- **REST Endpoints**: Define in `presentation/controller/api` with corresponding DTOs in `presentation/dto`
- **Integration Endpoints**: Place third-party service controllers in `presentation/controller/integration`
- **Business Rules**: Place in the appropriate domain entities or use cases in `domain/entity` or `domain/usecase`
- **Utility Classes (formatters, etc.)**: Create in a new package `common/util` or within the layer where they are most used
- **JPA Access**: Implement repository interfaces in `infrastructure/persistence/repository`
- **Messaging/Streaming**: Implement in `infrastructure/messaging`
- **Third-party API Access**: Create clients in `infrastructure/client`

## Getting Started

### Prerequisites

- **JDK 21**: Ensure you have JDK 21 installed. We recommend using a tool like [SDKMAN!](https://sdkman.io/) or [jEnv](https://www.jenv.dev/) to manage your Java versions. Once installed, you can set the project's Java version by running `sdk use java 21.0.2-tem` (for SDKMAN!) or `jenv local 21` (for jEnv) in the project root directory.
- [Docker](https://www.docker.com/get-started)
- [Docker Compose](https://docs.docker.com/compose/install/)
- [Gradle](https://gradle.org/install/) (optional, as the project includes the Gradle Wrapper)

### Running the Application

#### Using Docker Compose

1. **Clone the repository**:
```bash
<NAME_EMAIL>:MeContaAI/mecontai-api.git
```

2. **Build and start the services**:
```bash
docker-compose up --build
```

3. Once the services are up, the API should be accessible at http://localhost:8080/.

## Configuration

The application's primary configurations are located in the `application.yaml` file:

```yaml
server:
  port: 8080

spring:
  application:
    name: api

  # Connection to database
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
```

## Database Migration

This project uses Flyway for database versioning and migrations. Flyway ensures that the database schema is up-to-date and consistent across different environments. Migration scripts should be placed in the `src/main/resources/db/migration` directory and follow Flyway's naming conventions.

**Versioned Migrations**: These migrations are applied in order based on their version number and are typically used for schema changes. The naming convention is:

```
V<Version>__<Description>.sql
```

For example:
```
V1_0__create_user_table.sql
```

## Testing

Unit and integration tests should follow the same architecture as the main code:

- Domain layer tests should focus on business logic without dependencies on infrastructure
- Repository tests can use an in-memory database or test containers
- Controller tests can use MockMvc for Spring MVC testing

## Entity-Relationship Diagram

```mermaid
flowchart TD
    %% Main entities
    USERS(["Users"]):::primary
    CUSTOMERS(["Customers"]):::primary
    COMPANIES(["Companies"]):::primary
    ADDRESSES(["Addresses"]):::primary
    NFSE(["Invoices"]):::primary
    CERTIFICATES(["Certificates"]):::support
    CNAES(["CNAEs"]):::support
    EMPLOYEES(["Employees"]):::primary
    
    %% Relationships
    USERS -->|1:N| ADDRESSES
    COMPANIES -->|1:N| ADDRESSES
    CUSTOMERS -->|1:N| ADDRESSES
    
    USERS -->|M:N| COMPANIES
    USERS_COMPANIES(["Users Companies"]):::junction
    USERS --- USERS_COMPANIES
    USERS_COMPANIES --- COMPANIES
    
    COMPANIES -->|1:N| CUSTOMERS
    COMPANIES -->|1:N| NFSE
    COMPANIES -->|1:N| CERTIFICATES
    CUSTOMERS -->|1:N| NFSE
    
    COMPANIES -->|1:N| EMPLOYEES
    USERS -->|1:N| EMPLOYEES
    
    COMPANIES -->|M:N| CNAES
    COMPANIES_CNAES(["Companies CNAEs"]):::junction
    COMPANIES --- COMPANIES_CNAES
    COMPANIES_CNAES --- CNAES
    
    %% Styles
    classDef primary fill:#e6f7ff,stroke:#1890ff,stroke-width:1px,color:black
    classDef support fill:#fffbe6,stroke:#faad14,stroke-width:1px,color:black
    classDef junction fill:#f0f0f0,stroke:#d9d9d9,stroke-width:1px,color:black
```

### Legend
- <span style="color:#1890ff">■</span> Primary Entities
- <span style="color:#faad14">■</span> Support Entities
- <span style="color:#d9d9d9">■</span> Junction Tables

**Relationships:**
- A user can be associated with multiple companies (M:N relationship with companies)
- A user can have multiple addresses (1:N relationship with addresses, where addresses.entity_type = 'user')
- A user can be linked to multiple employees (1:N relationship with employees)

## Table Descriptions

### Table: users
Stores the users who access the system.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique user identifier |
| name | VARCHAR(255) NOT NULL | User's name |
| email | VARCHAR(255) UNIQUE | Email (used for login) |
| document | VARCHAR(20) | User's document (CPF) |
| password | TEXT NOT NULL | Password (hashed) |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- A user can be associated with multiple companies (M:N relationship with companies)
- A user can have multiple addresses (1:N relationship with addresses, where addresses.entity_type = 'user')
- A user can be linked to multiple employees (1:N relationship with employees)

### Table: companies
Stores the companies that issue invoices.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique company identifier |
| name | VARCHAR(255) NOT NULL | Company name |
| document | VARCHAR(20) NOT NULL UNIQUE | Company tax ID |
| municipal_registration | VARCHAR(20) NOT NULL | Municipal registration |
| state_registration | VARCHAR(20) | State registration (Inscrição Estadual) |
| simples_nacional | BOOLEAN DEFAULT false | Indicates if the company is enrolled in Simples Nacional |
| codigo_municipio | VARCHAR(10) | Código do município conforme Tabela do IBGE (ex: "7535") |
| codigo_siafi | VARCHAR(10) | Código SIAFI do município (ex: "7535") |
| codigo_ibge | VARCHAR(10) | Código IBGE do município (ex: "4106902") |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- A company can have multiple users (M:N relationship with users)
- A company can have multiple customers (1:N relationship with customers)
- A company can issue multiple invoices (1:N relationship with nfse)
- A company can have multiple certificates (1:N relationship with certificates)
- A company can have multiple addresses (1:N relationship with addresses, where addresses.entity_type = 'company')
- A company can have multiple CNAEs (M:N relationship with cnaes through companies_cnaes_servicos)
- A company can have multiple employees (1:N relationship with employees)

### Table: customers
Stores the customers to whom companies provide services.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique customer identifier |
| company_id | BIGINT REFERENCES companies(id) ON DELETE CASCADE | FK to the company that registered the customer |
| name | VARCHAR(255) NOT NULL | Customer name |
| document | VARCHAR(20) NOT NULL | Customer tax ID (CPF/CNPJ) |
| document_type | VARCHAR(10) NOT NULL | Type of document ('CPF' or 'CNPJ') |
| is_foreigner | BOOLEAN DEFAULT false | Indicates if the customer is a foreigner |
| email | VARCHAR(255) | Contact email |
| phone | VARCHAR(20) | Contact phone |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Important note**: The same tax ID (document) can appear multiple times in the table, once for each company that registered this customer. This ensures that each company has its own version of the customer, maintaining data isolation.

**Relationships:**
- A customer belongs to a specific company (N:1 relationship with companies)
- A customer can receive multiple invoices (1:N relationship with nfse)
- A customer can have multiple addresses (1:N relationship with addresses, where addresses.entity_type = 'customer')

### Table: addresses
Centralized table to store addresses for all entities in the system using a polymorphic approach.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique address identifier |
| entity_type | VARCHAR(20) NOT NULL | Entity type ('user', 'company', 'customer', etc.) |
| entity_id | BIGINT NOT NULL | ID of the entity this address belongs to |
| street | VARCHAR(255) NOT NULL | Street name |
| number | VARCHAR(20) NOT NULL | Street number |
| complement | VARCHAR(255) | Additional information |
| neighborhood | VARCHAR(255) NOT NULL | Neighborhood |
| city | VARCHAR(255) NOT NULL | City |
| state | VARCHAR(2) NOT NULL | State code |
| zip_code | VARCHAR(10) NOT NULL | ZIP code |
| is_primary | BOOLEAN DEFAULT false | Indicates if this is the entity's primary address |
| address_type | VARCHAR(50) | Type of address (e.g., 'fiscal', 'correspondence', 'residential') |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- An address belongs to a specific entity (user, company, or customer)
- We use entity_type and entity_id to implement a polymorphic relationship
- This approach allows adding new entity types without schema changes

**Index recommendation:**
- Create a composite index on (entity_type, entity_id) to optimize queries

### Table: users_companies
Junction table that associates users with the companies they have access to, including the chat UUID they use to interact with each company.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique identifier |
| user_id | BIGINT REFERENCES users(id) ON DELETE CASCADE | FK to the user |
| company_id | BIGINT REFERENCES companies(id) ON DELETE CASCADE | FK to the company |
| user_chat_uuid | UUID | UUID of the user's chat account used to interact with this company |
| bot_chat_uuid | UUID | UUID of the bot's chat account assigned to handle this user-company interaction |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- Links users (N:1) with companies (N:1) in a many-to-many relationship
- Stores the chat UUID configuration for each user-company pair

### Table: nfse (Electronic Service Invoices)
Records each invoice issued by a company to a customer.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique invoice identifier |
| company_id | BIGINT REFERENCES companies(id) ON DELETE CASCADE | FK to the issuing company |
| customer_id | BIGINT REFERENCES customers(id) ON DELETE CASCADE | FK to the customer |
| description | TEXT NOT NULL | Service description |
| amount | DECIMAL(10,2) NOT NULL | Service amount |
| status | VARCHAR(20) NOT NULL DEFAULT 'pending' | Status (e.g., 'pending', 'processing', 'authorized', 'cancelled') |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- An invoice is issued by a company (N:1 relationship with companies)
- An invoice is issued to a customer (N:1 relationship with customers)

### Table: certificates (Digital Certificates)
Stores the digital certificates of companies, maintaining a history of previous certificates.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique certificate identifier |
| company_id | BIGINT REFERENCES companies(id) ON DELETE CASCADE | FK to company |
| certificate_path | VARCHAR(255) NOT NULL | Path to the certificate binary file |
| password | VARCHAR(255) | Certificate password (encrypted) |
| expiration_date | DATE NOT NULL | Certificate expiration date |
| is_active | BOOLEAN DEFAULT true | Indicates if this is the company's active certificate |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- A certificate belongs to a company (N:1 relationship with companies)
- A company can have multiple certificates over time, but only one active

### Table: cnaes
Armazena todos os códigos CNAE (Classificação Nacional de Atividades Econômicas) disponíveis no sistema.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique CNAE identifier |
| code | VARCHAR NOT NULL UNIQUE | CNAE code |
| description | VARCHAR NOT NULL | CNAE description |
| simples_nacional_annexo | INT | Annexo do Simples Nacional (1-5) |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- Um CNAE pode ser utilizado por múltiplas empresas (1:N relationship with companies_cnaes_servicos)

### Table: codigos_servico_municipais
Armazena os códigos de serviço municipais utilizados na emissão de notas fiscais de serviço, conforme a lista de serviços do município.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique identifier |
| codigo | VARCHAR NOT NULL UNIQUE | Código de serviço municipal |
| descricao | VARCHAR NOT NULL | Descrição do serviço conforme a lista de serviços do município |
| codigo_municipio | VARCHAR NOT NULL | Código do município ao qual este código de serviço pertence |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- Um código de serviço municipal pode ser associado a múltiplos CNAEs e empresas (1:N relationship with companies_cnaes_servicos)

### Table: companies_cnaes_servicos
Tabela de junção que associa empresas aos seus CNAEs e códigos de serviço municipais.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique identifier |
| company_id | BIGINT REFERENCES companies(id) ON DELETE CASCADE | FK to the company |
| cnae_id | BIGINT REFERENCES cnaes(id) ON DELETE CASCADE | FK to the CNAE |
| codigo_servico_id | BIGINT REFERENCES codigos_servico_municipais(id) ON DELETE CASCADE | FK to the municipal service code |
| is_primary | BOOLEAN DEFAULT false | Indicates if this is the company's primary CNAE |
| is_active | BOOLEAN DEFAULT true | Indicates if this CNAE is active for the company |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- Links companies (N:1) with CNAEs (N:1) and municipal service codes (N:1) in a relationship
- Permite que empresas associem múltiplos CNAEs a múltiplos códigos de serviço municipal
- Facilita a emissão de notas fiscais ao conectar CNAEs com os códigos de serviço municipal corretos

### Table: employees
Armazena os funcionários das empresas cadastradas no sistema.

| Field | Data Type | Description |
|-------|-------------|-----------|
| id | BIGINT PRIMARY KEY AUTO_INCREMENT | Sequential table ID |
| uuid | UUID NOT NULL UNIQUE | Unique employee identifier |
| document | VARCHAR(20) NOT NULL | Employee document (CPF) |
| name | VARCHAR(255) NOT NULL | Employee name |
| type | VARCHAR(50) NOT NULL | Employee type (e.g., 'regular', 'manager', 'director') |
| user_id | BIGINT REFERENCES users(id) | FK to the user account (if the employee has system access) |
| company_id | BIGINT REFERENCES companies(id) ON DELETE CASCADE | FK to the company |
| created_at | TIMESTAMP DEFAULT now() | Creation date |
| updated_at | TIMESTAMP DEFAULT now() | Last update date |

**Relationships:**
- An employee belongs to a company (N:1 relationship with companies)
- An employee can be linked to a user account (N:1 relationship with users)

## Use Cases

### 1. Company Onboarding
### 1.A. Company CNPJ Data Management

The process of onboarding a new company in the system involves querying public data through the Focus API and recording the information in the database:

1. **CNPJ Data Query**:
   - The system receives the CNPJ (company tax ID) of the company to be registered
   - It queries the "Focus Consult CNPJ" endpoint
   - It receives the company's basic data, including corporate name, registration status, main CNAE, tax regime, and address

   **Example of Focus response**:
   ```json
   {
       "razao_social": "SISTEMA BRASILEIRO DE TECNOLOGIA E CONHECIMENTO LTDA",
       "cnpj": "**************",
       "situacao_cadastral": "baixada",
       "cnae_principal": "1830002",
       "optante_simples_nacional": false,
       "optante_mei": false,
       "endereco": {
           "codigo_municipio": "7535",
           "codigo_siafi": "7535",
           "codigo_ibge": "4106902",
           "nome_municipio": "Curitiba",
           "logradouro": "DIOGO MUGIATTI",
           "complemento": "",
           "numero": "660",
           "bairro": "BOQUEIRAO",
           "cep": "81730360",
           "uf": "PR"
       }
   }
   ```

2. **Company Data Storage**:
   - The system creates a new record in the `companies` table with:
     - `name`: Company's corporate name
     - `document`: Formatted CNPJ
     - `simples_nacional`: Simplified Tax System status (true/false)
   
3. **Address Storage**:
   - The system creates a new record in the `addresses` table with:
     - `entity_type`: "company"
     - `entity_id`: ID of the newly registered company
     - `street`: Street
     - `number`: Number
     - `complement`: Complement (if any)
     - `neighborhood`: Neighborhood 
     - `city`: City name
     - `state`: State code
     - `zip_code`: ZIP code
     - `is_primary`: true (primary address)
     - `address_type`: "fiscal"

4. **CNAE Association**:
   - The system checks if the main CNAE already exists in the `cnaes` table
   - If it doesn't exist, it adds the CNAE to the table
   - It verifica se já existe um código de serviço municipal para o CNAE na tabela `codigos_servico_municipais`
   - It creates an association in the `companies_cnaes_servicos` table with:
     - `company_id`: Company ID
     - `cnae_id`: Main CNAE ID
     - `codigo_servico_id`: ID do código de serviço municipal correspondente
     - `is_primary`: true
     - `is_active`: true

5. **User Association**:
   - The system creates an association in the `users_companies` table between the user who registered and the new company
   - It sets initial permissions for the user in this company

6. **Validation and Confirmation**:
   - The system checks the company's registration status
   - If the status is "inactive" or "suspended", it displays an alert to the user
   - It registers the information even in non-ideal situations, but marks it for administrative review

**Questions**:
1. **Missing endpoints**: 
   - Is there an endpoint to retrieve the `municipal_registration` (inscrição municipal)?
   - Is there an endpoint to retrieve the `state_registration` (inscrição estadual)?
   - Where should we look for the company's secondary CNAEs? The CNPJ endpoint only returns the main CNAE.

**Sequence Diagram**:
```mermaid
sequenceDiagram
    actor User
    participant System
    participant FocusAPI
    participant Database
    
    User->>System: Provides CNPJ
    System->>FocusAPI: Queries CNPJ
    FocusAPI-->>System: Returns company data
    
    System->>Database: Saves data in companies table
    System->>Database: Saves address in addresses table
    System->>Database: Associates CNAE in companies_cnaes_servicos table
    System->>Database: Associates user in users_companies table
    
    alt Non-regular status
        System->>User: Displays registration status alert
    end
    
    System-->>User: Confirms company registration
```

### 1.B. Digital Certificate Management

After completing the initial company registration, the next step is to receive and store the company's digital certificate:

1. **Digital Certificate Upload**:
   - The user uploads a digital certificate file (usually a .pfx or .p12 file)
   - The user provides the certificate's password

2. **Certificate Validation**:
   - The system verifies if the certificate is valid
   - The system checks the certificate's expiration date
   - The system verifies if the certificate belongs to the registered company (CNPJ validation)

3. **Certificate Storage**:
   - The system securely stores the certificate file in a protected location
   - The system encrypts the certificate password
   - The system creates a new record in the `certificates` table with:
     - `company_id`: Company ID
     - `certificate_path`: Path to the stored certificate file
     - `password`: Encrypted certificate password
     - `expiration_date`: Certificate expiration date
     - `is_active`: true (active certificate)

4. **Certificate Expiration Monitoring**:
   - The system sets up alerts for upcoming certificate expiration
   - The system notifies users of the company when the certificate is close to expiration
   - The system allows for certificate renewal and maintains the history of previous certificates

**Questions**:
1. **Missing endpoints**: 
   - Is there an endpoint to retrieve the `municipal_registration` (inscrição municipal)?
   - Is there an endpoint to retrieve the `state_registration` (inscrição estadual)?
   - Qual endpoint será usado para verificar a validade do certificado digital?

**Sequence Diagram**:
```mermaid
sequenceDiagram
    actor User
    participant System
    participant ValidationService
    participant Database
    participant FileStorage
    
    User->>System: Uploads certificate & provides password
    System->>ValidationService: Validates certificate
    ValidationService-->>System: Returns validation results
    
    alt Invalid Certificate
        System->>User: Displays validation error
    else Valid Certificate
        System->>FileStorage: Stores certificate securely
        FileStorage-->>System: Returns storage path
        System->>Database: Saves in certificates table
        System->>Database: Sets certificate as active
        System->>User: Confirms certificate storage
    end
```

### 2. NFSe Issuance

The process of issuing an Electronic Service Invoice (NFSe) involves gathering data from the system and sending it to the Focus NFSe API:

1. **Basic Information Input**:
   - The user selects a customer from their list of registered customers
   - The user provides the service description and the amount to be charged
   - The system automatically pre-fills all other required fields from company and customer data

2. **Data Preparation**:
   - The system retrieves the company's data (CNPJ, municipal registration, location)
   - The system retrieves the customer's data (CPF/CNPJ)
   - The system formats the current date and time
   - The system selects the appropriate service item code based on the company's CNAEs

3. **NFSe Request Creation**:
   - The system assembles the request payload for the Focus NFSe API:
     ```json
     {
         "data_emissao": "2024-03-08T15:30:00",
         "natureza_operacao": "1",
         "prestador": {
             "cnpj": "49286707000103",
             "inscricao_municipal": "75743523",
             "codigo_municipio": "3550308"
         },
         "tomador": {
             "cpf": "09308621474"
         },
         "servico": {
             "discriminacao": "DESCRIPTION OF THE SERVICE PROVIDED",
             "item_lista_servico": "02660",
             "valor_servicos": "290.00",
             "iss_retido": "false",
             "codigo_municipio": "3550308"
         }
     }
     ```

4. **Certificate Usage**:
   - The system retrieves the company's active digital certificate
   - The system uses the certificate for authentication with the municipal systems

5. **API Submission**:
   - The system sends the request to the Focus NFSe API
   - The system receives a synchronous response confirming receipt or reporting submission errors

6. **NFSe Database Entry**:
   - The system creates a new record in the `nfse` table with:
     - `company_id`: Issuing company ID
     - `customer_id`: Customer ID
     - `description`: Service description
     - `amount`: Service amount
     - `status`: Initially set to "processing"

7. **Webhook Processing**:
   - Later, the system receives a webhook from the Focus API with the final status
   - The system updates the NFSe record with the final status ("authorized" or "rejected")
   - If authorized, the system stores additional information like the NFSe number and access key

**Questions**:
1. **ISS retention**: Where will the "iss_retido" field value come from? Should it be stored in the customer table or determined by business rules?
2. **Service list item**: How should we manage and map the "item_lista_servico" codes to company services? Will this require a new table? **Resolvido**: A nova tabela `codigos_servico_municipais` armazena os códigos de serviço e a tabela `companies_cnaes_servicos` cria o relacionamento entre CNAEs, códigos de serviço e empresas.

**Sequence Diagram**:
```mermaid
sequenceDiagram
    actor User
    participant System
    participant Database
    participant CertificateManager
    participant FocusAPI
    
    User->>System: Selects customer, provides description and amount
    System->>Database: Retrieves company data
    System->>Database: Retrieves customer data
    System->>CertificateManager: Retrieves active certificate
    
    System->>FocusAPI: Submits NFSe request
    FocusAPI-->>System: Returns receipt confirmation
    
    System->>Database: Creates NFSe record with "processing" status
    System-->>User: Displays submission confirmation
    
    FocusAPI->>System: Later sends webhook with final status
    System->>Database: Updates NFSe status
    
    alt Authorized NFSe
        System->>Database: Stores NFSe number and access key
        System->>User: Notifies about successful issuance
    else Rejected NFSe
        System->>User: Notifies about rejection with reason
    end
```

### 3. WhatsApp Chatbot Integration

The `users_companies` table has been designed to support WhatsApp chatbot integration, allowing users to interact with different companies using different chat accounts:

1. **Chat Configuration per Company**:
   - A user can configure a specific chat account to interact with each company
   - The system stores this configuration in the `users_companies` table (`user_chat_uuid` field)

2. **Bot Assignment**:
   - Each user-company pair can be served by a specific bot account
   - The system manages this assignment through the `bot_chat_uuid` field

3. **Supported Scenarios**:
   - **Case 1**: User uses different accounts for different companies
     - User A uses account CH1 for Company E1 (served by bot CH-BOT1)
     - Same user A uses account CH2 for Company E2 (served by bot CH-BOT1 or CH-BOT2)
   
   - **Case 2**: User uses the same account for different companies
     - User A uses account CH1 for Company E1 (served by bot CH-BOT1)
     - Same user A uses account CH1 for Company E2 (served by bot CH-BOT2)

4. **Interaction Flow**:
   - When a message arrives from a chat account, the system identifies the user
   - The system checks which company is being accessed based on context or conversation
   - The system uses the appropriate bot to respond, as configured in the `users_companies` table

## Physical Database Schema

```mermaid
classDiagram
    class users {
        +BIGINT id PK
        +UUID uuid
        +VARCHAR(255) name
        +VARCHAR(255) email UK
        +VARCHAR(20) document
        +TEXT password
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class companies {
        +BIGINT id PK
        +UUID uuid
        +VARCHAR(255) name
        +VARCHAR(20) document UK
        +VARCHAR(20) municipal_registration
        +VARCHAR(20) state_registration
        +BOOLEAN simples_nacional
        +VARCHAR(10) codigo_municipio
        +VARCHAR(10) codigo_siafi
        +VARCHAR(10) codigo_ibge
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class customers {
        +BIGINT id PK
        +UUID uuid
        +BIGINT company_id FK
        +VARCHAR(255) name
        +VARCHAR(20) document
        +VARCHAR(10) document_type
        +BOOLEAN is_foreigner
        +VARCHAR(255) email
        +VARCHAR(20) phone
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class addresses {
        +BIGINT id PK
        +UUID uuid
        +VARCHAR(20) entity_type
        +BIGINT entity_id
        +VARCHAR(255) street
        +VARCHAR(20) number
        +VARCHAR(255) complement
        +VARCHAR(255) neighborhood
        +VARCHAR(255) city
        +VARCHAR(2) state
        +VARCHAR(10) zip_code
        +BOOLEAN is_primary
        +VARCHAR(50) address_type
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class nfse {
        +BIGINT id PK
        +UUID uuid
        +BIGINT company_id FK
        +BIGINT customer_id FK
        +TEXT description
        +DECIMAL(10,2) amount
        +VARCHAR(20) status
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class certificates {
        +BIGINT id PK
        +UUID uuid
        +BIGINT company_id FK
        +VARCHAR(255) certificate_path
        +VARCHAR(255) password
        +DATE expiration_date
        +BOOLEAN is_active
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class users_companies {
        +BIGINT id PK
        +UUID uuid
        +BIGINT user_id FK
        +BIGINT company_id FK
        +UUID user_chat_uuid
        +UUID bot_chat_uuid
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class cnaes {
        +BIGINT id PK
        +UUID uuid
        +VARCHAR code UK
        +VARCHAR description
        +INT simples_nacional_annexo
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class codigos_servico_municipais {
        +BIGINT id PK
        +UUID uuid
        +VARCHAR codigo UK
        +VARCHAR descricao
        +VARCHAR codigo_municipio
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class companies_cnaes_servicos {
        +BIGINT id PK
        +UUID uuid
        +BIGINT company_id FK
        +BIGINT cnae_id FK
        +BIGINT codigo_servico_id FK
        +BOOLEAN is_primary
        +BOOLEAN is_active
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    class employees {
        +BIGINT id PK
        +UUID uuid
        +VARCHAR(20) document
        +VARCHAR(255) name
        +VARCHAR(50) type
        +BIGINT user_id FK
        +BIGINT company_id FK
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }

    class chats {
        +BIGINT id PK
        +UUID uuid
        +VARCHAR(255) user_phone_number
        +TIMESTAMP conversational_window_started_at
        +VARCHAR(255) meta_phone_number_id
        +BOOLEAN is_active
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }

    class chat_messages {
        +BIGINT id PK
        +UUID uuid
        +BIGINT chat_id FK
        +VARCHAR(255) external_message_id
        +VARCHAR(255) meta_phone_number_id
        +VARCHAR(20) status
        +BOOLEAN is_message_to_me
        +BOOLEAN is_message_from_me
        +BOOLEAN is_group_message
        +BOOLEAN is_forwarded_message
        +BOOLEAN is_from_bot
        +TEXT message
        +VARCHAR(255) message_author_name
        +VARCHAR(255) message_author_phone_number
        +VARCHAR(100) media_mimetype
        +VARCHAR(255) media_download_url
        +VARCHAR(255) media_filename
        +TEXT media_caption
        +TIMESTAMP sent_at
        +TIMESTAMP delivery_at
        +TIMESTAMP read_at
        +UUID originating_message_uuid
        +BOOLEAN is_active
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }

    class users_chats {
        +BIGINT id PK
        +UUID uuid
        +BIGINT user_id FK
        +BIGINT chat_id FK
        +BOOLEAN is_active
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
    }
    
    users "1" -- "N" addresses
    companies "1" -- "N" addresses
    customers "1" -- "N" addresses
    
    users "M" -- "N" companies
    users "1" -- "N" users_companies
    companies "1" -- "N" users_companies
    
    companies "1" -- "N" customers
    customers "1" -- "N" nfse
    companies "1" -- "N" nfse
    companies "1" -- "N" certificates
    companies "1" -- "N" employees
    users "1" -- "N" employees
    
    companies "1" -- "N" companies_cnaes_servicos
    cnaes "1" -- "N" companies_cnaes_servicos
    codigos_servico_municipais "1" -- "N" companies_cnaes_servicos

    chats "1" -- "N" chat_messages
    users "M" -- "N" chats
    users "1" -- "N" users_chats
    chats "1" -- "N" users_chats
```