{"version": "7.1", "data_api_version": "3.0", "routing_model": {"SIGN_UP": ["TERMS_AND_CONDITIONS"], "TERMS_AND_CONDITIONS": []}, "screens": [{"id": "SIGN_UP", "title": "Crie sua conta com JAF!", "terminal": true, "data": {}, "layout": {"type": "SingleColumnLayout", "children": [{"type": "Form", "name": "sign_up_form", "children": [{"type": "TextInput", "required": true, "min-chars": 5, "label": "Nome", "name": "full_name", "input-type": "text"}, {"type": "TextInput", "required": true, "label": "CPF", "max-chars": 11, "min-chars": 11, "name": "cpf", "input-type": "number"}, {"type": "TextInput", "required": true, "label": "E-mail", "name": "email", "input-type": "email"}, {"type": "TextInput", "required": true, "label": "<PERSON><PERSON>", "helper-text": "Crie uma senha com no mínimo 8 caracteres, incluindo letras e números.", "name": "password", "input-type": "password"}, {"type": "TextInput", "required": true, "label": "<PERSON><PERSON><PERSON>", "name": "confirm_password", "input-type": "password"}, {"type": "OptIn", "name": "terms_agreement", "label": "Eu concordo com os termos.", "required": true, "on-click-action": {"name": "navigate", "next": {"type": "screen", "name": "TERMS_AND_CONDITIONS"}, "payload": {}}}, {"type": "Footer", "label": "<PERSON><PERSON><PERSON><PERSON>", "on-click-action": {"name": "data_exchange", "payload": {"full_name": "${form.full_name}", "cpf": "${form.cpf}", "email": "${form.email}", "password": "${form.password}", "confirm_password": "${form.confirm_password}", "terms_agreement": "${form.terms_agreement}"}}}]}]}}, {"id": "TERMS_AND_CONDITIONS", "title": "Terms and conditions", "data": {}, "layout": {"type": "SingleColumnLayout", "children": [{"type": "TextHeading", "text": "Our Terms"}, {"type": "TextSubheading", "text": "Data usage"}, {"type": "TextBody", "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed vitae odio dui. Praesent ut nulla tincidunt, scelerisque augue malesuada, volutpat lorem. Aliquam iaculis ex at diam posuere mollis. Suspendisse eget purus ac tellus interdum pharetra. In quis dolor turpis. Fusce in porttitor enim, vitae efficitur nunc. Fusce dapibus finibus volutpat. Fusce velit mi, ullamcorper ac gravida vitae, blandit quis ex. Fusce ultrices diam et justo blandit, quis consequat nisl euismod. Vestibulum pretium est sem, vitae convallis justo sollicitudin non. Morbi bibendum purus mattis quam condimentum, a scelerisque erat bibendum. Nullam sit amet bibendum lectus."}, {"type": "TextSubheading", "text": "Privacy policy"}, {"type": "TextBody", "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed vitae odio dui. Praesent ut nulla tincidunt, scelerisque augue malesuada, volutpat lorem. Aliquam iaculis ex at diam posuere mollis. Suspendisse eget purus ac tellus interdum pharetra. In quis dolor turpis. Fusce in porttitor enim, vitae efficitur nunc. Fusce dapibus finibus volutpat. Fusce velit mi, ullamcorper ac gravida vitae, blandit quis ex. Fusce ultrices diam et justo blandit, quis consequat nisl euismod. Vestibulum pretium est sem, vitae convallis justo sollicitudin non. Morbi bibendum purus mattis quam condimentum, a scelerisque erat bibendum. Nullam sit amet bibendum lectus."}]}}]}