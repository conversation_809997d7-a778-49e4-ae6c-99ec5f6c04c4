name: Deploy JAF to AWS Elastic Beanstalk Prod

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Elastic Beanstalk environment name'
        required: true
        default: 'Contacomjaf-prod-env'
      version_label:
        description: 'Application version label'
        required: true
        default: 'v1.0.0'

permissions:
  id-token: write
  contents: read

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Grant execute permission for Gradle wrapper
        run: chmod +x ./gradlew

      - name: Build application
        run: ./gradlew clean bootJar -x test

      - name: Generate deployment package
        run: |
          mkdir -p deploy
          cp build/libs/api-0.0.1-SNAPSHOT.jar deploy/application.jar
          cd deploy
          zip -r deploy.zip application.jar

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::418295705357:role/GithubAwsEbs
          aws-region: us-east-1

      - name: Deploy to Elastic Beanstalk
        uses: einaregilsson/beanstalk-deploy@v22
        with:
          aws_access_key: ${{ env.AWS_ACCESS_KEY_ID }}
          aws_secret_key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws_session_token: ${{ env.AWS_SESSION_TOKEN }}
          application_name: contacomjaf
          environment_name: ${{ github.event.inputs.environment }}
          version_label: ${{ github.event.inputs.version_label }}
          region: us-east-1
          deployment_package: deploy/deploy.zip