.PHONY: infra/raise infra/down infra/clean deploy build build-no-tests run clean log test coverage

# Comando para subir a infraestrutura
infra/raise:
	@echo "🚀 Iniciando infraestrutura..."
	docker-compose up -d
	@echo "✅ Infraestrutura iniciada com sucesso!"

# Comando para parar a infraestrutura
infra/down:
	@echo "🛑 Parando infraestrutura..."
	docker-compose down
	@echo "✅ Infraestrutura parada com sucesso!"

# Comando para limpar completamente a infraestrutura
infra/clean:
	@echo "🧹 Limpando infraestrutura..."
	docker-compose down -v
	@echo "✅ Infraestrutura limpa com sucesso!"

# Create a virtual environment using uv
whatsapp-env:
	cd scripts && uv venv

# Install dependencies specified in pyproject.toml
whatsapp-deps: whatsapp-env
	cd scripts && . .venv/bin/activate && uv pip install -r pyproject.toml

# Run the whatsapp.py script within the virtual environment
whatsapp-run: whatsapp-deps
	cd scripts && . .venv/bin/activate && python whatsapp/whatsapp.py

# Alvo principal para build
build:
	./gradlew clean bootJar

# Alvo para build sem rodar testes (mais rápido)
build-no-tests:
	./gradlew clean bootJar -x test

# Alvo para rodar o app localmente
run:
	./gradlew bootRun

# Alvo para limpar os arquivos de build
clean:
	./gradlew clean

# Alvo para seguir os logs do Beanstalk
log:
	@echo "📄 Seguindo logs do Beanstalk..."
	aws logs tail /aws/elasticbeanstalk/Contacomjaf-prod-env/var/log/web.stdout.log --since 2m --follow

# Comando para rodar o script deploy.sh
deploy:
	@echo "🚀 Iniciando o script de deployment..."
	./scripts/deploy.sh
	@echo "✅ Script de deployment concluído com sucesso!"

# Alvo para rodar os testes unitários
test:
	@echo "🧪 Rodando testes unitários..."
	./gradlew test
	@echo "✅ Testes unitários concluídos!"

# Alvo para gerar o relatório de cobertura de testes
coverage:
	@echo "📊 Gerando relatório de cobertura de testes..."
	./gradlew jacocoTestReport
	@echo "✅ Relatório de cobertura gerado em build/jacocoHtml/index.html"
