# Arquitetura do Sistema de Mensageria

## 1. Visão Geral

O sistema de mensageria é projetado para ser um hub central de comunicação, capaz de:
- Receber mensagens de múltiplos provedores (Meta/WhatsApp, Telegram, etc.)
- Processar diferentes tipos de interação (mensagens, status, botões, etc.)
- Aplicar regras de negócio específicas de contabilidade
- Responder ao usuário através do provedor apropriado
- Manter consistência e rastreabilidade das interações

## 2. Princípios de Design

### 2.1. Princípios SOLID
- **Single Responsibility**: Cada componente tem uma única responsabilidade
- **Open/Closed**: Extensível para novos provedores e tipos de mensagem
- **Liskov Substitution**: Implementações de provedores são intercambiáveis
- **Interface Segregation**: Interfaces específicas para cada tipo de operação
- **Dependency Inversion**: Dependências injetadas e baseadas em abstrações

### 2.2. Padrões de Projeto
- **Strategy**: Para diferentes provedores de mensageria
- **Factory**: Para criação de processadores de webhook
- **Adapter**: Para normalização de payloads de diferentes provedores
- **Observer**: Para notificações de eventos de mensagem
- **Command**: Para ações de resposta ao usuário
- **Repository**: Para persistência de mensagens e interações

## 3. Estrutura de Camadas

```
[Provedores de Mensagem] → [API Gateway] → [Controllers] → [Use Cases] → [Domain Services] → [Infrastructure]
```

### 3.1. Camada de Provedores (Providers)
```kotlin
interface MessageProvider {
    val providerType: ProviderType
    fun sendMessage(command: SendMessageCommand)
    fun receiveWebhook(payload: WebhookPayload)
}

enum class ProviderType {
    META,
    TELEGRAM,
    // Futuros provedores...
}
```

### 3.2. Camada de Adaptação (Adapters)
```kotlin
interface MessageAdapter {
    fun toDomainMessage(payload: WebhookPayload): DomainMessage
    fun fromDomainMessage(message: DomainMessage): ProviderMessage
}

// Exemplo de implementação para Meta
class MetaMessageAdapter : MessageAdapter {
    override fun toDomainMessage(payload: MetaWebhookPayload): DomainMessage {
        // Converte payload do Meta para mensagem de domínio
    }
}
```

### 3.3. Camada de Domínio (Domain)
```kotlin
// Entidades de Domínio
data class Message(
    val id: UUID,
    val providerType: ProviderType,
    val providerMessageId: String,
    val type: MessageType,
    val content: MessageContent,
    val sender: Contact,
    val timestamp: Instant,
    val status: MessageStatus
)

// Eventos de Domínio
sealed class MessageEvent {
    data class MessageReceived(val message: Message) : MessageEvent()
    data class MessageStatusUpdated(val messageId: UUID, val status: MessageStatus) : MessageEvent()
    data class ButtonClicked(val messageId: UUID, val buttonId: String) : MessageEvent()
}

// Serviços de Domínio
interface MessageService {
    fun processIncomingMessage(message: Message)
    fun updateMessageStatus(messageId: UUID, status: MessageStatus)
    fun handleButtonClick(messageId: UUID, buttonId: String)
}
```

### 3.4. Camada de Aplicação (Application)
```kotlin
// Use Cases
interface ProcessIncomingMessageUseCase {
    fun execute(message: Message)
}

interface SendMessageUseCase {
    fun execute(command: SendMessageCommand)
}

// Commands
data class SendMessageCommand(
    val providerType: ProviderType,
    val recipientId: String,
    val content: MessageContent,
    val options: MessageOptions
)
```

### 3.5. Camada de Infraestrutura (Infrastructure)
```kotlin
// Repositories
interface MessageRepository {
    fun save(message: Message)
    fun findById(id: UUID): Message?
    fun findByProviderMessageId(providerType: ProviderType, messageId: String): Message?
}

// Event Bus
interface EventBus {
    fun publish(event: MessageEvent)
    fun subscribe(eventType: Class<MessageEvent>, handler: (MessageEvent) -> Unit)
}

// External Services
interface ProviderClient {
    fun sendMessage(message: ProviderMessage)
    fun getMessageStatus(messageId: String): MessageStatus
}
```

## 4. Fluxo de Dados

### 4.1. Recebimento de Mensagem
1. **Webhook Recebido**
   ```
   [Provedor] → [Controller] → [WebhookProcessor] → [MessageAdapter] → [DomainMessage]
   ```

2. **Processamento de Domínio**
   ```
   [DomainMessage] → [MessageService] → [Business Rules] → [Event Publishing]
   ```

3. **Persistência e Notificação**
   ```
   [Event] → [MessageRepository] → [EventBus] → [Subscribers]
   ```

### 4.2. Envio de Mensagem
1. **Comando de Envio**
   ```
   [Use Case] → [MessageService] → [ProviderAdapter] → [ProviderClient]
   ```

2. **Confirmação e Status**
   ```
   [Provider] → [Webhook] → [StatusProcessor] → [MessageService] → [Event Publishing]
   ```

## 5. Decisões Técnicas e Justificativas

### 5.1. Separação de Provedores
**Problema**: Diferentes provedores têm diferentes APIs, formatos e comportamentos.
**Solução**: 
- Interface `MessageProvider` para abstrair provedores
- Adapters específicos para cada provedor
- Normalização para modelo de domínio unificado

**Benefícios**:
- Fácil adição de novos provedores
- Isolamento de complexidade específica de provedor
- Testabilidade melhorada

### 5.2. Event-Driven Architecture
**Problema**: Necessidade de processamento assíncrono e desacoplamento.
**Solução**:
- Eventos de domínio para mudanças de estado
- Event Bus para publicação/assinatura
- Processadores assíncronos para eventos

**Benefícios**:
- Desacoplamento entre componentes
- Escalabilidade horizontal
- Melhor rastreabilidade

### 5.3. CQRS para Mensagens
**Problema**: Diferentes necessidades de leitura/escrita para mensagens.
**Solução**:
- Commands para ações de modificação
- Queries para leitura de estado
- Modelos separados para leitura/escrita

**Benefícios**:
- Otimização de performance
- Escalabilidade independente
- Melhor modelagem de domínio

### 5.4. Persistência Poliglota
**Problema**: Diferentes necessidades de armazenamento.
**Solução**:
- MongoDB para mensagens e interações
- Redis para cache e estado temporário
- PostgreSQL para dados transacionais

**Benefícios**:
- Otimização por caso de uso
- Escalabilidade específica
- Performance melhorada

## 6. Considerações de Implementação

### 6.1. Tratamento de Erros
```kotlin
sealed class MessageError {
    data class ProviderError(val provider: ProviderType, val cause: Throwable) : MessageError()
    data class ValidationError(val field: String, val message: String) : MessageError()
    data class BusinessRuleError(val rule: String, val details: String) : MessageError()
}

// Uso em Use Cases
class SendMessageUseCaseImpl(
    private val messageService: MessageService,
    private val errorHandler: ErrorHandler
) : SendMessageUseCase {
    override fun execute(command: SendMessageCommand) {
        try {
            messageService.sendMessage(command)
        } catch (e: Exception) {
            errorHandler.handle(MessageError.ProviderError(command.providerType, e))
        }
    }
}
```

### 6.2. Logging e Monitoramento
```kotlin
@Aspect
@Component
class MessageLoggingAspect {
    @Around("execution(* br.com.mecontaai.api.accounting.domain.service.MessageService.*(..))")
    fun logMessageOperation(joinPoint: ProceedingJoinPoint): Any {
        val startTime = System.currentTimeMillis()
        try {
            val result = joinPoint.proceed()
            logSuccess(joinPoint, startTime)
            return result
        } catch (e: Exception) {
            logError(joinPoint, e, startTime)
            throw e
        }
    }
}
```

### 6.3. Circuit Breaker para Provedores
```kotlin
@CircuitBreaker(
    name = "messageProvider",
    fallbackMethod = "fallbackSendMessage"
)
fun sendMessage(command: SendMessageCommand) {
    // Implementação
}

fun fallbackSendMessage(command: SendMessageCommand, e: Exception) {
    // Lógica de fallback
}
```

## 7. Exemplos de Uso

### 7.1. Recebimento de Mensagem do Meta
```kotlin
@RestController
@RequestMapping("/api/v1/webhooks")
class MetaWebhookController(
    private val processWebhookUseCase: ProcessWebhookUseCase
) {
    @PostMapping("/meta")
    fun receiveWebhook(@RequestBody payload: String): ResponseEntity<Unit> {
        val command = WebhookCommand(
            providerType = ProviderType.META,
            payload = payload
        )
        processWebhookUseCase.execute(command)
        return ResponseEntity.ok().build()
    }
}
```

### 7.2. Processamento de Regra de Negócio
```kotlin
@Service
class AccountingMessageProcessor(
    private val eventBus: EventBus,
    private val accountingRules: AccountingRules
) {
    @EventListener
    fun handleMessageReceived(event: MessageEvent.MessageReceived) {
        val message = event.message
        if (accountingRules.shouldProcess(message)) {
            val response = accountingRules.processMessage(message)
            eventBus.publish(SendMessageCommand(
                providerType = message.providerType,
                recipientId = message.sender.id,
                content = response
            ))
        }
    }
}
```

## 8. Considerações Futuras

### 8.1. Escalabilidade
- Implementação de sharding por provedor
- Cache distribuído para estado de mensagem
- Processamento em lote para eventos similares

### 8.2. Segurança
- Validação de assinatura de webhook
- Rate limiting por provedor
- Criptografia de dados sensíveis

### 8.3. Monitoramento
- Métricas por provedor
- Alertas de falha
- Tracing distribuído

### 8.4. Manutenibilidade
- Documentação automatizada
- Testes de integração
- CI/CD robusto 